repos:
  - repo: https://github.com/PyCQA/isort
    rev: 5.11.5
    hooks:
      - id: isort
        args: ["--multi-line=7", "--sl", "--profile", "black", "--filter-files"]

  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: "v0.0.272"
    hooks:
      - id: ruff

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: a11d9314b22d8f8c7556443875b731ef05965464
    hooks:
      - id: check-merge-conflict
      - id: check-symlinks
      - id: detect-private-key
        files: (?!.*paddle)^.*$
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-case-conflict
      - id: check-yaml
        exclude: "mkdocs.yml|recipe/meta.yaml"
      - id: pretty-format-json
        args: [--autofix]
      - id: requirements-txt-fixer

  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.0.1
    hooks:
      - id: forbid-crlf
        files: \.md$
      - id: remove-crlf
        files: \.md$
      - id: forbid-tabs
        files: \.md$
      - id: remove-tabs
        files: \.md$

  - repo: local
    hooks:
      - id: clang-format
        name: clang-format
        description: Format files with ClangFormat
        entry: bash .clang_format.hook -i
        language: system
        files: \.(c|cc|cxx|cpp|cu|h|hpp|hxx|cuh|proto)$

exclude: |
  ^jointContribution/
