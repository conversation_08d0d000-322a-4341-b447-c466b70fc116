defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_deepcfd/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval/export/infer
seed: 2023
output_dir: ${hydra:run.dir}
log_freq: 20

# set data file path
DATAX_PATH: ./datasets/dataX.pkl
DATAY_PATH: ./datasets/dataY.pkl
SLIPT_RATIO: 0.7 # slipt dataset to train dataset and test datatset
SAMPLE_SIZE: 981 # the shape of dataX and dataY is [SAMPLE_SIZE, CHANNEL_SIZE, X_SIZE, Y_SIZE]
CHANNEL_SIZE: 3
X_SIZE: 172
Y_SIZE: 79

# model settings
MODEL:
  input_key: "input"
  output_key: "output"
  in_channel: 3
  out_channel: 3
  kernel_size: 5
  filters: [8, 16, 32, 32]
  weight_norm: false
  batch_norm: false

# training settings
TRAIN:
  epochs: 1000
  learning_rate: 0.001
  weight_decay: 0.005
  eval_during_train: true
  eval_freq: 50
  batch_size: 64
  pretrained_model_path: null
  checkpoint_path: null

EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true
  batch_size: 8

INFER:
  pretrained_model_path: "https://paddle-org.bj.bcebos.com/paddlescience/models/deepcfd/deepcfd_pretrained.pdparams"
  export_path: ./inference/deepcfd
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 6000
  gpu_id: 0
  max_batch_size: 100
  num_cpu_threads: 4
  batch_size: 100
