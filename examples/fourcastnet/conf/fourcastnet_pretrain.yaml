defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_fourcastnet_pretrain
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval/export/infer
seed: 1024
output_dir: ${hydra:run.dir}
log_freq: 20

# set training hyper-parameters
IMG_H: 720
IMG_W: 1440
# FourCastNet use 20 atmospheric variable，their index in the dataset is from 0 to 19.
# The variable name is 'u10', 'v10', 't2m', 'sp', 'msl', 't850', 'u1000', 'v1000', 'z000',
# 'u850', 'v850', 'z850',  'u500', 'v500', 'z500', 't500', 'z50', 'r500', 'r850', 'tcwv'.
# You can obtain detailed information about each variable from
# https://cds.climate.copernicus.eu/cdsapp#!/search?text=era5&type=dataset
VARS_CHANNEL: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
USE_SAMPLED_DATA: false

# set train data path
TRAIN_FILE_PATH: ./datasets/era5/train
DATA_MEAN_PATH: ./datasets/era5/stat/global_means.npy
DATA_STD_PATH: ./datasets/era5/stat/global_stds.npy
DATA_TIME_MEAN_PATH: ./datasets/era5/stat/time_means.npy

# set evaluate data path
VALID_FILE_PATH: ./datasets/era5/test

# set inference data path
INFER_FILE_PATH: ./datasets/era5/test/2018-09-08_n32.npy

# model settings
MODEL:
  afno:
    input_keys: ["input"]
    output_keys: ["output"]

# training settings
TRAIN:
  epochs: 150
  save_freq: 20
  eval_during_train: true
  eval_freq: 20
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    learning_rate: 0.0005
    by_epoch: true
  batch_size: 1
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  compute_metric_by_batch: true
  eval_with_no_grad: true
  batch_size: 8


INFER:
  pretrained_model_path: "https://paddle-org.bj.bcebos.com/paddlescience/models/fourcastnet/pretrain.pdparams"
  export_path: ./inference/fourcastnet_pretrain
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  onnx_path: ${INFER.export_path}.onnx
  batch_size: ${TRAIN.batch_size}
  output_keys: ${MODEL.afno.output_keys}
  device: gpu
  engine: native
  precision: fp32
  ir_optim: true
  min_subgraph_size: 5
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 1024
  num_cpu_threads: 10
  num_timestamps: 32
