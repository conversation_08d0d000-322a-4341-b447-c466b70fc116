defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    dir: outputs_drivaernet/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode}
    chdir: false
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  sweep:
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train
seed: 1
output_dir: ${hydra:run.dir}
log_freq: 100

# model settings
MODEL:
  input_keys: ["vertices"]
  output_keys: ["cd_value"]
  weight_keys: ["weight_keys"]
  dropout: 0.4
  emb_dims: 512
  k: 40
  output_channels: 1

# training settings
TRAIN:
  iters_per_epoch: 2776
  num_points: 5000
  epochs: 100
  num_workers: 32
  eval_during_train: True
  train_ids_file: "train_design_ids.txt"
  eval_ids_file: "val_design_ids.txt"
  batch_size: 1
  train_fractions: 1
  scheduler:
    mode: "min"
    patience: 20
    factor: 0.1
    verbose: True

# evaluation settings
EVAL:
  num_points: 5000
  batch_size: 2
  pretrained_model_path: "https://paddle-org.bj.bcebos.com/paddlescience/models/DrivAerNet/CdPrediction_DrivAerNet_r2_100epochs_5k_pretrained.pdparams"
  eval_with_no_grad: True
  ids_file: "test_design_ids.txt"
  num_workers: 8

# optimizer settings
optimizer:
  weight_decay: 0.0001
  lr: 0.001
  optimizer: "adam"

ARGS:
  # dataset settings
  dataset_path: "data/DrivAerNetPlusPlus_Processed_Point_Clouds_100k_paddle"
  aero_coeff: "data/AeroCoefficients_DrivAerNet_FilteredCorrected.csv"
  subset_dir: "data/subset_dir"
