defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: burgers/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 66
output_dir: ${hydra:run.dir}
DATA_PATH: ./data/burgers_2001x2x128x128.mat
case_name: 2D Burgers' equation
num_convlstm: 1
# set working condition
TIME_STEPS: 1001
DT: 0.002
DX: [1.0, 128]
TIME_BATCH_SIZE: 1000

# model settings
MODEL:
  input_channels: 2
  hidden_channels: [8, 32, 128, 128]
  input_kernel_size: [4, 4, 4, 3]
  input_stride: [2, 2, 2, 1]
  input_padding: [1, 1, 1, 1]
  num_layers: [3, 1]
  upscale_factor: 8

# training settings
TRAIN:
  epochs: 24000
  iters_per_epoch: 1
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    step_size: 50
    gamma: 0.99
    learning_rate: 6.0e-4
  save_freq: 50
  eval_with_no_grad: true
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true
  TIME_BATCH_SIZE: 2000
  TIME_STEPS: 2001
