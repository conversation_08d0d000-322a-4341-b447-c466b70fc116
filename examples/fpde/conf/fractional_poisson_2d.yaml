defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_fractional_poisson_2d/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 100

ALPHA: 1.8
NPOINT_INTERIOR: 100
NPOINT_BC: 1
NPOINT_EVAL: 1000

# model settings
MODEL:
  input_keys: ["x", "y"]
  output_keys: ["u"]
  num_layers: 4
  hidden_size: 20
  activation: "tanh"

# training settings
TRAIN:
  epochs: 20000
  iters_per_epoch: 1
  save_freq: 100
  eval_during_train: true
  eval_freq: 1000
  learning_rate: 0.001
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true
  batch_size: 128
