defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_heart/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

EVAL_CSV_PATH: ./data/label.csv
DATA_CSV_PATH: ./data/forward.csv
# general settings
mode: train # running mode: train/eval
seed: 2024
output_dir: ${hydra:run.dir}
log_freq: 200

# set geometry
GEOM_PATH: ./stl/heart.stl
BASE_PATH: ./stl/base.stl
ENDO_PATH: ./stl/endo.stl
EPI_PATH: ./stl/epi.stl

# set working condition
E: 9 # kPa
nu: 0.45
P: 1.064 # kPa

# model settings
MODEL:
  input_keys: ["x", "y", "z"]
  output_keys: ["u", "v", "w"]
  num_layers: 10
  hidden_size: 20
  activation: "silu"
  weight_norm: true

# training settings
TRAIN:
  epochs: 200
  iters_per_epoch: 1000
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    learning_rate: 1.0e-3
    gamma: 0.95
    decay_steps: 3000
    by_epoch: false
  batch_size:
    bc_base: 256
    bc_endo: 2048
    bc_epi: 32
    interior: 8000
  weight:
    bc_base: { "u": 0.2, "v": 0.2, "w": 0.2 }
    bc_endo: { "traction_x": 0.1, "traction_y": 0.1, "traction_z": 0.1 }
    bc_epi: { "traction": 0.2 }
    interior: { "hooke_x": 0.2, "hooke_y": 0.2, "hooke_z": 0.2 }
  save_freq: 20
  eval_freq: 20
  eval_during_train: true
  eval_with_no_grad: true
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true
  batch_size: 1000
  num_vis: 100000
