{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 简介\n", "\n", "本项目来源于共创计划开发性课题CAE领域的基于飞桨+DeepXDE/PaddleScience的复杂结构受力分析。深度学习擅长数据驱动，而工程结构有各种控制方程，PINN（Physics-informed Neural Network）方法利用控制方程加速深度学习神经网络收敛，甚至在无训练数据的情况下实现无监督学习。\n", "\n", "板是工程结构中常见构件，板控制方程存在高阶微分，这个问题的解决可以为后续解决复杂结构问题打下良好基础。从标准教科书中可以获得薄板小挠度理论的基本方程以及相关的边界条件表达式，教科书可参考《钱伟长，叶开沅，弹性力学，科学出版社，1956》。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["薄板小挠度理论的基本方程为：\n", "$$\n", "\\frac{\\partial^4 w}{\\partial x^4}+2 \\frac{\\partial^4 w}{\\partial x^2 \\partial y^2}+\\frac{\\partial^4 w}{\\partial y^4}=\\frac{q}{D}\n", "$$\n", "\n", "其中 $w(x,y)$ 表示薄板的挠度，即薄板在垂直载荷作用下的变形或偏移量，$x,y$ 表示薄板在平面内的坐标，$D$ 为薄板的弯曲刚度，$q$ 是作用在薄板上的面载荷，表示每单位面积上的外部载荷。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在本问题中，矩形薄板 $x$ 方向长 $2m$，$y$ 方向宽 $1m$，板厚 $10mm$，$x$ 方向左右两边处于简支状态（可以转动但不能位移），$y$ 方向上下两边自由（没有任何约束，可以自由移动和转动）。\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["左右两边 $(x=-1 \\mid x=+1)$ 为简支边界条件，因此挠度 $w$ 和弯矩 $M_x$ 都为 $0$ :\n", "\n", "$$\n", "(w)_{x=-1 \\mid x=+1}=0, \\quad\\left(M_x\\right)_{x=-1 \\mid x=+1}=0\n", "$$\n", "\n", "\n", "由于 $M_x=-D\\left(\\frac{\\partial^2 w}{\\partial x^2}+\\mu \\frac{\\partial^2 w}{\\partial y^2}\\right)$， 且 $\\frac{\\partial^2 w}{\\partial y^2}=0$， 所以简支边界条件可化简为：\n", "\n", "$$\n", "(w)_{x=-1 \\mid x=+1}=0, \\quad\\left(\\frac{\\partial^2 w}{\\partial x^2}\\right)_{x=-1 \\mid x=+1}=0\n", "$$\n", "\n", "\n", "上下两边 $(y=-0.5 \\mid y=+0.5)$ 为自由边界条件， 弯矩、扭矩、横向剪切力都为 $0$ :\n", "\n", "$$\n", "\\left(M_y\\right)_{\\mathrm{y}=-0.5 \\mid \\mathrm{y}=+0.5}=0, \\quad\\left(M_{x y}\\right)_{\\mathrm{y}=-0.5 \\mid \\mathrm{y}=+0.5}=0, \\quad\\left(Q_y\\right)_{\\mathrm{y}=-0.5 \\mid \\mathrm{y}=+0.5}=0\n", "$$\n", "\n", "\n", "由于 $M_y=-D\\left(\\frac{\\partial^2 w}{\\partial y^2}+\\mu \\frac{\\partial^2 w}{\\partial x^2}\\right), \\quad M_{x y}=-D(1-\\mu) \\frac{\\partial^2 w}{\\partial x \\partial y}, \\quad Q_y=-D \\frac{\\partial}{\\partial y}\\left(\\frac{\\partial^2 w}{\\partial x^2}+\\frac{\\partial^2 w}{\\partial y^2}\\right)$ ，且扭矩可以变换为等效剪力， 扭矩和横向剪力合并为 $\\left(Q_y+\\frac{\\partial M_{x y}}{\\partial x}\\right)_{\\mathrm{y}=-0.5 \\mid \\mathrm{y}=+0.5}=0$， 所以自由边界条件用挠度表示为\n", "\n", "$$\n", "\\left(\\frac{\\partial^2 w}{\\partial y^2}+\\mu \\frac{\\partial^2 w}{\\partial x^2}\\right)_{y=-0.5 \\mid y=+0.5}=0, \\quad\\left(\\frac{\\partial^3 w}{\\partial y^3}+(2-\\mu) \\frac{\\partial^3 w}{\\partial x^2 \\partial y}\\right)_{y=-0.5 \\mid y=+0.5}=0\n", "$$\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 设置计算域"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from matplotlib import pyplot as plt\n", "\n", "import ppsci\n", "import sympy as sp\n", "import numpy as np\n", "\n", "# 设置薄板计算域长、宽参数\n", "Lx = 2.0 # 薄板x方向长度(m)\n", "Ly = 1.0 # 薄板y方向宽度(m)\n", "\n", "# 设置方程参数\n", "E = 210000.0e6 # 弹性模量(Pa)\n", "mu = 0.28 # 薄板泊松比(无量纲)\n", "h = 0.01 # 薄板厚度(m)\n", "D = E * (h**3) / (12 * (1 - mu**2)) # 薄板弯曲刚度(kN*m^2)\n", "q = 1000.0 # 均布载荷(N/m^2)\n", "\n", "rectangle = ppsci.geometry.Rectangle([-Lx / 2, -Ly / 2], [Lx / 2, Ly / 2]) # 创建薄板几何形状"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 编写方程中的表达式"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/latex": ["$\\displaystyle \\frac{\\partial^{4}}{\\partial x^{4}} w{\\left(x,y \\right)} + \\frac{\\partial^{4}}{\\partial y^{4}} w{\\left(x,y \\right)} + 2 \\frac{\\partial^{4}}{\\partial y^{2}\\partial x^{2}} w{\\left(x,y \\right)} - 0.0526628571428571$"], "text/plain": ["Derivative(w(x, y), (x, 4)) + Derivative(w(x, y), (y, 4)) + 2*Derivative(w(x, y), (x, 2), (y, 2)) - 0.0526628571428571"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用sympy库计算符号公式\n", "x, y = sp.symbols(\"x y\") # 定义符号变量x, y\n", "w = sp.Function(\"w\")(x, y) # 定义函数 w(x,y)\n", "left = w.diff(x, 4) + 2 * w.diff(x, 2).diff(y, 2) + w.diff(y, 4) # 定义薄板弯曲的双调和方程的左侧部分\n", "right = q / D # 方程右侧的载荷项，表示均布载荷 q 除以板的弯曲刚度 D。这是薄板在载荷下的响应。\n", "res = left - right # 定义方程残差\n", "res # 可视化显示方程残差"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 初始化神经网络模型"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["W1117 17:10:41.976225  7764 gpu_resources.cc:119] Please NOTE: device: 0, GPU Compute Capability: 7.0, Driver API Version: 12.0, Runtime API Version: 11.8\n", "W1117 17:10:41.977502  7764 gpu_resources.cc:164] device: 0, cuDNN Version: 8.7.\n"]}, {"data": {"text/plain": ["MLP(\n", "  (linears): LayerList(\n", "    (0): Linear(in_features=2, out_features=50, dtype=float32)\n", "    (1): Linear(in_features=50, out_features=50, dtype=float32)\n", "    (2): Linear(in_features=50, out_features=50, dtype=float32)\n", "    (3): Linear(in_features=50, out_features=50, dtype=float32)\n", "  )\n", "  (acts): LayerList(\n", "    (0): <PERSON><PERSON>()\n", "    (1): <PERSON><PERSON>()\n", "    (2): <PERSON><PERSON>()\n", "    (3): <PERSON><PERSON>()\n", "  )\n", "  (last_fc): Linear(in_features=50, out_features=1, dtype=float32)\n", ")"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["model = ppsci.arch.MLP([\"x\", \"y\"], [\"w\"], 4, 50)\n", "model # 可视化显示模型结构"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 初始化控制方程和边界条件\n", "\n", "接下来讲解如何将开头简介中的控制方程和边界条件转换为深度学习代码。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1 控制方程\n", "\n", "控制方程表示在矩形薄板区域内部，挠度和弯矩所满足的微分方程。因此可以在矩形内部采样足够多的配点(collation points)用于模型训练，如下所示："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["interior_points = rectangle.sample_interior(1000, random='Halton')\n", "px1, py1 = interior_points[\"x\"], interior_points[\"y\"]\n", "plt.scatter(px1, py1, color='green')\n", "plt.title('Interior training points for PDE')\n", "plt.xlabel('X-axis')\n", "plt.ylabel('Y-axis')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来将控制方程转为实际深度学习模型中需要的约束条件：内部约束\n", "> $$\n", "\\frac{\\partial^4 w}{\\partial x^4}+2 \\frac{\\partial^4 w}{\\partial x^2 \\partial y^2}+\\frac{\\partial^4 w}{\\partial y^4}=\\frac{q}{D}\n", "$$"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33m[2024/11/17 17:10:44] ppsci WARNING: <PERSON>gger has already been automatically initialized as `log_file` is set to None by default, information will only be printed to terminal without writting to any file.\u001b[0m\n"]}], "source": ["pde_contraint = ppsci.constraint.InteriorConstraint(\n", "    {\"kirchhoff_res\": res}, # 残差表达式\n", "    {\"kirchhoff_res\": 0.0}, # 残差目标优化值\n", "    rectangle, # 约束区域：薄板矩形\n", "    {\n", "        \"dataset\": \"IterableNamedArrayDataset\",\n", "        \"iters_per_epoch\": 1,\n", "        \"batch_size\": 20000, # 采样两万个配点用于训练\n", "    },\n", "    random=\"Halton\",\n", "    loss=ppsci.loss.MSELoss(), # 使用均方根误差损失函数\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 简支边界条件\n", "\n", "接下来讲解如何将开头简介中的左右简支边界条件转换为深度学习代码。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["同样先预览一下简支边界条件所对应的在矩形左右边界上的训练点"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lr_buondary_points = rectangle.sample_boundary(100, random='Halton', criteria=lambda x, y: np.isclose(x, -Lx / 2) | np.isclose(x, Lx / 2))\n", "px2, py2 = lr_buondary_points[\"x\"], lr_buondary_points[\"y\"]\n", "plt.scatter(px2, py2, color='blue')\n", "plt.title('boundary points for simply supported boundary condition')\n", "plt.xlabel('X-axis')\n", "plt.ylabel('Y-axis')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来将控制方程转为实际深度学习模型中需要的约束条件：(简支)边界约束\n", "> $$\n", "(w)_{x=-1 \\mid x=+1}=0, \\quad\\left(\\frac{\\partial^2 w}{\\partial x^2}\\right)_{x=-1 \\mid x=+1}=0\n", "$$"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["constraint_left_right = ppsci.constraint.BoundaryConstraint(\n", "    {\"w\": w, \"ddw_dxx\": w.diff(x, 2)}, # 挠度和 x 轴弯矩表达式\n", "    {\"w\": 0, \"ddw_dxx\": 0}, # 挠度和 x 轴弯矩目标值均为0\n", "    rectangle,\n", "    {\n", "        \"dataset\": \"IterableNamedArrayDataset\",\n", "        \"iters_per_epoch\": 1,\n", "        \"batch_size\": 10000, # 采样一万个点用于训练\n", "    },\n", "    criteria=lambda x, y: np.isclose(x, -Lx / 2) | np.isclose(x, Lx / 2), # 采样点在左右两侧边界上\n", "    loss=ppsci.loss.MSELoss(), # 使用均方根误差损失函数\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 自由边界条件\n", "\n", "接下来讲解如何将开头简介中的左右简支边界条件转换为深度学习代码。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["同样先预览一下简支边界条件所对应的在矩形上下边界上的训练点"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ud_buondary_points = rectangle.sample_boundary(100, random='Halton', criteria=lambda x, y: np.isclose(y, -Ly / 2) | np.isclose(y, Ly / 2))\n", "px3, py3 = ud_buondary_points[\"x\"], ud_buondary_points[\"y\"]\n", "plt.scatter(px3, py3, color='red')\n", "plt.title('boundary points for free boundary condition')\n", "plt.xlabel('X-axis')\n", "plt.ylabel('Y-axis')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["接下来将自由边界条件转为实际深度学习模型中需要的约束条件：(自由)边界约束\n", "> $$\n", "\\left(\\frac{\\partial^2 w}{\\partial y^2}+\\mu \\frac{\\partial^2 w}{\\partial x^2}\\right)_{y=-0.5 \\mid y=+0.5}=0, \\quad\\left(\\frac{\\partial^3 w}{\\partial y^3}+(2-\\mu) \\frac{\\partial^3 w}{\\partial x^2 \\partial y}\\right)_{y=-0.5 \\mid y=+0.5}=0\n", "$$"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["constraint_up_down = ppsci.constraint.BoundaryConstraint(\n", "    {\n", "        \"item1\": w.diff(y, 2) + mu * w.diff(x, 2), # 上下边界上需要满足的条件\n", "        \"item2\": w.diff(y, 3) + (2 - mu) * w.diff(x, 2).diff(y), # 上下边界上需要满足的条件\n", "    },\n", "    {\"item1\": 0.0, \"item2\": 0.0}, # 上下边界上需要满足的条件\n", "    rectangle,\n", "    {\n", "        \"dataset\": \"IterableNamedArrayDataset\",\n", "        \"iters_per_epoch\": 1,\n", "        \"batch_size\": 10000, # 采样一万个点用于训练\n", "    },\n", "    criteria=lambda x, y: np.isclose(y, -Ly / 2) | np.isclose(y, Ly / 2), # 采样点在左右两侧边界上\n", "    loss=ppsci.loss.MSELoss(), # 使用均方根误差损失函数\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["综上所述，控制方程、简支边界条件、自由边界条件所用的训练数据点预览如下："]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(px1, py1, color='green', label='collation points')\n", "plt.scatter(px2, py2, color='blue', label='simply supported boundary condition points')\n", "plt.scatter(px3, py3, color='red', label='free boundary condition points')\n", "plt.title('Summary of points')\n", "plt.xlabel('X-axis')\n", "plt.ylabel('Y-axis')\n", "plt.legend(bbox_to_anchor=(1, 0), loc=3, borderaxespad=0)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 初始化深度学习求解器并开始求解"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2024/11/17 17:10:45] ppsci INFO: Using paddlepaddle 3.0.0 on device Place(gpu:0)\u001b[0m\n", "\u001b[36m[2024/11/17 17:10:45] ppsci MESSAGE: Set to_static=False for computational optimization.\u001b[0m\n", "[2024/11/17 17:11:51] ppsci INFO: [Train][Epoch   1/400][Iter 1/1] lr: 1.00000, loss: 0.00000, pde_contraint: 0.00000, constraint_left_right: 0.00000, constraint_up_down: 0.00000, batch_cost: 66.07724s, reader_cost: 0.00001s, ips: 605.35, eta: 7:19:24\u001b[0m\n", "\u001b[36m[2024/11/17 17:11:51] ppsci MESSAGE: Finish saving checkpoint to: ./output_kirchhoff/checkpoints/latest(latest checkpoint will be saved every epoch as expected, but this log will be printed only once for tidy logging)\u001b[0m\n", "[2024/11/17 17:12:13] ppsci INFO: [Train][Epoch 100/400][Iter 1/1] lr: 1.00000, loss: 0.00000, pde_contraint: 0.00000, constraint_left_right: 0.00000, constraint_up_down: 0.00000, batch_cost: 0.16517s, reader_cost: 0.00002s, ips: 242176.51, eta: 0:00:49\u001b[0m\n", "[2024/11/17 17:12:32] ppsci INFO: [Train][Epoch 200/400][Iter 1/1] lr: 1.00000, loss: 0.00000, pde_contraint: 0.00000, constraint_left_right: 0.00000, constraint_up_down: 0.00000, batch_cost: 0.14143s, reader_cost: 0.00002s, ips: 282823.83, eta: 0:00:28\u001b[0m\n", "[2024/11/17 17:12:50] ppsci INFO: [Train][Epoch 300/400][Iter 1/1] lr: 1.00000, loss: 0.00000, pde_contraint: 0.00000, constraint_left_right: 0.00000, constraint_up_down: 0.00000, batch_cost: 0.14079s, reader_cost: 0.00002s, ips: 284106.49, eta: 0:00:14\u001b[0m\n", "[2024/11/17 17:13:09] ppsci INFO: [Train][Epoch 400/400][Iter 1/1] lr: 1.00000, loss: 0.00000, pde_contraint: 0.00000, constraint_left_right: 0.00000, constraint_up_down: 0.00000, batch_cost: 0.14086s, reader_cost: 0.00002s, ips: 283973.91, eta: 0:00:00\u001b[0m\n"]}], "source": ["# set optimizer\n", "opt = ppsci.optimizer.LBFGS(max_iter=1000)(model)\n", "solver = ppsci.solver.Solver(\n", "    model,\n", "    {\n", "        \"pde_contraint\": pde_contraint,\n", "        \"constraint_left_right\": constraint_left_right,\n", "        \"constraint_up_down\": constraint_up_down,\n", "    },\n", "    output_dir=\"./output_kirchhoff\",\n", "    optimizer=opt,\n", "    epochs=400,\n", "    iters_per_epoch=1,\n", "    log_freq=100,\n", "    # pretrained_model_path=\"./output_kirchhoff/checkpoints/latest\"\n", ")\n", "solver.train()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 结果可视化"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["num_cords = 10201\n", "(10201,) (10201,) (10201, 1)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# plot result\n", "num_cord0 = 101\n", "num_cord1 = 101\n", "num_cords = num_cord0 * num_cord1\n", "print(f\"num_cords = {num_cords}\")\n", "x, y = np.meshgrid(\n", "    np.linspace(\n", "        start=-Lx / 2, stop=Lx / 2, num=num_cord0, endpoint=True, dtype=\"float32\"\n", "    ),\n", "    np.linspace(\n", "        start=-Ly / 2, stop=Ly / 2, num=num_cord1, endpoint=True, dtype=\"float32\"\n", "    ),\n", ")\n", "x = x.ravel()\n", "y = y.ravel()\n", "# predict solution of w(x, y) on the 2D grid\n", "w_pred = solver.predict({\"x\": x[:, None], \"y\": y[:, None]}, return_numpy=True)[\"w\"]\n", "fig = plt.figure(100, figsize=(5, 4))\n", "y_min = w_pred.min(axis=(0,))[0]\n", "y_max = w_pred.max(axis=(0,))[0]\n", "ax1 = plt.subplot(1, 1, 1)\n", "plt.tricontourf(x, y, w_pred[:, 0], levels=30, cmap=\"rainbow\")\n", "print(x.shape, y.shape, w_pred.shape)\n", "cb1 = plt.colorbar()\n", "plt.axis(\"equal\")\n", "plt.xlabel(\"$x (m)$\")\n", "plt.ylabel(\"$y (m)$\")\n", "plt.title(f\"w-field: [{y_min:.6f}, {y_max:.6f}]\", fontsize=9.5)\n", "plt.show()\n", "# plt.savefig(\"./result.jpg\")\n", "# print(\"saved matplotlib to: ./result.jpg\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7.有限元计算结果比较\n", "\n", "通过比较，可以发现PINN方法计算结果和有限元方法计算结果基本一致。这里有限元计算所用薄板的几何参数、材料参数、载荷及边界条件和第2部分所描述薄板是一样的，有限元计算所用软件为SIPESC2022。PINN方法计算的最大挠度是12.2mm，有限元方法计算的最大挠度是12.2mm，两者的计算结果相差很小。\n", "<img src=\"./FEM.png\" alt=\"FEM_result\" width=\"60%\">\n"]}], "metadata": {"kernelspec": {"display_name": "conda_py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}