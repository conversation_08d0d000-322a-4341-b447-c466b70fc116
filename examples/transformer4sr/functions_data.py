# Copyright (c) 2024 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Reference: https://github.com/omron-sinicx/transformer4sr
"""

import json
import os
from typing import Tuple

import numpy as np
import sympy
from tqdm import tqdm
from utils import from_sympy_to_seq


class DataFuncs:
    """Functions of dataset generated by this example.

    Args:
        data_path (str): Path of dataset.
        vocab_library (Tuple): Library of vocabulary, like ("add","mul","sin","C","x1","x2").
        seq_length_max (int): Max length of sequence.
        ratio (Tuple): The ratio of dividing training, validation, and test datasets.
        shuffle (bool, optional): Whether to shuffle. Defaults to True.
    """

    def __init__(
        self,
        data_path: str,
        vocab_library: Tuple,
        seq_length_max: int,
        ratio: Tuple,
        shuffle: bool = True,
    ) -> None:
        self.data_path = data_path
        self.vocab_library = vocab_library
        self.seq_length_max = seq_length_max
        self.ratio = ratio
        self.load_data()
        self.get_vocab_target()
        self.split_dataset_idx(shuffle)

        for mode in ["train", "val", "test"]:
            self.init_data(mode)

    def init_data(self, mode="test"):
        idx = getattr(self, f"idx_{mode}")
        setattr(self, f"values_{mode}", self.data_values[idx])
        setattr(self, f"targets_{mode}", self.data_targets[idx])

    def load_data(self):
        gt_path = os.path.join(self.data_path, "ground_truth")
        gt_files = sorted(os.listdir(gt_path))
        value_path = os.path.join(self.data_path, "values")
        value_files = sorted(os.listdir(value_path))

        data_values = []
        data_tokens = []
        for i in tqdm(range(len(gt_files)), desc="Loading data"):
            try:
                tokens = self.get_token_from_file(os.path.join(gt_path, gt_files[i]))
                assert len(tokens) <= self.seq_length_max
                data_tokens.append(tokens)
                data_values.append(np.load(os.path.join(value_path, value_files[i])))
            except Exception:
                continue
        data_values = np.expand_dims(np.array(data_values), axis=-1)
        self.data_tokens = data_tokens
        self.data_values = data_values

    def get_token_from_file(self, file_path):
        with open(file_path) as f:
            lines = []
            for token in f.readlines():
                assert token[-1] == "\n"
                if token[0] == "C":
                    lines.append("C")
                else:
                    lines.append(token[:-1])
        return lines

    def get_vocab_target(self):
        data_targets = []
        for tokens in self.data_tokens:
            sample_target = [1]
            for token in tokens:
                sample_target.append(self.vocab_library.index(token) + 2)
            sample_target.extend([0] * (self.seq_length_max + 1 - len(sample_target)))
            data_targets.append(sample_target)
        self.data_targets = np.array(data_targets)

    def split_dataset_idx(self, shuffle=True):
        num_total = self.data_values.shape[0]
        idx = np.arange(num_total)
        if shuffle:
            np.random.shuffle(idx)
        num_train, num_val, _ = (
            int(num_total * self.ratio[0]),
            int(num_total * self.ratio[1]),
            int(num_total * self.ratio[2]),
        )
        self.idx_train = idx[:num_train]
        self.idx_val = idx[num_train : num_train + num_val]
        self.idx_test = idx[num_train + num_val :]


class SRSDDataFuncs:
    """Functions of SRSD dataset.

    Args:
        data_path_lst (Tuple): Paths of srsd datasets.
        sampling_times (int): Sampling times.
        response_variable (Tuple): Response variable, like ("y","x1","x2").
        vocab_library (Tuple): Library of vocabulary, like ("add","mul","sin","C","x1","x2").
        seq_length_max (int): Max length of sequence.
        shuffle (bool, optional): Whether to obtain data randomly. If set to true, pseudo-random seeds will be ignored. Defaults to True.
    """

    def __init__(
        self,
        data_path_lst: Tuple,
        sampling_times: int,
        response_variable: Tuple,
        vocab_library: Tuple,
        seq_length_max: int,
        shuffle: bool = True,
    ) -> None:
        self.data_path_lst = data_path_lst
        self.st = sampling_times
        self.rvar = response_variable
        self.vlab = vocab_library
        self.seq_length_max = seq_length_max
        self.C = sympy.symbols("C", real=True, positive=True)
        self.shuffle = shuffle

        for mode in ["train", "val", "test"]:
            self.init_data(mode)

    def init_data(self, mode="test"):
        mode_lst = [[], [], []]
        keys_lst = []
        for data_path in self.data_path_lst:
            self.load_supp_info(data_path)
            tokens, values, targets = self.load_data(data_path, mode)
            mode_lst[0].extend(tokens)
            mode_lst[1].append(values)
            mode_lst[2].append(targets)
            keys_lst.extend(self.keys)
        setattr(self, f"tokens_{mode}", mode_lst[0])
        setattr(self, f"values_{mode}", np.concatenate(mode_lst[1], axis=0))
        setattr(self, f"targets_{mode}", np.concatenate(mode_lst[2], axis=0))
        setattr(self, f"keys_{mode}", keys_lst)

    def load_supp_info(self, data_path):
        supp_info_path = os.path.join(data_path, "supp_info.json")
        with open(supp_info_path, "rb") as f:
            feynman_dict = json.load(f)
        self.keys = list(feynman_dict.keys())
        self.keys.sort()
        self.feynman_dict = feynman_dict

    def load_data(self, data_path, mode="test"):
        data_values = []
        data_tokens = []
        data_targets = []
        remaining_keys = []
        for i in tqdm(
            range(len(self.keys)),
            desc=f"Loading {mode} data from {data_path}",
            leave=False,
        ):
            key = self.keys[i]
            try:
                data_path_txt = os.path.join(data_path, mode, f"{key}.txt")
                values = self.load_srsd_dataset_input(data_path_txt, key)
                assert not np.isnan(values).any(), "nan in values. deprecate it."
                sample_target_tokens, sample_target = self.load_srsd_dataset_target(key)
                assert (
                    len(sample_target) <= self.seq_length_max
                ), f"{key}'s eq is too complex. deprecate it."

                data_values.append(values)
                data_tokens.append(sample_target_tokens)
                sample_target.extend(
                    [0] * (self.seq_length_max + 1 - len(sample_target))
                )
                data_targets.append(np.array(sample_target))
            except Exception:
                continue
            remaining_keys.append(key)
        self.keys = remaining_keys

        return (
            data_tokens,
            np.expand_dims(np.array(data_values), axis=-1),
            np.array(data_targets),
        )

    def trans_data(self, data, signs=1):
        return np.power(10.0, np.log10(data) - np.mean(np.log10(data))) * signs

    def load_srsd_dataset_input(self, data_path, key):
        # Load SRSD dataset
        data = np.genfromtxt(data_path)

        # Filter valid expressions with respect to the ST (positive inputs)
        mask = np.all(data[:, :-1] > 0.0, axis=1)  # y is in the last column here
        valid_data = data[mask]
        N, Vars = valid_data.shape[0], valid_data.shape[1]
        assert (
            N >= self.st
        ), f"sample quantity {N} of dataset should not be samaller than sampling_times {self.st}."

        # Create normalized dataset (input of ST)
        if self.shuffle:
            # temporarily avoid non-randomness caused by setting random seed
            rng = np.random.default_rng()
            idx_rows = rng.choice(N, self.st, replace=False)
        else:
            idx_rows = np.random.choice(N, self.st, replace=False)
        data_values = np.zeros((self.st, len(self.rvar)))
        for k in range(Vars - 1):  # y will be done separately at the end
            data_values[:, k + 1] = (
                valid_data[idx_rows, k]
                if self.feynman_dict[key]["si-derived_units"][k + 1] == "$rad$"
                else self.trans_data(valid_data[idx_rows, k])
            )
        if data_values.mean() > 1.0e30:
            print("data_values", data_values.mean())

        signs = np.where(
            valid_data[idx_rows, -1] < 0.0, -1.0, 1.0
        )  # maybe some negative values for y
        data = np.abs(valid_data[idx_rows, -1])
        data_values[:, 0] = self.trans_data(data, signs)
        assert (
            np.abs(data_values).mean() < 1.0e10
        ), "value of data is bigger than 1.0e10.  deprecate it."
        return data_values

    def load_srsd_dataset_target(self, key):
        expr_sympy = (
            sympy.sympify(self.feynman_dict[key]["sympy_eq_srepr"]) * self.C
        )  # shifted due to rescaling
        expr_sympy = expr_sympy.evalf()
        expr_srepr = sympy.srepr(expr_sympy)
        for i in range(9, 0, -1):  # i = 9, 8, ..., 2, 1
            expr_srepr = expr_srepr.replace(
                f"Symbol('x{i-1}', real=True)", f"Symbol('x{i}', real=True)"
            )
        expr_sympy = sympy.sympify(expr_srepr)

        target_seq_tokens = from_sympy_to_seq(expr_sympy)
        target_seq = []
        for token in target_seq_tokens:
            target_seq.append(self.vlab.index(token) + 2)
        return target_seq_tokens, target_seq
