defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: output_ldc2d_unsteady_Re10/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20

# set working condition
NU: 0.01
RHO: 1.0
NTIME_ALL: 16

# model settings
MODEL:
  input_keys: ["t", "x", "y"]
  output_keys: ["u", "v", "p"]
  num_layers: 9
  hidden_size: 50
  activation: "tanh"

# training settings
TRAIN:
  epochs: 20000
  iters_per_epoch: 1
  eval_during_train: true
  eval_freq: 200
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    learning_rate: 0.001
  weight:
    pde: {"continuity": 0.0001,"momentum_x": 0.0001,"momentum_y": 0.0001}
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  batch_size:
    residual_validator: 8192

# inference settings
INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/ldc2d_unsteady_Re10/ldc2d_unsteady_Re10_pretrained.pdparams
  export_path: ./inference/ldc2d_unsteady_Re10
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  onnx_path: ${INFER.export_path}.onnx
  device: gpu
  engine: native
  precision: fp32
  ir_optim: true
  min_subgraph_size: 5
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 8192
  num_cpu_threads: 10
  batch_size: 8192
