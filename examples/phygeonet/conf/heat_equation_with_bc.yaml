defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_phygeonet/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
log_freq: 50
seed: 66
data_dir: ./data/heat_equation_bc.npz
test_data_dir: ./data/heat_equation_bc_test.npz
output_dir: ${hydra:run.dir}
r: 0.5
R: 1
dtheta: 0
epochs: 2000
# model settings
MODEL:
  input_keys: ["coords"]
  output_keys: ["output_v"]
  hidden_size: [16, 32, 16]
  h: 0.01
  ny: 276
  nx: 49
  nvar_in: 1
  nvar_out: 1
  pad_singleside: 1

# training settings
TRAIN:
  learning_rate: 1e-3
  batch_size: 1

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true

# inference settings
INFER:
  pretrained_model_path: "https://paddle-org.bj.bcebos.com/paddlescience/models/PhyGeoNet/heat_equation_bc_pretrain.pdparams"
  export_path: ./inference/heat_equation_bc
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  onnx_path: ${INFER.export_path}.onnx
  device: gpu
  engine: native
  precision: fp32
  ir_optim: true
  min_subgraph_size: 5
  gpu_mem: 20
  gpu_id: 0
  max_batch_size: 256
  num_cpu_threads: 10
  batch_size: 256
