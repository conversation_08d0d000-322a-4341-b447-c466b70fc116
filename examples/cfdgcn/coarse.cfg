%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%                                                                              %
% SU2 configuration file                                                       %
% Case description: Transonic inviscid flow around a NACA0012 airfoil          %
% Author: <PERSON>. Economon                                                   %
% Institution: Stanford University                                             %
% Date: 2014.06.11                                                             %
% File Version 6.2.0 "Falcon"                                                  %
%                                                                              %
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% ------------- DIRECT, ADJOINT, AND LINEARIZED PROBLEM DEFINITION ------------%
%
% Physical governing equations (EULER, NAVIER_STOKES,
%                               WAVE_EQUATION, HEAT_EQUATION, FEM_ELASTICITY,
%                               POISSON_EQUATION)
PHYSICAL_PROBLEM= EULER
%
% Mathematical problem (DIRECT, CONTINUOUS_ADJOINT)
MATH_PROBLEM= DIRECT
%
% Restart solution (NO, YES)
RESTART_SOL= NO

% ----------- COMPRESSIBLE AND INCOMPRESSIBLE FREE-STREAM DEFINITION ----------%
%
% Mach number (non-dimensional, based on the free-stream values)
MACH_NUMBER= 0.8
%
% Angle of attack (degrees)
AOA= 1.25
%
% Free-stream pressure (101325.0 N/m^2 by default, only Euler flows)  
FREESTREAM_PRESSURE= 101325.0
%
% Free-stream temperature (273.15 K by default)
FREESTREAM_TEMPERATURE= 273.15

% -------------- COMPRESSIBLE AND INCOMPRESSIBLE FLUID CONSTANTS --------------%
%
% Ratio of specific heats (1.4 (air), only for compressible flows)
GAMMA_VALUE= 1.4
%
% Specific gas constant (287.87 J/kg*K (air), only for compressible flows)
GAS_CONSTANT= 287.87

% ---------------------- REFERENCE VALUE DEFINITION ---------------------------%
%
% Reference origin for moment computation
REF_ORIGIN_MOMENT_X = 0.25
REF_ORIGIN_MOMENT_Y = 0.00
REF_ORIGIN_MOMENT_Z = 0.00
%
% Reference length for pitching, rolling, and yawing non-dimensional moment
REF_LENGTH= 1.0
%
% Reference area for force coefficients (0 implies automatic calculation)
REF_AREA= 1.0
%
% Flow non-dimensionalization (DIMENSIONAL, FREESTREAM_PRESS_EQ_ONE,
%                              FREESTREAM_VEL_EQ_MACH, FREESTREAM_VEL_EQ_ONE)
REF_DIMENSIONALIZATION= DIMENSIONAL

% ----------------------- BOUNDARY CONDITION DEFINITION -----------------------%
%
% Marker of the Euler boundary (NONE = no marker)
MARKER_EULER= ( airfoil )
%
% Marker of the far field (NONE = no marker)
MARKER_FAR= ( farfield )

% ------------------------ SURFACES IDENTIFICATION ----------------------------%
%
% Marker(s) of the surface in the surface flow solution file
MARKER_PLOTTING = ( airfoil )
%
% Marker(s) of the surface where the non-dimensional coefficients are evaluated.
MARKER_MONITORING = ( airfoil )
%
% Marker(s) of the surface where obj. func. (design problem) will be evaluated
MARKER_DESIGNING = ( airfoil )

% ------------- COMMON PARAMETERS TO DEFINE THE NUMERICAL METHOD --------------%
%
% Numerical method for spatial gradients (GREEN_GAUSS, WEIGHTED_LEAST_SQUARES)
NUM_METHOD_GRAD= WEIGHTED_LEAST_SQUARES
%
% Objective function in optimization problem (DRAG, LIFT, SIDEFORCE, MOMENT_X,
%                                             MOMENT_Y, MOMENT_Z, EFFICIENCY,
%                                             EQUIVALENT_AREA, NEARFIELD_PRESSURE,
%                                             FORCE_X, FORCE_Y, FORCE_Z, THRUST,
%                                             TORQUE, FREE_SURFACE, TOTAL_HEATFLUX,
%                                             MAXIMUM_HEATFLUX, INVERSE_DESIGN_PRESSURE,
%                                             INVERSE_DESIGN_HEATFLUX)
% OBJECTIVE_FUNCTION= DRAG
%
% Courant-Friedrichs-Lewy condition of the finest grid
%CFL_NUMBER= 4.0
CFL_NUMBER= 1.0
%CFL_NUMBER=0.1
%
% Number of total iterations
EXT_ITER=200
ITER= 200

% ------------------------ LINEAR SOLVER DEFINITION ---------------------------%
%
% Linear solver for implicit formulations (BCGSTAB, FGMRES)
LINEAR_SOLVER= FGMRES
%
% Preconditioner of the Krylov linear solver (JACOBI, LINELET, LU_SGS)
LINEAR_SOLVER_PREC= LU_SGS
%
% Minimum error of the linear solver for implicit formulations
LINEAR_SOLVER_ERROR= 1E-6
%
% Max number of iterations of the linear solver for the implicit formulation
LINEAR_SOLVER_ITER= 5

% -------------------------- MULTIGRID PARAMETERS -----------------------------%
%
% Multi-Grid Levels (0 = no multi-grid)
MGLEVEL= 2
%
% Multi-grid cycle (V_CYCLE, W_CYCLE, FULLMG_CYCLE)
MGCYCLE= W_CYCLE
%
% Multi-Grid PreSmoothing Level
MG_PRE_SMOOTH= ( 1, 2, 3, 3 )
%
% Multi-Grid PostSmoothing Level
MG_POST_SMOOTH= ( 0, 0, 0, 0 )
%
% Jacobi implicit smoothing of the correction
MG_CORRECTION_SMOOTH= ( 0, 0, 0, 0 )
%
% Damping factor for the residual restriction
MG_DAMP_RESTRICTION= 1.0
%
% Damping factor for the correction prolongation
MG_DAMP_PROLONGATION= 1.0

% -------------------- FLOW NUMERICAL METHOD DEFINITION -----------------------%
%
% Convective numerical method (JST, LAX-FRIEDRICH, CUSP, ROE, AUSM, HLLC,
%                              TURKEL_PREC, MSW)
CONV_NUM_METHOD_FLOW= JST
%
% Monotonic Upwind Scheme for Conservation Laws (TVD) in the flow equations.
%           Required for 2nd order upwind schemes (NO, YES)
MUSCL_FLOW= YES
%
% Slope limiter (NONE, VENKATAKRISHNAN, VENKATAKRISHNAN_WANG,
%                BARTH_JESPERSEN, VAN_ALBADA_EDGE)
SLOPE_LIMITER_FLOW= VENKATAKRISHNAN
%
% 2nd and 4th order artificial dissipation coefficients
JST_SENSOR_COEFF= ( 0.5, 0.02 )
%
% Time discretization (RUNGE-KUTTA_EXPLICIT, EULER_IMPLICIT, EULER_EXPLICIT)
TIME_DISCRE_FLOW= EULER_IMPLICIT

% ---------------- ADJOINT-FLOW NUMERICAL METHOD DEFINITION -------------------%
%
% Convective numerical method (JST, LAX-FRIEDRICH, ROE)
CONV_NUM_METHOD_ADJFLOW= JST
%
% Monotonic Upwind Scheme for Conservation Laws (TVD) in the adjoint flow equations.
%           Required for 2nd order upwind schemes (NO, YES)
MUSCL_ADJFLOW= YES
%
% Slope limiter (NONE, VENKATAKRISHNAN, BARTH_JESPERSEN, VAN_ALBADA_EDGE,
%                SHARP_EDGES, WALL_DISTANCE)
SLOPE_LIMITER_ADJFLOW= NONE
%
% Reduction factor of the CFL coefficient in the adjoint problem
CFL_REDUCTION_ADJFLOW= 0.5
%
% Time discretization (RUNGE-KUTTA_EXPLICIT, EULER_IMPLICIT)
TIME_DISCRE_ADJFLOW= EULER_IMPLICIT

% ----------------------- DESIGN VARIABLE PARAMETERS --------------------------%
%
% Kind of deformation (NO_DEFORMATION, TRANSLATION, ROTATION, SCALE,
%                      FFD_SETTING, FFD_NACELLE
%                      FFD_CONTROL_POINT, FFD_CAMBER, FFD_THICKNESS, FFD_TWIST
%                      FFD_CONTROL_POINT_2D, FFD_CAMBER_2D, FFD_THICKNESS_2D, FFD_TWIST_2D,
%                      HICKS_HENNE, SURFACE_BUMP)
DV_KIND= HICKS_HENNE
%
% Marker of the surface in which we are going apply the shape deformation
DV_MARKER= ( airfoil )
%
% Parameters of the shape deformation
% - NO_DEFORMATION ( 1.0 )
% - TRANSLATION ( x_Disp, y_Disp, z_Disp ), as a unit vector
% - ROTATION ( x_Orig, y_Orig, z_Orig, x_End, y_End, z_End )
% - SCALE ( 1.0 )
% - ANGLE_OF_ATTACK ( 1.0 )
% - FFD_SETTING ( 1.0 )
% - FFD_CONTROL_POINT ( FFD_BoxTag, i_Ind, j_Ind, k_Ind, x_Disp, y_Disp, z_Disp )
% - FFD_NACELLE ( FFD_BoxTag, rho_Ind, theta_Ind, phi_Ind, rho_Disp, phi_Disp )
% - FFD_GULL ( FFD_BoxTag, j_Ind )
% - FFD_ANGLE_OF_ATTACK ( FFD_BoxTag, 1.0 )
% - FFD_CAMBER ( FFD_BoxTag, i_Ind, j_Ind )
% - FFD_THICKNESS ( FFD_BoxTag, i_Ind, j_Ind )
% - FFD_TWIST ( FFD_BoxTag, j_Ind, x_Orig, y_Orig, z_Orig, x_End, y_End, z_End )
% - FFD_CONTROL_POINT_2D ( FFD_BoxTag, i_Ind, j_Ind, x_Disp, y_Disp )
% - FFD_CAMBER_2D ( FFD_BoxTag, i_Ind )
% - FFD_THICKNESS_2D ( FFD_BoxTag, i_Ind )
% - FFD_TWIST_2D ( FFD_BoxTag, x_Orig, y_Orig )
% - HICKS_HENNE ( Lower Surface (0)/Upper Surface (1)/Only one Surface (2), x_Loc )
% - SURFACE_BUMP ( x_Start, x_End, x_Loc )
DV_PARAM= ( 1, 0.5 )
%
% Value of the shape deformation
DV_VALUE= 0.01

% ------------------------ GRID DEFORMATION PARAMETERS ------------------------%
%
% Number of smoothing iterations for FEA mesh deformation
DEFORM_LINEAR_ITER= 500
%
% Number of nonlinear deformation iterations (surface deformation increments)
DEFORM_NONLINEAR_ITER= 1
%
% Minimum residual criteria for the linear solver convergence of grid deformation
DEFORM_LINEAR_SOLVER_ERROR= 1E-14
%
% Print the residuals during mesh deformation to the console (YES, NO)
DEFORM_CONSOLE_OUTPUT= YES
%
% Type of element stiffness imposed for FEA mesh deformation (INVERSE_VOLUME, 
%                                          WALL_DISTANCE, CONSTANT_STIFFNESS)
DEFORM_STIFFNESS_TYPE= INVERSE_VOLUME
%
% Visualize the surface deformation (NO, YES)
VISUALIZE_SURFACE_DEF= NO
%
% Visualize the volume deformation (NO, YES)
VISUALIZE_VOLUME_DEF= NO

% --------------------------- CONVERGENCE PARAMETERS --------------------------%
% Convergence criteria (CAUCHY, RESIDUAL)
%
CONV_CRITERIA= RESIDUAL
%
% Residual reduction (order of magnitude with respect to the initial value)
RESIDUAL_REDUCTION= 6
%
% Min value of the residual (log10 of the residual)
RESIDUAL_MINVAL= -8
%
% Start Cauchy criteria at iteration number
STARTCONV_ITER= 10
%
% Number of elements to apply the criteria
CAUCHY_ELEMS= 100
%
% Epsilon to control the series convergence
CAUCHY_EPS= 1E-6
%
% Function to apply the criteria (LIFT, DRAG, SENS_GEOMETRY, SENS_MACH,
%                                 DELTA_LIFT, DELTA_DRAG)
CAUCHY_FUNC_FLOW= DRAG

% ------------------------- INPUT/OUTPUT INFORMATION --------------------------%
% Mesh input file
%MESH_FILENAME=mesh_NACA0012_xcoarse.su2
MESH_FILENAME=passed_as_flag_to_train.py.su2
%
% Mesh input file format (SU2, CGNS, NETCDF_ASCII)
MESH_FORMAT= SU2
%
% Mesh output file
MESH_OUT_FILENAME= mesh_out.su2
%
% Restart flow input file
SOLUTION_FLOW_FILENAME= solution_flow.dat
%
% Restart adjoint input file
SOLUTION_ADJ_FILENAME= solution_adj.dat
%
% Output file format (TECPLOT, PARAVIEW, TECPLOT_BINARY)
%OUTPUT_FORMAT= TECPLOT_BINARY
%
% Output file convergence history (w/o extension) 
CONV_FILENAME= history
%
% Output file restart flow
RESTART_FLOW_FILENAME= restart_flow.dat
%
% Output file restart adjoint
RESTART_ADJ_FILENAME= restart_adj.dat
%
% Output file flow (w/o extension) variables
VOLUME_FLOW_FILENAME= flow
%
% Output file adjoint (w/o extension) variables
VOLUME_ADJ_FILENAME= adjoint
%
% Output Objective function gradient (using continuous adjoint)
GRAD_OBJFUNC_FILENAME= of_grad.dat
%
% Output file surface flow coefficient (w/o extension)
SURFACE_FLOW_FILENAME= surface_flow
%
% Output file surface adjoint coefficient (w/o extension)
SURFACE_ADJ_FILENAME= surface_adjoint
%
% Writing solution file frequency
WRT_SOL_FREQ= 1000
%
% Writing convergence history frequency
WRT_CON_FREQ= 1000

% --------------------- OPTIMAL SHAPE DESIGN DEFINITION -----------------------%
% Available flow based objective functions or constraint functions
%    DRAG, LIFT, SIDEFORCE, EFFICIENCY,
%    FORCE_X, FORCE_Y, FORCE_Z,
%    MOMENT_X, MOMENT_Y, MOMENT_Z,
%    THRUST, TORQUE, FIGURE_OF_MERIT,
%    EQUIVALENT_AREA, NEARFIELD_PRESSURE, 
%    TOTAL_HEATFLUX, MAXIMUM_HEATFLUX,
%    INVERSE_DESIGN_PRESSURE, INVERSE_DESIGN_HEATFLUX,
%
% Available geometrical based objective functions or constraint functions
%    AIRFOIL_AREA, AIRFOIL_THICKNESS, AIRFOIL_CHORD, AIRFOIL_TOC, AIRFOIL_AOA,
%    WING_VOLUME, WING_MIN_THICKNESS, WING_MAX_THICKNESS, WING_MAX_CHORD, WING_MIN_TOC, WING_MAX_TWIST, WING_MAX_CURVATURE, WING_MAX_DIHEDRAL
%    STATION#_WIDTH, STATION#_AREA, STATION#_THICKNESS, STATION#_CHORD, STATION#_TOC,
%    STATION#_TWIST (where # is the index of the station defined in GEO_LOCATION_STATIONS)
%
% Available design variables
%    HICKS_HENNE 	(  1, Scale | Mark. List | Lower(0)/Upper(1) side, x_Loc )
%    NACA_4DIGITS	(  4, Scale | Mark. List | 1st digit, 2nd digit, 3rd and 4th digit )
%    TRANSLATION	(  5, Scale | Mark. List | x_Disp, y_Disp, z_Disp )
%    ROTATION		(  6, Scale | Mark. List | x_Axis, y_Axis, z_Axis, x_Turn, y_Turn, z_Turn )
%    FFD_CONTROL_POINT_2D (  15, Scale | Mark. List | FFD_Box_ID, i_Ind, j_Ind, x_Mov, y_Mov )
%    FFD_CAMBER_2D 	( 16, Scale | Mark. List | FFD_Box_ID, i_Ind )
%    FFD_THICKNESS_2D 	( 17, Scale | Mark. List | FFD_Box_ID, i_Ind )
%
% Optimization objective function with scaling factor
% ex= Objective * Scale
% OPT_OBJECTIVE= DRAG * 0.001
%
% Optimization constraint functions with scaling factors, separated by semicolons
% ex= (Objective = Value ) * Scale, use '>','<','='
% OPT_CONSTRAINT= ( LIFT > 0.328188 ) * 0.001; ( MOMENT_Z > 0.034068 ) * 0.001; ( AIRFOIL_THICKNESS > 0.11 ) * 0.001
%
% Optimization design variables, separated by semicolons
% DEFINITION_DV= ( 1, 1.0 | airfoil | 0, 0.05 ); ( 1, 1.0 | airfoil | 0, 0.10 ); ( 1, 1.0 | airfoil | 0, 0.15 ); ( 1, 1.0 | airfoil | 0, 0.20 ); ( 1, 1.0 | airfoil | 0, 0.25 ); ( 1, 1.0 | airfoil | 0, 0.30 ); ( 1, 1.0 | airfoil | 0, 0.35 ); ( 1, 1.0 | airfoil | 0, 0.40 ); ( 1, 1.0 | airfoil | 0, 0.45 ); ( 1, 1.0 | airfoil | 0, 0.50 ); ( 1, 1.0 | airfoil | 0, 0.55 ); ( 1, 1.0 | airfoil | 0, 0.60 ); ( 1, 1.0 | airfoil | 0, 0.65 ); ( 1, 1.0 | airfoil | 0, 0.70 ); ( 1, 1.0 | airfoil | 0, 0.75 ); ( 1, 1.0 | airfoil | 0, 0.80 ); ( 1, 1.0 | airfoil | 0, 0.85 ); ( 1, 1.0 | airfoil | 0, 0.90 ); ( 1, 1.0 | airfoil | 0, 0.95 ); ( 1, 1.0 | airfoil | 1, 0.05 ); ( 1, 1.0 | airfoil | 1, 0.10 ); ( 1, 1.0 | airfoil | 1, 0.15 ); ( 1, 1.0 | airfoil | 1, 0.20 ); ( 1, 1.0 | airfoil | 1, 0.25 ); ( 1, 1.0 | airfoil | 1, 0.30 ); ( 1, 1.0 | airfoil | 1, 0.35 ); ( 1, 1.0 | airfoil | 1, 0.40 ); ( 1, 1.0 | airfoil | 1, 0.45 ); ( 1, 1.0 | airfoil | 1, 0.50 ); ( 1, 1.0 | airfoil | 1, 0.55 ); ( 1, 1.0 | airfoil | 1, 0.60 ); ( 1, 1.0 | airfoil | 1, 0.65 ); ( 1, 1.0 | airfoil | 1, 0.70 ); ( 1, 1.0 | airfoil | 1, 0.75 ); ( 1, 1.0 | airfoil | 1, 0.80 ); ( 1, 1.0 | airfoil | 1, 0.85 ); ( 1, 1.0 | airfoil | 1, 0.90 ); ( 1, 1.0 | airfoil | 1, 0.95 )




DIFF_INPUTS= COORDS_X, COORDS_Y, AOA, MACH
DIFF_OUTPUTS= VEL_X, VEL_Y, PRESSURE
