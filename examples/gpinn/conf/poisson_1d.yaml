defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_poisson_1d/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 2023
output_dir: ${hydra:run.dir}

# set working condition
NPOINT_PDE: 15
NPOINT_PDE_EVAL: 100

# model settings
MODEL:
  input_keys: ["x"]
  output_keys: ["u"]
  num_layers: 3
  hidden_size: 20
  activation: "tanh"

# training settings
TRAIN:
  epochs: 20000
  iters_per_epoch: 1
  eval_freq: 1000
  eval_during_train: true
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  batch_size:
    l2rel_validator: ${NPOINT_PDE_EVAL}
  pretrained_model_path: null
