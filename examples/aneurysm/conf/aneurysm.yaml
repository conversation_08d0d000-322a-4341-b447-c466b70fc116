defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_aneurysm/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 2023
output_dir: ${hydra:run.dir}
log_freq: 20

# set working condition
NU: 0.025
SCALE: 0.4
RHO: 1.0
DIM: 3

# set geometry file path
INLET_STL_PATH: "./stl/aneurysm_inlet.stl"
OUTLET_STL_PATH: "./stl/aneurysm_outlet.stl"
NOSLIP_STL_PATH: "./stl/aneurysm_noslip.stl"
INTEGRAL_STL_PATH: "./stl/aneurysm_integral.stl"
INTERIOR_STL_PATH: "./stl/aneurysm_closed.stl"

# inlet velocity profile
CENTER: [-18.40381048596882, -50.285383353981196, 12.848136936899031]
INLET_NORMAL: [0.8526, -0.428, 0.299]
INLET_CENTER: [-4.24298030045776, 4.082857101816247, -4.637790193399717]
INLET_VEL: 1.5

# set evaluate data path
EVAL_CSV_PATH: "./data/aneurysm_parabolicInlet_sol0.csv"

# model settings
MODEL:
  input_keys: ["x", "y", "z"]
  output_keys: ["u", "v", "w", "p"]
  num_layers: 6
  hidden_size: 512
  activation: "silu"
  weight_norm: true

# training settings
TRAIN:
  epochs: 1500
  iters_per_epoch: 1000
  iters_integral:
    igc_outlet: 100
    igc_integral: 100
  save_freq: 20
  eval_during_train: true
  eval_freq: 20
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    learning_rate: 0.001
    gamma: 0.95
    decay_steps: 15000
    by_epoch: false
  batch_size:
    bc_inlet: 1100
    bc_outlet: 650
    bc_noslip: 5200
    pde: 6000
    igc_outlet: 1
    igc_integral: 1
  integral_batch_size:
    igc_outlet: 310
    igc_integral: 310
  weight:
    igc_outlet: {"normal_dot_vec": 0.1}
    igc_integral: {"normal_dot_vec": 0.1}
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true
  batch_size: 4096

# inference settings
INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/aneurysm/aneurysm_pretrained.pdparams
  export_path: ./inference/aneurysm
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  onnx_path: ${INFER.export_path}.onnx
  device: gpu
  engine: native
  precision: fp32
  ir_optim: true
  min_subgraph_size: 5
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 1024
  num_cpu_threads: 10
  batch_size: 1024
