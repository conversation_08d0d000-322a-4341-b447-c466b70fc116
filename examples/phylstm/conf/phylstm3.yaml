defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_PhyLSTM3/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20

# set data file path
DATA_FILE_PATH: data_boucwen.mat

# model settings
MODEL:
  input_size: 1
  hidden_size: 100
  model_type: 3

# training settings
TRAIN:
  epochs: 200
  iters_per_epoch: 1
  save_freq: 50
  learning_rate: 0.001
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true
