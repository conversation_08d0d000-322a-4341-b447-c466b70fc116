defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_shock_wave/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20

# set working condition
MA: 2.0

# set hyper-parameters
Lt: 0.4
Lx: 1.5
Ly: 2.0
rx: 1.0
ry: 1.0
rd: 0.25
N_INTERIOR: 100000
N_BOUNDARY: 10000
RHO1: 2.112
P1: 3.001
GAMMA: 1.4
V1: 0.0

# visualize prediction
Nd: 600
T: 0.4

# model settings
MODEL:
  input_keys: ["t", "x", "y"]
  output_keys: ["u", "v", "p", "rho"]
  num_layers: 9
  hidden_size: 90
  activation: "tanh"

# training settings
TRAIN:
  epochs: 100
  iters_per_epoch: 1
  save_freq: 50
  eval_during_train: false
  eval_freq: 20
  learning_rate: 1e-1
  max_iter: 100
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true

INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/shockwave/shock_wave_Ma2_pretrained.pdparams
  export_path: ./inference/shock_wave_Ma2.0
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 256
  num_cpu_threads: 4
  batch_size: 256
