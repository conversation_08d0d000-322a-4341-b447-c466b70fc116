defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20

# set working condition
DL: 1.0  # length of the domain
cp_c: 1.0 # specific heat capacity of cold boundary
cp_h: 1.0 # specific heat capacity of hot boundary
cp_w: 1.0 # specific heat capacity of wall
v_h: 1.0 # flow rate of hot boundary
v_c: 1.0 # flow rate of cold boundary
alpha_h: 1.0 # surface efficiency*heat transfer coefficient*heat transfer area of hot boundary
alpha_c: 1.0 # surface efficiency*heat transfer coefficient*heat transfer area of cold boundary
L: 1.0 # flow length
M: 1.0 # heat transfer structural quality
T_hin: 10.0 # initial temperature of hot boundary
T_cin: 1.0 # initial temperature of cold boundary
T_win: 5.5 # initial temperature of wall
NTIME: 20 # number of time steps
NPOINT: 101 # number of points in the domain
NQM: 60 # Number of branch network samples

# model settings
MODEL:
  heat_input_keys: ['qm_h']
  cold_input_keys: ['qm_c']
  trunk_input_keys: ["x",'t']
  output_keys: ["T_h",'T_c','T_w']
  heat_num_loc: 1
  cold_num_loc: 1
  num_features: 100
  branch_num_layers: 9
  trunk_num_layers: 6
  branch_hidden_size: 256
  trunk_hidden_size: 128
  branch_activation: "swish"
  trunk_activation: "swish"
  use_bias: true

# training settings
TRAIN:
  epochs: 10000
  iters_per_epoch: 1
  eval_during_train: true
  eval_freq: 1000
  learning_rate: 0.001
  batch_size: 1000
  weight:
    left_sup_constraint:
      T_h: 20
    right_sup_constraint:
      T_h: 20
    interior_sup_constraint:
      heat_boundary: 1
      cold_boundary: 1
      wall: 20
    initial_sup_constraint:
      T_h: 1
      T_c: 1
      T_w: 20

# evaluation settings
EVAL:
  pretrained_model_path: null

# visualization settings
qm_h: 1
qm_c: 1
eta_true: 0.5

# inference settings
INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/HEDeepONet/HEDeepONet_pretrained.pdparams
  export_path: ./inference/ldc2d_steady_Re10
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  onnx_path: ${INFER.export_path}.onnx
  device: gpu
  engine: native
  precision: fp32
  ir_optim: true
  min_subgraph_size: 5
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 1000
  num_cpu_threads: 10
  batch_size: 1000
  input_keys: ['qm_h','qm_c',"x",'t']
