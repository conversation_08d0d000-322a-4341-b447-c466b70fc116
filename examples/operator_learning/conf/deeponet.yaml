defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_deeponet/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 2023
output_dir: ${hydra:run.dir}
log_freq: 20
TRAIN_FILE_PATH: ./antiderivative_unaligned_train.npz
VALID_FILE_PATH: ./antiderivative_unaligned_test.npz

# set working condition
NUM_Y: 1000 # number of y point for G(u) to be visualized

# model settings
MODEL:
  u_key: "u"
  y_key: "y"
  G_key: "G"
  num_loc: 100
  num_features: 40
  branch_num_layers: 1
  trunk_num_layers: 1
  branch_hidden_size: 40
  trunk_hidden_size: 40
  branch_activation: relu
  trunk_activation: relu
  use_bias: true

# training settings
TRAIN:
  epochs: 10000
  iters_per_epoch: 1
  learning_rate: 1.0e-3
  save_freq: 500
  eval_freq: 500
  eval_during_train: true
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true

# inference settings
INFER:
  pretrained_model_path: "https://paddle-org.bj.bcebos.com/paddlescience/models/deeponet/deeponet_pretrained.pdparams"
  export_path: ./inference/deeponet
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 128
  num_cpu_threads: 4
  batch_size: 128
