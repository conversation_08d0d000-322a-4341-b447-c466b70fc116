defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_uno_pretrain
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval/export/infer
seed: 666
output_dir: ${hydra:run.dir}
log_freq: 20

# set train and evaluate data path
FILE_PATH: ./datasets/darcyflow/

# dataset setting
DATASET:
  label_keys: ["y"]
  train_resolution: 16
  test_resolutions: [16, 32]
  grid_boundaries: [[0, 1], [0, 1]]
  positional_encoding: True
  encode_input: False
  encode_output: False
  encoding: "channel-wise"
  channel_dim: 1

# model settings
MODEL:
  input_keys: ["x"]
  output_keys: ["y"]
  in_channels: 3
  out_channels: 1
  hidden_channels: 64
  projection_channels: 64
  n_layers: 5
  uno_out_channels: [32, 64, 64, 64, 32]
  uno_n_modes: [[16, 16], [8, 8], [8, 8], [8, 8], [16, 16]]
  uno_scalings: [[1.0, 1.0], [0.5, 0.5], [1, 1], [2, 2], [1, 1]]
  horizontal_skips_map: null
  incremental_n_modes: null

  use_mlp: false
  mlp:
    expansion: 0.5
    dropout: 0.0
  norm: "group_norm"
  fno_skip: "linear"
  horizontal_skip: "linear"
  mlp_skip: "soft-gating"
  separable: false
  preactivation: false
  factorization: null
  rank: 1.0
  joint_factorization: false
  fixed_rank_modes: null
  implementation: "factorized"
  domain_padding: 0.2 #0.078125
  domain_padding_mode: "one-sided" #symmetric
  fft_norm: "forward"
  patching_levels: 0

# training settings
TRAIN:
  epochs: 300
  save_freq: 20
  eval_during_train: true
  eval_freq: 1
  training_loss: "h1"
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    learning_rate: 5e-3
    by_epoch: True
    type: "StepDecay"
    step_size: 60
    gamma: 0.5
    # ReduceOnPlateau only
    scheduler_patience: 5

    # CosineAnnealingLR
    scheduler_T_max: 30
  wd: 1.0e-4
  batch_size: 16
  # /home/<USER>/darcy_flow_small.pdparams
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: ./outputs_uno_pretrain/checkpoints/best_model.pdparams
  compute_metric_by_batch: false
  eval_with_no_grad: true
  batch_size: 16

INFER:
  pretrained_model_path: ./outputs_uno_pretrain/checkpoints/best_model.pdparams
  export_path: ./inference/uno/uno_darcyflow
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 16
  num_cpu_threads: 4
  batch_size: 1
  data_path: ./datasets/darcyflow/darcy_test_16.npy
  grid_boundaries: [[0, 1], [0, 1]]
