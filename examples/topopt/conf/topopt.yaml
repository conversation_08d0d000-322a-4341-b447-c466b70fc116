defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory
    dir: outputs_topopt/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
    config:
      override_dirname:
        exclude_keys:
          - TRAIN.batch_size
          - TRAIN.epochs
          - TRAIN.learning_rate
          - EVAL.pretrained_model_path_dict
          - EVAL.batch_size
          - EVAL.num_val_step
          - INFER.pretrained_model_name
          - INFER.pretrained_model_path_dict
          - INFER.export_path
          - INFER.batch_size
          - mode
          - vol_coeff
          - log_freq
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20

# set default cases parameters
CASE_PARAM: [[Poisson, 5], [Poisson, 10], [Poisson, 30], [Uniform, null]]

# set data path
DATA_PATH: ./datasets/top_dataset.h5

# model settings
MODEL:
  in_channel: 2
  out_channel: 1
  kernel_size: 3
  filters: [16, 32, 64]
  layers: 2

# other parameters
n_samples: 10000
train_test_ratio: 1.0 # use 10000 original data with different channels for training
vol_coeff: 1 # coefficient for volume fraction constraint in the loss - beta in equation (3) in paper

# training settings
TRAIN:
  epochs: 30
  learning_rate: 0.001
  batch_size: 64
  eval_during_train: false

# evaluation settings
EVAL:
  pretrained_model_path_dict: null # a dict: {casename1:path1, casename2:path2, casename3:path3, casename4:path4}
  num_val_step: 10 # the number of iteration for each evaluation case
  batch_size: 16

# inference settings
INFER:
  pretrained_model_name: null # a string, indicating which model you want to export. Support [Uniform, Poisson5, Poisson10, Poisson30].
  pretrained_model_path_dict:
    {
      "Uniform": "https://paddle-org.bj.bcebos.com/paddlescience/models/topopt/uniform_pretrained.pdparams",
      "Poisson5": "https://paddle-org.bj.bcebos.com/paddlescience/models/topopt/poisson5_pretrained.pdparams",
      "Poisson10": "https://paddle-org.bj.bcebos.com/paddlescience/models/topopt/poisson10_pretrained.pdparams",
      "Poisson30": "https://paddle-org.bj.bcebos.com/paddlescience/models/topopt/poisson30_pretrained.pdparams",
    }
  export_path: ./inference/topopt_${INFER.pretrained_model_name}
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: null
  ir_optim: true
  min_subgraph_size: 30
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 1024
  num_cpu_threads: 10
  batch_size: 4
  sampler_key: Fixed # a string, indicating the sampling method. Support [Fixed, Uniform, Poisson].
  sampler_num: 8 # a integer number, indicating the sampling rate of the sampling method, supported when `sampler_key` is Fixed or Poisson.
  img_num: 4
  res_img_figsize: null
  save_res_path: ./inference/predicted_${INFER.pretrained_model_name}
  save_npy: false
