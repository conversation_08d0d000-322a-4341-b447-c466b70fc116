defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_cylinder2d_unsteady_transformer_physx_transformer/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
    config:
      override_dirname:
        exclude_keys:
          - TRAIN.checkpoint_path
          - TRAIN.pretrained_model_path
          - EVAL.pretrained_model_path
          - mode
          - output_dir
          - log_freq
          - EMBEDDING_MODEL_PATH
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
TRAIN_BLOCK_SIZE: 16
VALID_BLOCK_SIZE: 256
TRAIN_FILE_PATH: ./datasets/cylinder_training.hdf5
VALID_FILE_PATH: ./datasets/cylinder_valid.hdf5
log_freq: 20

# set working condition
EMBEDDING_MODEL_PATH: ./outputs_cylinder2d_unsteady_transformer_physx_enn/checkpoints/latest
VIS_DATA_NUMS: 1

# model settings
MODEL:
  input_keys: ["embeds"]
  output_keys: ["pred_embeds"]
  num_layers: 6
  num_ctx: 16
  embed_size: 128
  num_heads: 4

# training settings
TRAIN:
  epochs: 200
  batch_size: 4
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    learning_rate: 0.001
    T_0: 14
    T_mult: 2
    eta_min: 1.0e-9
  optimizer:
    weight_decay: 1.0e-8
  eval_during_train: true
  eval_freq: 50
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  batch_size: 16
  pretrained_model_path: null

INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/cylinder/cylinder_transformer_pretrained.pdparams
  export_path: ./inference/cylinder_transformer
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: false
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 64
  num_cpu_threads: 4
  batch_size: 16
