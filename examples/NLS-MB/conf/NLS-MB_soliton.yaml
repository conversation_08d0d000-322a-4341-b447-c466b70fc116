defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: output_NLS-MB_soliton/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
log_freq: 100
output_dir: ${hydra:run.dir}
NPOINT_INTERIOR: 20000
NPOINT_BC: 600
NTIME_ALL: 200

# model settings
MODEL:
  input_keys: ["t", "x"]
  output_keys: ["Eu", "Ev", "pu", "pv", "eta"]
  num_layers: 5
  hidden_size: 64

# training settings
TRAIN:
  epochs: 50000
  iters_per_epoch: 1
  lbfgs:
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    output_dir: ${output_dir}LBFGS
    learning_rate: 1.0
    max_iter: 1
    eval_freq: ${TRAIN.eval_freq}
    eval_during_train: ${TRAIN.eval_during_train}
  eval_during_train: true
  eval_freq: 1000
  learning_rate: 0.001

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: false

INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/NLS-MB/NLS-MB_soliton_pretrained.pdparams
  export_path: ./inference/NLS-MB_soliton
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 64
  num_cpu_threads: 4
  batch_size: 64
