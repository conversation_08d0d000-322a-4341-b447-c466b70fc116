defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_tempoGAN/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20
DATASET_PATH: ./datasets/tempoGAN/2d_train.mat
DATASET_PATH_VALID: ./datasets/tempoGAN/2d_valid.mat

# set working condition
USE_AMP: true
USE_SPATIALDISC: true
USE_TEMPODISC: true
WEIGHT_GEN: [5.0, 0.0, 1.0]  # lambda_l1, lambda_l2, lambda_t
WEIGHT_GEN_LAYER: [-1.0e-5, -1.0e-5, -1.0e-5, -1.0e-5, -1.0e-5]
WEIGHT_DISC: 1.0
TILE_RATIO: 1

# model settings
MODEL:
  gen_net:
    input_keys: ["input_gen"] # 'NCHW'
    output_keys: ["output_gen"]
    in_channel: 1
    out_channels_tuple: [[2, 8, 8], [128, 128, 128], [32, 8, 8], [2, 1, 1]]
    kernel_sizes_tuple: [[[5, 5], [5, 5], [1, 1]], [[5, 5], [5, 5], [1, 1]], [[5, 5], [5, 5], [1, 1]], [[5, 5], [5, 5], [1, 1]]]
    strides_tuple: [[1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]]
    use_bns_tuple: [[true, true, true], [true, true, true], [true, true, true], [false, false, false]]
    acts_tuple: [['relu', null, null], ['relu', null, null], ['relu', null, null], ['relu', null, null]]
  disc_net:
    input_keys:  ['input_disc_from_target', 'input_disc_from_gen']  # 'NCHW'
    output_keys:  ['out0_layer0', 'out0_layer1', 'out0_layer2', 'out0_layer3', 'out_disc_from_target', 'out1_layer0', 'out1_layer1', 'out1_layer2', 'out1_layer3', 'out_disc_from_gen']
    in_channel:  2
    out_channels:  [32, 64, 128, 256]
    fc_channel:  1048576
    kernel_sizes:  [[4, 4], [4, 4], [4, 4], [4, 4]]
    strides:  [2, 2, 2, 1]
    use_bns:  [false, true, true, true]
    acts:  ['leaky_relu', 'leaky_relu', 'leaky_relu', 'leaky_relu', null]
  tempo_net:
    input_keys:  ['input_tempo_disc_from_target', 'input_tempo_disc_from_gen']  # 'NCHW'
    output_keys:  ['out0_tempo_layer0', 'out0_tempo_layer1', 'out0_tempo_layer2', 'out0_tempo_layer3', 'out_disc_tempo_from_target', 'out1_tempo_layer0', 'out1_tempo_layer1', 'out1_tempo_layer2', 'out1_tempo_layer3', 'out_disc_tempo_from_gen']
    in_channel:  3
    out_channels:  [32, 64, 128, 256]
    fc_channel:  1048576
    kernel_sizes:  [[4, 4], [4, 4], [4, 4], [4, 4]]
    strides:  [2, 2, 2, 1]
    use_bns:  [false, true, true, true]
    acts:  ['leaky_relu', 'leaky_relu', 'leaky_relu', 'leaky_relu', null]

# training settings
TRAIN:
  epochs: 40000
  epochs_gen: 1
  epochs_disc: 1
  epochs_disc_tempo: 1
  iters_per_epoch: 2
  batch_size:
    sup_constraint: 8
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    learning_rate: 2.0e-4
    gamma: 0.05
    by_epoch: true
  eval_during_train: false
  amp_level: O2
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  save_outs: true

INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/tempoGAN/tempogan_pretrained.pdparams
  export_path: ./inference/tempoGAN
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 16
  num_cpu_threads: 4
  batch_size: 1
