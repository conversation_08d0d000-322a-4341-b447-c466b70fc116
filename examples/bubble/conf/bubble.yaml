defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_bubble/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20
DATA_PATH: bubble.mat

# model settings
MODEL:
  psi_net:
    input_keys: ["t", "x", "y"]
    output_keys: ["psi"]
    num_layers: 9
    hidden_size: 30
    activation: "tanh"
  p_net:
    input_keys: ["t", "x", "y"]
    output_keys: ["p"]
    num_layers: 9
    hidden_size: 30
    activation: "tanh"
  phil_net:
    input_keys: ["t", "x", "y"]
    output_keys: ["phil"]
    num_layers: 9
    hidden_size: 30
    activation: "tanh"
  output_keys: ["u", "v", "p", "phil"]

# training settings
TRAIN:
  epochs: 10000
  iters_per_epoch: 1
  eval_during_train: true
  eval_freq: 1000
  learning_rate: 0.001
  batch_size:
    pde_constraint: 228595
    sup_constraint: 2419
    mse_validator: 2419
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true

# inference settings
INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/bubble/bubble_pretrained.pdparams
  export_path: ./inference/bubble
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  onnx_path: ${INFER.export_path}.onnx
  device: gpu
  engine: native
  precision: fp32
  ir_optim: true
  min_subgraph_size: 5
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 8192
  num_cpu_threads: 10
  batch_size: 8192
