defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_darcy2d/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20

# set working condition
NPOINT_PDE: 9801  # 99 ** 2
NPOINT_BC: 400  # 100 * 4

# model settings
MODEL:
  input_keys: ["x", "y"]
  output_keys: ["p"]
  num_layers: 5
  hidden_size: 20
  activation: "stan"

# training settings
TRAIN:
  epochs: 10000
  iters_per_epoch: 1
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    max_learning_rate: 1.0e-3
    end_learning_rate: 1.0e-7
  lbfgs:
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    output_dir: ./outputs_darcy2d_L-BFGS
    learning_rate: 1.0
    max_iter: 10
    eval_freq: ${TRAIN.eval_freq}
    eval_during_train: ${TRAIN.eval_during_train}
  eval_freq: 200
  eval_during_train: true
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  batch_size:
    residual_validator: 8192
  pretrained_model_path: null

INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/darcy2d/darcy2d_pretrained.pdparams
  export_path: ./inference/darcy2d
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  onnx_path: ${INFER.export_path}.onnx
  device: gpu
  engine: native
  precision: fp32
  ir_optim: true
  min_subgraph_size: 5
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 8192
  num_cpu_threads: 10
  batch_size: 8192
