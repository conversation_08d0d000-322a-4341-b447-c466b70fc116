defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_poiseuille_flow/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
log_freq: 20

# set working condition
NU_MEAN: 0.001
NU_STD: 0.9
L: 1.0 # length of pipe
R: 0.05 # radius of pipe
RHO: 1 # density
P_OUT: 0 # pressure at the outlet of pipe
P_IN: 0.1 # pressure at the inlet of pipe
N_x: 10
N_y: 50
N_p: 50
X_IN: 0

# model settings
MODEL:
  u_net:
    input_keys: ["sin(x)", "cos(x)", "y", "nu"]
    output_keys: ["u"]
    num_layers: 3
    hidden_size: 50
    activation: "swish"
  v_net:
    input_keys: ["sin(x)", "cos(x)", "y", "nu"]
    output_keys: ["v"]
    num_layers: 3
    hidden_size: 50
    activation: "swish"
  p_net:
    input_keys: ["sin(x)", "cos(x)", "y", "nu"]
    output_keys: ["p"]
    num_layers: 3
    hidden_size: 50
    activation: "swish"
  output_keys: ["v", "u", "p"]

# training settings
TRAIN:
  epochs: 3000
  batch_size:
    pde_constraint: 128
  learning_rate: 5.0e-3
  eval_during_train: false
  save_freq: 10
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true

# inference settings
INFER:
  pretrained_model_path: "https://paddle-org.bj.bcebos.com/paddlescience/models/LabelFree-DNN-Surrogate/poiseuille_flow_pretrained.pdparams"
  export_path: ./inference/poiseuile_flow
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 5
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 256
  num_cpu_threads: 4
  batch_size: 256
