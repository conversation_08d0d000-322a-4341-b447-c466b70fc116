defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_heat_pinn/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 2
output_dir: ${hydra:run.dir}
log_freq: 20

# model settings
MODEL:
  input_keys: ["x", "y"]
  output_keys: ["u"]
  num_layers: 9
  hidden_size: 20
  activation: "tanh"

# training settings
TRAIN:
  epochs: 1000
  iters_per_epoch: 1
  save_freq: 20
  learning_rate: 5.0e-4
  weight:
    bc_top: 0.25
    bc_bottom: 0.25
    bc_left: 0.25
    bc_right: 0.25
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null

# inference settings
INFER:
  pretrained_model_path: "https://paddle-org.bj.bcebos.com/paddlescience/models/heat_pinn/heat_pinn_pretrained.pdparams"
  export_path: ./inference/heat_pinn
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 2000
  gpu_id: 0
  max_batch_size: 128
  num_cpu_threads: 4
  batch_size: 128
