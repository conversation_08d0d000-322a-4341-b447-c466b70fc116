defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_control_arm/forward/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 2023
output_dir: ${hydra:run.dir}
log_freq: 100

# set working condition
NU: 0.3
E: 1
# T: [0, 0, 0.0025]   # +Z axis
T: [-0.0025, 0, 0]  # -X axis

# set geometry file path
GEOM_PATH: ./datasets/control_arm.stl

# set geometry parameter
CIRCLE_LEFT_CENTER_XY: [-4.4, 0]
CIRCLE_LEFT_RADIUS: 1.65
CIRCLE_RIGHT_CENTER_XZ: [15.8, 0]
CIRCLE_RIGHT_RADIUS: 2.21

# model settings
MODEL:
  disp_net:
    input_keys: ["x", "y", "z"]
    output_keys: ["u", "v", "w"]
    num_layers: 6
    hidden_size: 512
    activation: "silu"
    weight_norm: true
  stress_net:
    input_keys: ["x", "y", "z"]
    output_keys: ["sigma_xx", "sigma_yy", "sigma_zz", "sigma_xy", "sigma_xz", "sigma_yz"]
    num_layers: 6
    hidden_size: 512
    activation: "silu"
    weight_norm: true

# training settings
TRAIN:
  epochs: 2000
  iters_per_epoch: 1000
  save_freq: 100
  eval_freq: 100
  eval_during_train: false
  eval_with_no_grad: false
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    learning_rate: 1.0e-4
    gamma: 0.95
    decay_steps: 15000
    by_epoch: false
  batch_size:
    arm_left: 128
    arm_right: 256
    arm_surface: 4096
    arm_interior: 2048
    visualizer_vtu: 100000
  weight:
    arm_right: {"u": 1, "v": 1, "w": 1}
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  eval_with_no_grad: true
  pretrained_model_path: null


INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/control_arm/forward_x_axis_pretrained.pdparams
  export_path: ./inference/forward_analysis
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 128
  num_cpu_threads: 4
  batch_size: 64
