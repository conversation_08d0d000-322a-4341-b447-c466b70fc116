defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_control_arm/inverse/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 2023
output_dir: ${hydra:run.dir}
log_freq: 100

# set working condition
NU: 0.3
E: 1

# set geometry file path
GEOM_PATH: ./datasets/control_arm.stl

# set geometry parameter
CIRCLE_LEFT_CENTER_XY: [-4.4, 0]
CIRCLE_LEFT_RADIUS: 1.65
CIRCLE_RIGHT_CENTER_XZ: [15.8, 0]
CIRCLE_RIGHT_RADIUS: 2.21

# model settings
MODEL:
  disp_net:
    input_keys: ["x", "y", "z"]
    output_keys: ["u", "v", "w"]
    num_layers: 6
    hidden_size: 512
    activation: "silu"
    weight_norm: true
  stress_net:
    input_keys: ["x", "y", "z"]
    output_keys: ["sigma_xx", "sigma_yy", "sigma_zz", "sigma_xy", "sigma_xz", "sigma_yz"]
    num_layers: 6
    hidden_size: 512
    activation: "silu"
    weight_norm: true
  inverse_lambda_net:
    input_keys: ["x", "y", "z"]
    output_keys: ["lambda_",]
    num_layers: 6
    hidden_size: 512
    activation: "silu"
    weight_norm: true
  inverse_mu_net:
    input_keys: ["x", "y", "z"]
    output_keys: ["mu",]
    num_layers: 6
    hidden_size: 512
    activation: "silu"
    weight_norm: true

# training settings
TRAIN:
  epochs: 100
  iters_per_epoch: 100
  save_freq: 1
  eval_freq: 1
  eval_during_train: true
  eval_with_no_grad: false
  lr_scheduler:
    epochs: ${TRAIN.epochs}
    iters_per_epoch: ${TRAIN.iters_per_epoch}
    learning_rate: 1.0e-4
    gamma: 0.95
    decay_steps: 100
    by_epoch: false
  batch_size:
    arm_interior: 2000
    visualizer_vtu: 100000
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true
  total_size:
    validator: 20000
  batch_size:
    validator: 1024
    visualizer_vtu: 100000

INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/control_arm/inverse_x_axis_pretrained.pdparams
  export_path: ./inference/inverse_parameter
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 128
  num_cpu_threads: 4
  batch_size: 64
