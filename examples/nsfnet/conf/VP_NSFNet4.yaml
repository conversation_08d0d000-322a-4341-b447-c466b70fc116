defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_nsfnet/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  callbacks:
    init_callback:
      _target_: ppsci.utils.callbacks.InitCallback
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
seed: 1234
output_dir: ${hydra:run.dir}
data_dir: ./data/
log_freq: 20
MODEL:
  input_keys: ["x", "y", "z", "t"]
  output_keys: ["u", "v", "w", "p"]
  num_layers: 10
  hidden_size: 300
  activation: "tanh"
  weight_norm: true
mode: train
ntrain: 11333
nb_train: 2952
n0_train: 986
alpha: 100
beta: 100
re: 999.35
epochs: 15250
TRAIN:
  log_freq: 5000
  eval_freq: 5000
  save_freq: 5000
  eval_with_no_grad: true
  lr_scheduler:
    epochs: 15250
    decay_epochs: [250, 4500, 5000, 5500]
    iters_per_epoch: 150
    values: [1e-3, 1e-4, 1e-5, 1e-6, 1e-7]
EVAL:
  pretrained_model_path: null
  eval_with_no_grad: true

INFER:
  pretrained_model_path: https://paddle-org.bj.bcebos.com/paddlescience/models/nsfnet/nsfnet4.pdparams
  export_path: ./inference/VP_NSFNet4
  pdmodel_path: ${INFER.export_path}.pdmodel
  pdiparams_path: ${INFER.export_path}.pdiparams
  output_keys: ["p", "u", "v", "w"]
  device: gpu
  engine: native
  precision: fp32
  onnx_path: ${INFER.export_path}.onnx
  ir_optim: true
  min_subgraph_size: 10
  gpu_mem: 4000
  gpu_id: 0
  max_batch_size: 64
  num_cpu_threads: 4
  batch_size: 16
