defaults:
  - ppsci_default
  - TRAIN: train_default
  - TRAIN/ema: ema_default
  - TRAIN/swa: swa_default
  - EVAL: eval_default
  - INFER: infer_default
  - hydra/job/config/override_dirname/exclude_keys: exclude_keys_default
  - _self_

hydra:
  run:
    # dynamic output directory according to running time and override name
    dir: outputs_burgers/${now:%Y-%m-%d}/${now:%H-%M-%S}/${hydra.job.override_dirname}
  job:
    name: ${mode} # name of logfile
    chdir: false # keep current working directory unchanged
  sweep:
    # output directory for multirun
    dir: ${hydra.run.dir}
    subdir: ./

# general settings
mode: train # running mode: train/eval
seed: 42
output_dir: ${hydra:run.dir}
DATASET_PATH: ./datasets/burgers_sine.mat
DATASET_PATH_SOL: ./datasets/burgers_sine.mat

# set working condition
T_LB: 0.0
T_UB: 10.0
X_LB: -8.0
X_UB: 8.0

# model settings
MODEL:
  idn_net:
    input_keys: ["t", "x"]
    output_keys: ["u_idn"]
    num_layers: 4
    hidden_size: 50
    activation: "sin"
  pde_net:
    input_keys: ["u_x", "du_x", "du_xx"]
    output_keys: ["f_pde"]
    num_layers: 2
    hidden_size: 100
    activation: "sin"
  sol_net:
    input_keys: ["t", "x"]
    output_keys: ["u_sol"]
    num_layers: 4
    hidden_size: 50
    activation: "sin"

# training settings
TRAIN:
  epochs: 50000 # set 1 for LBFGS
  iters_per_epoch: 1
  max_iter: 50000  # for LBFGS
  learning_rate: 1.0e-3
  eval_during_train: false
  pretrained_model_path: null
  checkpoint_path: null

# evaluation settings
EVAL:
  pretrained_model_path: null
