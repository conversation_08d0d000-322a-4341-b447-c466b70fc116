===========================train_params===========================
model_name:euler_beam
bs_item:1
run_mode:DP
device_num:N1C1
python:python3.10
gpu_list:0
Global.use_gpu:null
null:null
TRAIN.epochs
null:null
null:null
null:null
train_model_name:latest
train_infer_img_dir:null
null:null
##
trainer:norm_train
norm_train:/examples/euler_beam/euler_beam.py
pact_train:null
fpgm_train:null
distill_train:null
null:null
null:null
##
===========================eval_params===========================
eval:null
null:null
##
===========================infer_params===========================
Global.save_inference_dir:./output/
Global.checkpoints:
norm_export:null
quant_export:null
fpgm_export:null
distill_export:null
export1:null
export2:null
inference_dir:null
train_model:null
infer_export:tools/export_model.py -c null -o
infer_quant:False
inference:tools/infer/predict_det.py
--use_gpu:True
--enable_mkldnn:null
--cpu_threads:null
--rec_batch_num:1
--use_tensorrt:null
--precision:fp32
--det_model_dir:
--image_dir:null
null:null
--benchmark:True
===========================train_benchmark_params==========================
batch_size:1
fp_items:fp32
epoch:10000
--profiler_options:batch_range=[1,1];state=GPU;tracer_option=Default;profile_path=model.profile
flags:FLAGS_eager_delete_tensor_gb=0.0;FLAGS_fraction_of_gpu_memory_to_use=0.98;FLAGS_conv_workspace_size_limit=0
===========================prepare_params==========================
download_dataset:
pip:pip
workdir:examples/euler_beam
