{"cells": [{"cell_type": "markdown", "metadata": {"id": "SqRGNIxYlTr7"}, "source": ["# **官方版本数据导入**"]}, {"cell_type": "markdown", "metadata": {"id": "yHcQ9wurwwFX"}, "source": ["赛道一"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "executionInfo": {"elapsed": 82228, "status": "ok", "timestamp": 1720765083865, "user": {"displayName": "<PERSON><PERSON>", "userId": "04820485600131748919"}, "user_tz": -480}, "id": "oL_v8aw8lZ72", "outputId": "2fd464ba-e35f-423a-f157-5b20a5f71e3f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2024-07-12 06:16:43--  https://drive.usercontent.google.com/download?id=1JwR0Q1ArTg6c47EF2ZuIBpQwCPgXKrO2&export=download&authuser=0&confirm=t&uuid=dc3aa13c-c3a9-458f-983a-8586798cb635&at=APZUnTX25XMxi-z-3wBcgR93IGsL%3A1719235792953\n", "Resolving drive.usercontent.google.com (drive.usercontent.google.com)... **************, 2404:6800:4003:c11::84\n", "Connecting to drive.usercontent.google.com (drive.usercontent.google.com)|**************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 1084182095 (1.0G) [application/octet-stream]\n", "Saving to: ‘Dataset.zip’\n", "\n", "Dataset.zip         100%[===================>]   1.01G  27.5MB/s    in 45s     \n", "\n", "2024-07-12 06:17:30 (23.0 MB/s) - ‘Dataset.zip’ saved [1084182095/1084182095]\n", "\n", "Archive:  Dataset.zip\n", "   creating: Dataset/\n", "   creating: Dataset/Testset_track_A/\n", "   creating: Dataset/Testset_track_A/Inference/\n", "  inflating: Dataset/Testset_track_A/Inference/mesh_658.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_659.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_660.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_662.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_663.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_664.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_665.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_666.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_667.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_668.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_672.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_673.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_674.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_675.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_676.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_677.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_678.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_679.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_681.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_683.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_684.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_686.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_687.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_688.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_689.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_690.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_691.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_692.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_693.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_695.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_696.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_697.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_700.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_701.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_702.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_703.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_704.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_705.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_708.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_709.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_710.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_711.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_712.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_713.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_715.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_717.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_718.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_719.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_721.ply  \n", "  inflating: Dataset/Testset_track_A/Inference/mesh_722.ply  \n", "   creating: Dataset/Testset_track_B/\n", "   creating: Dataset/Testset_track_B/Auxiliary/\n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_1.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_10.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_11.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_12.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_13.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_14.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_15.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_16.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_17.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_18.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_19.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_2.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_20.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_21.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_22.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_23.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_24.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_25.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_26.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_27.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_28.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_29.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_3.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_30.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_31.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_32.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_33.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_34.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_35.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_36.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_37.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_38.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_39.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_4.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_40.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_41.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_42.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_43.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_44.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_45.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_46.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_47.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_48.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_49.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_5.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_50.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_6.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_7.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_8.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_9.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/area_bounds.txt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/global_bounds.txt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_1.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_10.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_11.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_12.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_13.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_14.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_15.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_16.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_17.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_18.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_19.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_2.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_20.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_21.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_22.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_23.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_24.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_25.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_26.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_27.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_28.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_29.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_3.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_30.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_31.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_32.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_33.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_34.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_35.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_36.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_37.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_38.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_39.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_4.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_40.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_41.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_42.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_43.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_44.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_45.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_46.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_47.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_48.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_49.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_5.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_50.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_6.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_7.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_8.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_9.pt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/info_bounds.txt  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_1.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_10.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_11.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_12.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_13.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_14.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_15.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_16.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_17.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_18.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_19.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_2.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_20.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_21.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_22.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_23.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_24.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_25.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_26.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_27.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_28.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_29.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_3.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_30.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_31.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_32.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_33.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_34.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_35.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_36.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_37.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_38.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_39.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_4.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_40.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_41.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_42.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_43.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_44.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_45.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_46.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_47.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_48.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_49.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_5.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_50.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_6.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_7.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_8.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/normal_9.npy  \n", "  inflating: Dataset/Testset_track_B/Auxiliary/train_pressure_mean_std.txt  \n", "  inflating: Dataset/Testset_track_B/IJCAI_data_doc_v1.pdf  \n", "   creating: Dataset/Testset_track_B/Inference/\n", "  inflating: Dataset/Testset_track_B/Inference/centroid_1.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_10.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_11.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_12.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_13.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_14.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_15.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_16.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_17.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_18.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_19.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_2.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_20.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_21.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_22.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_23.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_24.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_25.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_26.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_27.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_28.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_29.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_3.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_30.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_31.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_32.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_33.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_34.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_35.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_36.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_37.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_38.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_39.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_4.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_40.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_41.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_42.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_43.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_44.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_45.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_46.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_47.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_48.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_49.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_5.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_50.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_6.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_7.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_8.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/centroid_9.npy  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_1.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_10.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_11.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_12.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_13.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_14.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_15.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_16.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_17.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_18.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_19.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_2.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_20.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_21.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_22.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_23.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_24.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_25.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_26.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_27.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_28.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_29.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_3.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_30.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_31.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_32.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_33.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_34.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_35.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_36.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_37.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_38.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_39.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_4.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_40.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_41.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_42.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_43.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_44.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_45.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_46.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_47.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_48.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_49.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_5.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_50.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_6.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_7.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_8.ply  \n", "  inflating: Dataset/Testset_track_B/Inference/mesh_9.ply  \n", "  inflating: Dataset/Testset_track_B/track_B_data_dict.xlsx  \n", "   creating: Dataset/Training_data/\n", "   creating: Dataset/Training_data/Feature/\n", "  inflating: Dataset/Training_data/Feature/mesh_001.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_002.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_004.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_005.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_006.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_007.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_008.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_010.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_012.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_013.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_017.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_018.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_021.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_022.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_023.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_025.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_026.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_027.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_028.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_029.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_030.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_031.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_032.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_034.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_035.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_039.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_040.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_043.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_044.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_045.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_046.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_047.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_048.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_049.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_050.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_051.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_052.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_054.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_055.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_056.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_058.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_059.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_060.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_061.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_062.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_063.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_064.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_065.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_067.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_069.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_070.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_071.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_072.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_073.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_074.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_075.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_076.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_077.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_078.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_079.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_080.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_081.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_083.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_084.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_085.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_086.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_087.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_088.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_090.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_091.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_092.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_094.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_095.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_096.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_097.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_100.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_101.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_102.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_105.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_106.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_107.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_109.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_110.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_111.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_112.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_113.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_114.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_115.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_116.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_117.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_118.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_119.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_120.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_121.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_123.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_124.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_125.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_126.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_127.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_128.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_129.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_130.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_131.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_133.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_134.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_136.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_137.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_138.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_139.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_140.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_141.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_142.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_143.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_144.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_145.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_146.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_147.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_148.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_149.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_150.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_151.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_152.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_153.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_155.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_156.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_157.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_158.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_159.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_160.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_161.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_162.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_163.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_165.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_166.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_170.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_172.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_173.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_175.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_176.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_177.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_178.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_179.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_180.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_181.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_182.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_183.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_184.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_186.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_190.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_191.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_192.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_193.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_195.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_196.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_198.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_199.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_200.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_201.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_202.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_203.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_205.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_207.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_210.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_211.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_212.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_213.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_214.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_215.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_217.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_219.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_220.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_221.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_222.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_223.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_224.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_225.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_227.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_228.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_229.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_230.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_231.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_232.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_233.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_234.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_235.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_236.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_237.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_241.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_243.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_244.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_245.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_246.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_247.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_248.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_249.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_251.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_252.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_253.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_255.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_257.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_258.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_259.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_260.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_261.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_262.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_263.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_264.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_266.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_267.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_268.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_269.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_271.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_272.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_273.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_274.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_275.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_276.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_277.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_278.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_279.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_280.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_281.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_282.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_283.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_285.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_286.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_289.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_290.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_291.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_292.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_293.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_294.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_295.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_296.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_297.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_298.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_299.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_300.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_301.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_302.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_304.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_305.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_306.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_308.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_309.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_310.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_311.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_312.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_313.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_314.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_315.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_319.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_320.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_321.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_322.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_323.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_324.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_325.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_327.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_328.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_329.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_331.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_332.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_333.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_334.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_335.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_337.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_338.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_339.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_340.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_341.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_344.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_345.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_347.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_348.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_349.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_350.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_352.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_353.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_354.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_355.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_356.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_357.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_358.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_360.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_362.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_364.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_365.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_366.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_367.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_369.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_371.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_372.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_373.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_374.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_375.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_376.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_378.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_379.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_380.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_381.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_384.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_385.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_389.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_392.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_393.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_397.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_398.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_399.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_401.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_402.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_403.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_404.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_405.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_407.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_408.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_410.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_412.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_413.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_414.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_415.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_417.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_418.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_419.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_420.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_422.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_424.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_425.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_427.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_430.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_431.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_433.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_435.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_436.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_437.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_439.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_440.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_443.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_444.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_446.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_447.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_448.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_449.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_450.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_451.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_452.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_453.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_454.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_455.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_456.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_457.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_459.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_460.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_462.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_463.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_464.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_465.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_466.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_467.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_468.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_469.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_470.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_472.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_473.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_474.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_475.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_476.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_478.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_479.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_480.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_482.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_483.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_486.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_487.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_488.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_490.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_493.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_494.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_495.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_496.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_497.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_498.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_499.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_501.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_502.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_503.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_504.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_505.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_507.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_508.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_509.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_511.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_512.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_513.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_514.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_515.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_516.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_518.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_519.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_521.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_522.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_523.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_524.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_525.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_527.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_529.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_530.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_532.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_533.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_536.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_538.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_539.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_540.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_542.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_543.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_545.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_547.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_548.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_549.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_550.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_551.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_552.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_553.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_554.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_555.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_560.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_561.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_562.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_564.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_565.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_566.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_567.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_568.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_569.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_572.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_573.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_574.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_576.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_577.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_579.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_581.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_582.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_583.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_584.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_587.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_588.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_589.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_591.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_593.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_594.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_595.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_596.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_597.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_598.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_600.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_602.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_604.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_608.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_610.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_611.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_612.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_613.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_615.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_616.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_617.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_618.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_620.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_621.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_622.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_623.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_625.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_626.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_627.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_628.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_629.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_630.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_631.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_632.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_633.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_634.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_635.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_636.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_638.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_639.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_640.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_641.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_642.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_643.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_644.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_645.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_646.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_647.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_648.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_649.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_651.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_652.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_654.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_655.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_656.ply  \n", "  inflating: Dataset/Training_data/Feature/mesh_657.ply  \n", "   creating: Dataset/Training_data/Label/\n", "  inflating: Dataset/Training_data/Label/press_001.npy  \n", "  inflating: Dataset/Training_data/Label/press_002.npy  \n", "  inflating: Dataset/Training_data/Label/press_004.npy  \n", "  inflating: Dataset/Training_data/Label/press_005.npy  \n", "  inflating: Dataset/Training_data/Label/press_006.npy  \n", "  inflating: Dataset/Training_data/Label/press_007.npy  \n", "  inflating: Dataset/Training_data/Label/press_008.npy  \n", "  inflating: Dataset/Training_data/Label/press_010.npy  \n", "  inflating: Dataset/Training_data/Label/press_012.npy  \n", "  inflating: Dataset/Training_data/Label/press_013.npy  \n", "  inflating: Dataset/Training_data/Label/press_017.npy  \n", "  inflating: Dataset/Training_data/Label/press_018.npy  \n", "  inflating: Dataset/Training_data/Label/press_021.npy  \n", "  inflating: Dataset/Training_data/Label/press_022.npy  \n", "  inflating: Dataset/Training_data/Label/press_023.npy  \n", "  inflating: Dataset/Training_data/Label/press_025.npy  \n", "  inflating: Dataset/Training_data/Label/press_026.npy  \n", "  inflating: Dataset/Training_data/Label/press_027.npy  \n", "  inflating: Dataset/Training_data/Label/press_028.npy  \n", "  inflating: Dataset/Training_data/Label/press_029.npy  \n", "  inflating: Dataset/Training_data/Label/press_030.npy  \n", "  inflating: Dataset/Training_data/Label/press_031.npy  \n", "  inflating: Dataset/Training_data/Label/press_032.npy  \n", "  inflating: Dataset/Training_data/Label/press_034.npy  \n", "  inflating: Dataset/Training_data/Label/press_035.npy  \n", "  inflating: Dataset/Training_data/Label/press_039.npy  \n", "  inflating: Dataset/Training_data/Label/press_040.npy  \n", "  inflating: Dataset/Training_data/Label/press_043.npy  \n", "  inflating: Dataset/Training_data/Label/press_044.npy  \n", "  inflating: Dataset/Training_data/Label/press_045.npy  \n", "  inflating: Dataset/Training_data/Label/press_046.npy  \n", "  inflating: Dataset/Training_data/Label/press_047.npy  \n", "  inflating: Dataset/Training_data/Label/press_048.npy  \n", "  inflating: Dataset/Training_data/Label/press_049.npy  \n", "  inflating: Dataset/Training_data/Label/press_050.npy  \n", "  inflating: Dataset/Training_data/Label/press_051.npy  \n", "  inflating: Dataset/Training_data/Label/press_052.npy  \n", "  inflating: Dataset/Training_data/Label/press_054.npy  \n", "  inflating: Dataset/Training_data/Label/press_055.npy  \n", "  inflating: Dataset/Training_data/Label/press_056.npy  \n", "  inflating: Dataset/Training_data/Label/press_058.npy  \n", "  inflating: Dataset/Training_data/Label/press_059.npy  \n", "  inflating: Dataset/Training_data/Label/press_060.npy  \n", "  inflating: Dataset/Training_data/Label/press_061.npy  \n", "  inflating: Dataset/Training_data/Label/press_062.npy  \n", "  inflating: Dataset/Training_data/Label/press_063.npy  \n", "  inflating: Dataset/Training_data/Label/press_064.npy  \n", "  inflating: Dataset/Training_data/Label/press_065.npy  \n", "  inflating: Dataset/Training_data/Label/press_067.npy  \n", "  inflating: Dataset/Training_data/Label/press_069.npy  \n", "  inflating: Dataset/Training_data/Label/press_070.npy  \n", "  inflating: Dataset/Training_data/Label/press_071.npy  \n", "  inflating: Dataset/Training_data/Label/press_072.npy  \n", "  inflating: Dataset/Training_data/Label/press_073.npy  \n", "  inflating: Dataset/Training_data/Label/press_074.npy  \n", "  inflating: Dataset/Training_data/Label/press_075.npy  \n", "  inflating: Dataset/Training_data/Label/press_076.npy  \n", "  inflating: Dataset/Training_data/Label/press_077.npy  \n", "  inflating: Dataset/Training_data/Label/press_078.npy  \n", "  inflating: Dataset/Training_data/Label/press_079.npy  \n", "  inflating: Dataset/Training_data/Label/press_080.npy  \n", "  inflating: Dataset/Training_data/Label/press_081.npy  \n", "  inflating: Dataset/Training_data/Label/press_083.npy  \n", "  inflating: Dataset/Training_data/Label/press_084.npy  \n", "  inflating: Dataset/Training_data/Label/press_085.npy  \n", "  inflating: Dataset/Training_data/Label/press_086.npy  \n", "  inflating: Dataset/Training_data/Label/press_087.npy  \n", "  inflating: Dataset/Training_data/Label/press_088.npy  \n", "  inflating: Dataset/Training_data/Label/press_090.npy  \n", "  inflating: Dataset/Training_data/Label/press_091.npy  \n", "  inflating: Dataset/Training_data/Label/press_092.npy  \n", "  inflating: Dataset/Training_data/Label/press_094.npy  \n", "  inflating: Dataset/Training_data/Label/press_095.npy  \n", "  inflating: Dataset/Training_data/Label/press_096.npy  \n", "  inflating: Dataset/Training_data/Label/press_097.npy  \n", "  inflating: Dataset/Training_data/Label/press_100.npy  \n", "  inflating: Dataset/Training_data/Label/press_101.npy  \n", "  inflating: Dataset/Training_data/Label/press_102.npy  \n", "  inflating: Dataset/Training_data/Label/press_105.npy  \n", "  inflating: Dataset/Training_data/Label/press_106.npy  \n", "  inflating: Dataset/Training_data/Label/press_107.npy  \n", "  inflating: Dataset/Training_data/Label/press_109.npy  \n", "  inflating: Dataset/Training_data/Label/press_110.npy  \n", "  inflating: Dataset/Training_data/Label/press_111.npy  \n", "  inflating: Dataset/Training_data/Label/press_112.npy  \n", "  inflating: Dataset/Training_data/Label/press_113.npy  \n", "  inflating: Dataset/Training_data/Label/press_114.npy  \n", "  inflating: Dataset/Training_data/Label/press_115.npy  \n", "  inflating: Dataset/Training_data/Label/press_116.npy  \n", "  inflating: Dataset/Training_data/Label/press_117.npy  \n", "  inflating: Dataset/Training_data/Label/press_118.npy  \n", "  inflating: Dataset/Training_data/Label/press_119.npy  \n", "  inflating: Dataset/Training_data/Label/press_120.npy  \n", "  inflating: Dataset/Training_data/Label/press_121.npy  \n", "  inflating: Dataset/Training_data/Label/press_123.npy  \n", "  inflating: Dataset/Training_data/Label/press_124.npy  \n", "  inflating: Dataset/Training_data/Label/press_125.npy  \n", "  inflating: Dataset/Training_data/Label/press_126.npy  \n", "  inflating: Dataset/Training_data/Label/press_127.npy  \n", "  inflating: Dataset/Training_data/Label/press_128.npy  \n", "  inflating: Dataset/Training_data/Label/press_129.npy  \n", "  inflating: Dataset/Training_data/Label/press_130.npy  \n", "  inflating: Dataset/Training_data/Label/press_131.npy  \n", "  inflating: Dataset/Training_data/Label/press_133.npy  \n", "  inflating: Dataset/Training_data/Label/press_134.npy  \n", "  inflating: Dataset/Training_data/Label/press_136.npy  \n", "  inflating: Dataset/Training_data/Label/press_137.npy  \n", "  inflating: Dataset/Training_data/Label/press_138.npy  \n", "  inflating: Dataset/Training_data/Label/press_139.npy  \n", "  inflating: Dataset/Training_data/Label/press_140.npy  \n", "  inflating: Dataset/Training_data/Label/press_141.npy  \n", "  inflating: Dataset/Training_data/Label/press_142.npy  \n", "  inflating: Dataset/Training_data/Label/press_143.npy  \n", "  inflating: Dataset/Training_data/Label/press_144.npy  \n", "  inflating: Dataset/Training_data/Label/press_145.npy  \n", "  inflating: Dataset/Training_data/Label/press_146.npy  \n", "  inflating: Dataset/Training_data/Label/press_147.npy  \n", "  inflating: Dataset/Training_data/Label/press_148.npy  \n", "  inflating: Dataset/Training_data/Label/press_149.npy  \n", "  inflating: Dataset/Training_data/Label/press_150.npy  \n", "  inflating: Dataset/Training_data/Label/press_151.npy  \n", "  inflating: Dataset/Training_data/Label/press_152.npy  \n", "  inflating: Dataset/Training_data/Label/press_153.npy  \n", "  inflating: Dataset/Training_data/Label/press_155.npy  \n", "  inflating: Dataset/Training_data/Label/press_156.npy  \n", "  inflating: Dataset/Training_data/Label/press_157.npy  \n", "  inflating: Dataset/Training_data/Label/press_158.npy  \n", "  inflating: Dataset/Training_data/Label/press_159.npy  \n", "  inflating: Dataset/Training_data/Label/press_160.npy  \n", "  inflating: Dataset/Training_data/Label/press_161.npy  \n", "  inflating: Dataset/Training_data/Label/press_162.npy  \n", "  inflating: Dataset/Training_data/Label/press_163.npy  \n", "  inflating: Dataset/Training_data/Label/press_165.npy  \n", "  inflating: Dataset/Training_data/Label/press_166.npy  \n", "  inflating: Dataset/Training_data/Label/press_170.npy  \n", "  inflating: Dataset/Training_data/Label/press_172.npy  \n", "  inflating: Dataset/Training_data/Label/press_173.npy  \n", "  inflating: Dataset/Training_data/Label/press_175.npy  \n", "  inflating: Dataset/Training_data/Label/press_176.npy  \n", "  inflating: Dataset/Training_data/Label/press_177.npy  \n", "  inflating: Dataset/Training_data/Label/press_178.npy  \n", "  inflating: Dataset/Training_data/Label/press_179.npy  \n", "  inflating: Dataset/Training_data/Label/press_180.npy  \n", "  inflating: Dataset/Training_data/Label/press_181.npy  \n", "  inflating: Dataset/Training_data/Label/press_182.npy  \n", "  inflating: Dataset/Training_data/Label/press_183.npy  \n", "  inflating: Dataset/Training_data/Label/press_184.npy  \n", "  inflating: Dataset/Training_data/Label/press_186.npy  \n", "  inflating: Dataset/Training_data/Label/press_190.npy  \n", "  inflating: Dataset/Training_data/Label/press_191.npy  \n", "  inflating: Dataset/Training_data/Label/press_192.npy  \n", "  inflating: Dataset/Training_data/Label/press_193.npy  \n", "  inflating: Dataset/Training_data/Label/press_195.npy  \n", "  inflating: Dataset/Training_data/Label/press_196.npy  \n", "  inflating: Dataset/Training_data/Label/press_198.npy  \n", "  inflating: Dataset/Training_data/Label/press_199.npy  \n", "  inflating: Dataset/Training_data/Label/press_200.npy  \n", "  inflating: Dataset/Training_data/Label/press_201.npy  \n", "  inflating: Dataset/Training_data/Label/press_202.npy  \n", "  inflating: Dataset/Training_data/Label/press_203.npy  \n", "  inflating: Dataset/Training_data/Label/press_205.npy  \n", "  inflating: Dataset/Training_data/Label/press_207.npy  \n", "  inflating: Dataset/Training_data/Label/press_210.npy  \n", "  inflating: Dataset/Training_data/Label/press_211.npy  \n", "  inflating: Dataset/Training_data/Label/press_212.npy  \n", "  inflating: Dataset/Training_data/Label/press_213.npy  \n", "  inflating: Dataset/Training_data/Label/press_214.npy  \n", "  inflating: Dataset/Training_data/Label/press_215.npy  \n", "  inflating: Dataset/Training_data/Label/press_217.npy  \n", "  inflating: Dataset/Training_data/Label/press_219.npy  \n", "  inflating: Dataset/Training_data/Label/press_220.npy  \n", "  inflating: Dataset/Training_data/Label/press_221.npy  \n", "  inflating: Dataset/Training_data/Label/press_222.npy  \n", "  inflating: Dataset/Training_data/Label/press_223.npy  \n", "  inflating: Dataset/Training_data/Label/press_224.npy  \n", "  inflating: Dataset/Training_data/Label/press_225.npy  \n", "  inflating: Dataset/Training_data/Label/press_227.npy  \n", "  inflating: Dataset/Training_data/Label/press_228.npy  \n", "  inflating: Dataset/Training_data/Label/press_229.npy  \n", "  inflating: Dataset/Training_data/Label/press_230.npy  \n", "  inflating: Dataset/Training_data/Label/press_231.npy  \n", "  inflating: Dataset/Training_data/Label/press_232.npy  \n", "  inflating: Dataset/Training_data/Label/press_233.npy  \n", "  inflating: Dataset/Training_data/Label/press_234.npy  \n", "  inflating: Dataset/Training_data/Label/press_235.npy  \n", "  inflating: Dataset/Training_data/Label/press_236.npy  \n", "  inflating: Dataset/Training_data/Label/press_237.npy  \n", "  inflating: Dataset/Training_data/Label/press_241.npy  \n", "  inflating: Dataset/Training_data/Label/press_243.npy  \n", "  inflating: Dataset/Training_data/Label/press_244.npy  \n", "  inflating: Dataset/Training_data/Label/press_245.npy  \n", "  inflating: Dataset/Training_data/Label/press_246.npy  \n", "  inflating: Dataset/Training_data/Label/press_247.npy  \n", "  inflating: Dataset/Training_data/Label/press_248.npy  \n", "  inflating: Dataset/Training_data/Label/press_249.npy  \n", "  inflating: Dataset/Training_data/Label/press_251.npy  \n", "  inflating: Dataset/Training_data/Label/press_252.npy  \n", "  inflating: Dataset/Training_data/Label/press_253.npy  \n", "  inflating: Dataset/Training_data/Label/press_255.npy  \n", "  inflating: Dataset/Training_data/Label/press_257.npy  \n", "  inflating: Dataset/Training_data/Label/press_258.npy  \n", "  inflating: Dataset/Training_data/Label/press_259.npy  \n", "  inflating: Dataset/Training_data/Label/press_260.npy  \n", "  inflating: Dataset/Training_data/Label/press_261.npy  \n", "  inflating: Dataset/Training_data/Label/press_262.npy  \n", "  inflating: Dataset/Training_data/Label/press_263.npy  \n", "  inflating: Dataset/Training_data/Label/press_264.npy  \n", "  inflating: Dataset/Training_data/Label/press_266.npy  \n", "  inflating: Dataset/Training_data/Label/press_267.npy  \n", "  inflating: Dataset/Training_data/Label/press_268.npy  \n", "  inflating: Dataset/Training_data/Label/press_269.npy  \n", "  inflating: Dataset/Training_data/Label/press_271.npy  \n", "  inflating: Dataset/Training_data/Label/press_272.npy  \n", "  inflating: Dataset/Training_data/Label/press_273.npy  \n", "  inflating: Dataset/Training_data/Label/press_274.npy  \n", "  inflating: Dataset/Training_data/Label/press_275.npy  \n", "  inflating: Dataset/Training_data/Label/press_276.npy  \n", "  inflating: Dataset/Training_data/Label/press_277.npy  \n", "  inflating: Dataset/Training_data/Label/press_278.npy  \n", "  inflating: Dataset/Training_data/Label/press_279.npy  \n", "  inflating: Dataset/Training_data/Label/press_280.npy  \n", "  inflating: Dataset/Training_data/Label/press_281.npy  \n", "  inflating: Dataset/Training_data/Label/press_282.npy  \n", "  inflating: Dataset/Training_data/Label/press_283.npy  \n", "  inflating: Dataset/Training_data/Label/press_285.npy  \n", "  inflating: Dataset/Training_data/Label/press_286.npy  \n", "  inflating: Dataset/Training_data/Label/press_289.npy  \n", "  inflating: Dataset/Training_data/Label/press_290.npy  \n", "  inflating: Dataset/Training_data/Label/press_291.npy  \n", "  inflating: Dataset/Training_data/Label/press_292.npy  \n", "  inflating: Dataset/Training_data/Label/press_293.npy  \n", "  inflating: Dataset/Training_data/Label/press_294.npy  \n", "  inflating: Dataset/Training_data/Label/press_295.npy  \n", "  inflating: Dataset/Training_data/Label/press_296.npy  \n", "  inflating: Dataset/Training_data/Label/press_297.npy  \n", "  inflating: Dataset/Training_data/Label/press_298.npy  \n", "  inflating: Dataset/Training_data/Label/press_299.npy  \n", "  inflating: Dataset/Training_data/Label/press_300.npy  \n", "  inflating: Dataset/Training_data/Label/press_301.npy  \n", "  inflating: Dataset/Training_data/Label/press_302.npy  \n", "  inflating: Dataset/Training_data/Label/press_304.npy  \n", "  inflating: Dataset/Training_data/Label/press_305.npy  \n", "  inflating: Dataset/Training_data/Label/press_306.npy  \n", "  inflating: Dataset/Training_data/Label/press_308.npy  \n", "  inflating: Dataset/Training_data/Label/press_309.npy  \n", "  inflating: Dataset/Training_data/Label/press_310.npy  \n", "  inflating: Dataset/Training_data/Label/press_311.npy  \n", "  inflating: Dataset/Training_data/Label/press_312.npy  \n", "  inflating: Dataset/Training_data/Label/press_313.npy  \n", "  inflating: Dataset/Training_data/Label/press_314.npy  \n", "  inflating: Dataset/Training_data/Label/press_315.npy  \n", "  inflating: Dataset/Training_data/Label/press_319.npy  \n", "  inflating: Dataset/Training_data/Label/press_320.npy  \n", "  inflating: Dataset/Training_data/Label/press_321.npy  \n", "  inflating: Dataset/Training_data/Label/press_322.npy  \n", "  inflating: Dataset/Training_data/Label/press_323.npy  \n", "  inflating: Dataset/Training_data/Label/press_324.npy  \n", "  inflating: Dataset/Training_data/Label/press_325.npy  \n", "  inflating: Dataset/Training_data/Label/press_327.npy  \n", "  inflating: Dataset/Training_data/Label/press_328.npy  \n", "  inflating: Dataset/Training_data/Label/press_329.npy  \n", "  inflating: Dataset/Training_data/Label/press_331.npy  \n", "  inflating: Dataset/Training_data/Label/press_332.npy  \n", "  inflating: Dataset/Training_data/Label/press_333.npy  \n", "  inflating: Dataset/Training_data/Label/press_334.npy  \n", "  inflating: Dataset/Training_data/Label/press_335.npy  \n", "  inflating: Dataset/Training_data/Label/press_337.npy  \n", "  inflating: Dataset/Training_data/Label/press_338.npy  \n", "  inflating: Dataset/Training_data/Label/press_339.npy  \n", "  inflating: Dataset/Training_data/Label/press_340.npy  \n", "  inflating: Dataset/Training_data/Label/press_341.npy  \n", "  inflating: Dataset/Training_data/Label/press_344.npy  \n", "  inflating: Dataset/Training_data/Label/press_345.npy  \n", "  inflating: Dataset/Training_data/Label/press_347.npy  \n", "  inflating: Dataset/Training_data/Label/press_348.npy  \n", "  inflating: Dataset/Training_data/Label/press_349.npy  \n", "  inflating: Dataset/Training_data/Label/press_350.npy  \n", "  inflating: Dataset/Training_data/Label/press_352.npy  \n", "  inflating: Dataset/Training_data/Label/press_353.npy  \n", "  inflating: Dataset/Training_data/Label/press_354.npy  \n", "  inflating: Dataset/Training_data/Label/press_355.npy  \n", "  inflating: Dataset/Training_data/Label/press_356.npy  \n", "  inflating: Dataset/Training_data/Label/press_357.npy  \n", "  inflating: Dataset/Training_data/Label/press_358.npy  \n", "  inflating: Dataset/Training_data/Label/press_360.npy  \n", "  inflating: Dataset/Training_data/Label/press_362.npy  \n", "  inflating: Dataset/Training_data/Label/press_364.npy  \n", "  inflating: Dataset/Training_data/Label/press_365.npy  \n", "  inflating: Dataset/Training_data/Label/press_366.npy  \n", "  inflating: Dataset/Training_data/Label/press_367.npy  \n", "  inflating: Dataset/Training_data/Label/press_369.npy  \n", "  inflating: Dataset/Training_data/Label/press_371.npy  \n", "  inflating: Dataset/Training_data/Label/press_372.npy  \n", "  inflating: Dataset/Training_data/Label/press_373.npy  \n", "  inflating: Dataset/Training_data/Label/press_374.npy  \n", "  inflating: Dataset/Training_data/Label/press_375.npy  \n", "  inflating: Dataset/Training_data/Label/press_376.npy  \n", "  inflating: Dataset/Training_data/Label/press_378.npy  \n", "  inflating: Dataset/Training_data/Label/press_379.npy  \n", "  inflating: Dataset/Training_data/Label/press_380.npy  \n", "  inflating: Dataset/Training_data/Label/press_381.npy  \n", "  inflating: Dataset/Training_data/Label/press_384.npy  \n", "  inflating: Dataset/Training_data/Label/press_385.npy  \n", "  inflating: Dataset/Training_data/Label/press_389.npy  \n", "  inflating: Dataset/Training_data/Label/press_392.npy  \n", "  inflating: Dataset/Training_data/Label/press_393.npy  \n", "  inflating: Dataset/Training_data/Label/press_397.npy  \n", "  inflating: Dataset/Training_data/Label/press_398.npy  \n", "  inflating: Dataset/Training_data/Label/press_399.npy  \n", "  inflating: Dataset/Training_data/Label/press_401.npy  \n", "  inflating: Dataset/Training_data/Label/press_402.npy  \n", "  inflating: Dataset/Training_data/Label/press_403.npy  \n", "  inflating: Dataset/Training_data/Label/press_404.npy  \n", "  inflating: Dataset/Training_data/Label/press_405.npy  \n", "  inflating: Dataset/Training_data/Label/press_407.npy  \n", "  inflating: Dataset/Training_data/Label/press_408.npy  \n", "  inflating: Dataset/Training_data/Label/press_410.npy  \n", "  inflating: Dataset/Training_data/Label/press_412.npy  \n", "  inflating: Dataset/Training_data/Label/press_413.npy  \n", "  inflating: Dataset/Training_data/Label/press_414.npy  \n", "  inflating: Dataset/Training_data/Label/press_415.npy  \n", "  inflating: Dataset/Training_data/Label/press_417.npy  \n", "  inflating: Dataset/Training_data/Label/press_418.npy  \n", "  inflating: Dataset/Training_data/Label/press_419.npy  \n", "  inflating: Dataset/Training_data/Label/press_420.npy  \n", "  inflating: Dataset/Training_data/Label/press_422.npy  \n", "  inflating: Dataset/Training_data/Label/press_424.npy  \n", "  inflating: Dataset/Training_data/Label/press_425.npy  \n", "  inflating: Dataset/Training_data/Label/press_427.npy  \n", "  inflating: Dataset/Training_data/Label/press_430.npy  \n", "  inflating: Dataset/Training_data/Label/press_431.npy  \n", "  inflating: Dataset/Training_data/Label/press_433.npy  \n", "  inflating: Dataset/Training_data/Label/press_435.npy  \n", "  inflating: Dataset/Training_data/Label/press_436.npy  \n", "  inflating: Dataset/Training_data/Label/press_437.npy  \n", "  inflating: Dataset/Training_data/Label/press_439.npy  \n", "  inflating: Dataset/Training_data/Label/press_440.npy  \n", "  inflating: Dataset/Training_data/Label/press_443.npy  \n", "  inflating: Dataset/Training_data/Label/press_444.npy  \n", "  inflating: Dataset/Training_data/Label/press_446.npy  \n", "  inflating: Dataset/Training_data/Label/press_447.npy  \n", "  inflating: Dataset/Training_data/Label/press_448.npy  \n", "  inflating: Dataset/Training_data/Label/press_449.npy  \n", "  inflating: Dataset/Training_data/Label/press_450.npy  \n", "  inflating: Dataset/Training_data/Label/press_451.npy  \n", "  inflating: Dataset/Training_data/Label/press_452.npy  \n", "  inflating: Dataset/Training_data/Label/press_453.npy  \n", "  inflating: Dataset/Training_data/Label/press_454.npy  \n", "  inflating: Dataset/Training_data/Label/press_455.npy  \n", "  inflating: Dataset/Training_data/Label/press_456.npy  \n", "  inflating: Dataset/Training_data/Label/press_457.npy  \n", "  inflating: Dataset/Training_data/Label/press_459.npy  \n", "  inflating: Dataset/Training_data/Label/press_460.npy  \n", "  inflating: Dataset/Training_data/Label/press_462.npy  \n", "  inflating: Dataset/Training_data/Label/press_463.npy  \n", "  inflating: Dataset/Training_data/Label/press_464.npy  \n", "  inflating: Dataset/Training_data/Label/press_465.npy  \n", "  inflating: Dataset/Training_data/Label/press_466.npy  \n", "  inflating: Dataset/Training_data/Label/press_467.npy  \n", "  inflating: Dataset/Training_data/Label/press_468.npy  \n", "  inflating: Dataset/Training_data/Label/press_469.npy  \n", "  inflating: Dataset/Training_data/Label/press_470.npy  \n", "  inflating: Dataset/Training_data/Label/press_472.npy  \n", "  inflating: Dataset/Training_data/Label/press_473.npy  \n", "  inflating: Dataset/Training_data/Label/press_474.npy  \n", "  inflating: Dataset/Training_data/Label/press_475.npy  \n", "  inflating: Dataset/Training_data/Label/press_476.npy  \n", "  inflating: Dataset/Training_data/Label/press_478.npy  \n", "  inflating: Dataset/Training_data/Label/press_479.npy  \n", "  inflating: Dataset/Training_data/Label/press_480.npy  \n", "  inflating: Dataset/Training_data/Label/press_482.npy  \n", "  inflating: Dataset/Training_data/Label/press_483.npy  \n", "  inflating: Dataset/Training_data/Label/press_486.npy  \n", "  inflating: Dataset/Training_data/Label/press_487.npy  \n", "  inflating: Dataset/Training_data/Label/press_488.npy  \n", "  inflating: Dataset/Training_data/Label/press_490.npy  \n", "  inflating: Dataset/Training_data/Label/press_493.npy  \n", "  inflating: Dataset/Training_data/Label/press_494.npy  \n", "  inflating: Dataset/Training_data/Label/press_495.npy  \n", "  inflating: Dataset/Training_data/Label/press_496.npy  \n", "  inflating: Dataset/Training_data/Label/press_497.npy  \n", "  inflating: Dataset/Training_data/Label/press_498.npy  \n", "  inflating: Dataset/Training_data/Label/press_499.npy  \n", "  inflating: Dataset/Training_data/Label/press_501.npy  \n", "  inflating: Dataset/Training_data/Label/press_502.npy  \n", "  inflating: Dataset/Training_data/Label/press_503.npy  \n", "  inflating: Dataset/Training_data/Label/press_504.npy  \n", "  inflating: Dataset/Training_data/Label/press_505.npy  \n", "  inflating: Dataset/Training_data/Label/press_507.npy  \n", "  inflating: Dataset/Training_data/Label/press_508.npy  \n", "  inflating: Dataset/Training_data/Label/press_509.npy  \n", "  inflating: Dataset/Training_data/Label/press_511.npy  \n", "  inflating: Dataset/Training_data/Label/press_512.npy  \n", "  inflating: Dataset/Training_data/Label/press_513.npy  \n", "  inflating: Dataset/Training_data/Label/press_514.npy  \n", "  inflating: Dataset/Training_data/Label/press_515.npy  \n", "  inflating: Dataset/Training_data/Label/press_516.npy  \n", "  inflating: Dataset/Training_data/Label/press_518.npy  \n", "  inflating: Dataset/Training_data/Label/press_519.npy  \n", "  inflating: Dataset/Training_data/Label/press_521.npy  \n", "  inflating: Dataset/Training_data/Label/press_522.npy  \n", "  inflating: Dataset/Training_data/Label/press_523.npy  \n", "  inflating: Dataset/Training_data/Label/press_524.npy  \n", "  inflating: Dataset/Training_data/Label/press_525.npy  \n", "  inflating: Dataset/Training_data/Label/press_527.npy  \n", "  inflating: Dataset/Training_data/Label/press_529.npy  \n", "  inflating: Dataset/Training_data/Label/press_530.npy  \n", "  inflating: Dataset/Training_data/Label/press_532.npy  \n", "  inflating: Dataset/Training_data/Label/press_533.npy  \n", "  inflating: Dataset/Training_data/Label/press_536.npy  \n", "  inflating: Dataset/Training_data/Label/press_538.npy  \n", "  inflating: Dataset/Training_data/Label/press_539.npy  \n", "  inflating: Dataset/Training_data/Label/press_540.npy  \n", "  inflating: Dataset/Training_data/Label/press_542.npy  \n", "  inflating: Dataset/Training_data/Label/press_543.npy  \n", "  inflating: Dataset/Training_data/Label/press_545.npy  \n", "  inflating: Dataset/Training_data/Label/press_547.npy  \n", "  inflating: Dataset/Training_data/Label/press_548.npy  \n", "  inflating: Dataset/Training_data/Label/press_549.npy  \n", "  inflating: Dataset/Training_data/Label/press_550.npy  \n", "  inflating: Dataset/Training_data/Label/press_551.npy  \n", "  inflating: Dataset/Training_data/Label/press_552.npy  \n", "  inflating: Dataset/Training_data/Label/press_553.npy  \n", "  inflating: Dataset/Training_data/Label/press_554.npy  \n", "  inflating: Dataset/Training_data/Label/press_555.npy  \n", "  inflating: Dataset/Training_data/Label/press_560.npy  \n", "  inflating: Dataset/Training_data/Label/press_561.npy  \n", "  inflating: Dataset/Training_data/Label/press_562.npy  \n", "  inflating: Dataset/Training_data/Label/press_564.npy  \n", "  inflating: Dataset/Training_data/Label/press_565.npy  \n", "  inflating: Dataset/Training_data/Label/press_566.npy  \n", "  inflating: Dataset/Training_data/Label/press_567.npy  \n", "  inflating: Dataset/Training_data/Label/press_568.npy  \n", "  inflating: Dataset/Training_data/Label/press_569.npy  \n", "  inflating: Dataset/Training_data/Label/press_572.npy  \n", "  inflating: Dataset/Training_data/Label/press_573.npy  \n", "  inflating: Dataset/Training_data/Label/press_574.npy  \n", "  inflating: Dataset/Training_data/Label/press_576.npy  \n", "  inflating: Dataset/Training_data/Label/press_577.npy  \n", "  inflating: Dataset/Training_data/Label/press_579.npy  \n", "  inflating: Dataset/Training_data/Label/press_581.npy  \n", "  inflating: Dataset/Training_data/Label/press_582.npy  \n", "  inflating: Dataset/Training_data/Label/press_583.npy  \n", "  inflating: Dataset/Training_data/Label/press_584.npy  \n", "  inflating: Dataset/Training_data/Label/press_587.npy  \n", "  inflating: Dataset/Training_data/Label/press_588.npy  \n", "  inflating: Dataset/Training_data/Label/press_589.npy  \n", "  inflating: Dataset/Training_data/Label/press_591.npy  \n", "  inflating: Dataset/Training_data/Label/press_593.npy  \n", "  inflating: Dataset/Training_data/Label/press_594.npy  \n", "  inflating: Dataset/Training_data/Label/press_595.npy  \n", "  inflating: Dataset/Training_data/Label/press_596.npy  \n", "  inflating: Dataset/Training_data/Label/press_597.npy  \n", "  inflating: Dataset/Training_data/Label/press_598.npy  \n", "  inflating: Dataset/Training_data/Label/press_600.npy  \n", "  inflating: Dataset/Training_data/Label/press_602.npy  \n", "  inflating: Dataset/Training_data/Label/press_604.npy  \n", "  inflating: Dataset/Training_data/Label/press_608.npy  \n", "  inflating: Dataset/Training_data/Label/press_610.npy  \n", "  inflating: Dataset/Training_data/Label/press_611.npy  \n", "  inflating: Dataset/Training_data/Label/press_612.npy  \n", "  inflating: Dataset/Training_data/Label/press_613.npy  \n", "  inflating: Dataset/Training_data/Label/press_615.npy  \n", "  inflating: Dataset/Training_data/Label/press_616.npy  \n", "  inflating: Dataset/Training_data/Label/press_617.npy  \n", "  inflating: Dataset/Training_data/Label/press_618.npy  \n", "  inflating: Dataset/Training_data/Label/press_620.npy  \n", "  inflating: Dataset/Training_data/Label/press_621.npy  \n", "  inflating: Dataset/Training_data/Label/press_622.npy  \n", "  inflating: Dataset/Training_data/Label/press_623.npy  \n", "  inflating: Dataset/Training_data/Label/press_625.npy  \n", "  inflating: Dataset/Training_data/Label/press_626.npy  \n", "  inflating: Dataset/Training_data/Label/press_627.npy  \n", "  inflating: Dataset/Training_data/Label/press_628.npy  \n", "  inflating: Dataset/Training_data/Label/press_629.npy  \n", "  inflating: Dataset/Training_data/Label/press_630.npy  \n", "  inflating: Dataset/Training_data/Label/press_631.npy  \n", "  inflating: Dataset/Training_data/Label/press_632.npy  \n", "  inflating: Dataset/Training_data/Label/press_633.npy  \n", "  inflating: Dataset/Training_data/Label/press_634.npy  \n", "  inflating: Dataset/Training_data/Label/press_635.npy  \n", "  inflating: Dataset/Training_data/Label/press_636.npy  \n", "  inflating: Dataset/Training_data/Label/press_638.npy  \n", "  inflating: Dataset/Training_data/Label/press_639.npy  \n", "  inflating: Dataset/Training_data/Label/press_640.npy  \n", "  inflating: Dataset/Training_data/Label/press_641.npy  \n", "  inflating: Dataset/Training_data/Label/press_642.npy  \n", "  inflating: Dataset/Training_data/Label/press_643.npy  \n", "  inflating: Dataset/Training_data/Label/press_644.npy  \n", "  inflating: Dataset/Training_data/Label/press_645.npy  \n", "  inflating: Dataset/Training_data/Label/press_646.npy  \n", "  inflating: Dataset/Training_data/Label/press_647.npy  \n", "  inflating: Dataset/Training_data/Label/press_648.npy  \n", "  inflating: Dataset/Training_data/Label/press_649.npy  \n", "  inflating: Dataset/Training_data/Label/press_651.npy  \n", "  inflating: Dataset/Training_data/Label/press_652.npy  \n", "  inflating: Dataset/Training_data/Label/press_654.npy  \n", "  inflating: Dataset/Training_data/Label/press_655.npy  \n", "  inflating: Dataset/Training_data/Label/press_656.npy  \n", "  inflating: Dataset/Training_data/Label/press_657.npy  \n", "  inflating: Dataset/Training_data/train_pressure_min_std.txt  \n", "  inflating: Dataset/Training_data/watertight_global_bounds.txt  \n", "  inflating: Dataset/Training_data/watertight_meshes.txt  \n"]}], "source": ["####下载Dataset.zip\n", "!wget --header=\"Host: drive.usercontent.google.com\" --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\" --header=\"Cookie: __Secure-ENID=12.SE=Yd0Bj-CLJ14fnd4qzdJHmwUs4B5zz46UaPC1cPJigNqqFV9PtM2CYyBpSbCkOyzUwzlEdZ1nZFf-igtGi7wSdJ_gqQSfQfh84r9egqFQAy9-GKayCRbdQKdera-2mkpuIT-c64CyR9vfNojM3hxZ9Dej-dGvtxlGjal9ttEHybw; __gsas=ID=ae0421b9a34b478c:T=1710758437:RT=1710758437:S=ALNI_MZP13R9ZOHbCzC0rgHSMrGXj6GCsg; HSID=A-4I-ZudDNUIB6EKH; SSID=A7v_1v9un6xAwVNku; APISID=ctK8IbLjeuDUmgys/AFnMSLWt9KddceDI6; SAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; __Secure-1PAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; __Secure-3PAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; SID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_kzuBV1TvOhAIC8VF1e9fpgACgYKATQSARQSFQHGX2Mi8LXUwWoIwNCEPU8Sy3mXUxoVAUF8yKqGXVfjTGz9gQal7nwGr4Pl0076; __Secure-1PSID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_PDa-DzVmbdGFPyxMQpk9_QACgYKAewSARQSFQHGX2MiAeee4fn0OWglWZfAygqkyBoVAUF8yKp-Sfmtnueimxc-0QbJRF9I0076; __Secure-3PSID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_g9IrMeU98APBo9Stp6wEnAACgYKAQASARQSFQHGX2MiFWtc9ucONXnpxBzlRdudEhoVAUF8yKoeZwCpJDnjfAFjGssHSUGm0076; NID=515=GQhY9nKKFCx3qFDjE0MA4ubjWNdef6xCIY_RfWOPWKEtyfBN3nAUl8WHI2VczjNQ4rVkj1XBAY8WNWHXyqSK10CfT4FxsFlPzrHIJpeTtm1nWRNBd9AAfBKJHz4XpESszntVUTE_59RklZuKo0vk1poReVi2da1PZKC3CTKH2Ll3gB5xuB9wf4bmq8ylVUuIROPJczr0XnCuUHV3qLdBvgy9_870b6UwOq1iOlIxFQFm01EZ4pqF4q1Ub3QRSWpEMLh4LSZFpJ5O255R5OV7krmEdDvH_sHoTEPZAg2PoEpwAyGK6Xp9qcLIlldgx5-5V86N8Wtb93uTlQuA_CFXb5_2eP3bgeX8txwlJ5SrldVjg9ctzYtBU2RwJKTSvdHfIG7lpOkg6XlkvDOcJpR3DihT_OlqnPn7drCAJpvVDv29hZn5XPMXaSrNdbG64OJ9urJEw5odEwsLYkkpC1vmlUcuoo52S5f6RQu0Z8kZiV8iRW6XIqHsSmQHunVaxk6xWCStUg; __Secure-1PSIDTS=sidts-CjEB3EgAEtTS0OazynCofIH4RCBstiRP5flEcvYW3z4Fg9oGd5QOESDOZt1wO2iqUYHjEAA; __Secure-3PSIDTS=sidts-CjEB3EgAEtTS0OazynCofIH4RCBstiRP5flEcvYW3z4Fg9oGd5QOESDOZt1wO2iqUYHjEAA; SIDCC=AKEyXzVI6aMX8lSDja86Yts3FBAtBzPCzVNgaX5BCz78NWsWzlT3yFWKUV7ZE46SFzE1GiBI-cHdTw; __Secure-1PSIDCC=AKEyXzUo4NQAwqqPMxP2eye-MFEbZmBIm_sZqRU1amttg0YoQkc8ZKSNXdHl5jNCMEbhrUHhS9-K; __Secure-3PSIDCC=AKEyXzWf2lIdmDLeZKpXSi9GytVQb6XudrYiNUBA5gW952YuLh8kL6T3IbBlu8zOTfGEcdUp5O1R\" --header=\"Connection: keep-alive\" \"https://drive.usercontent.google.com/download?id=1JwR0Q1ArTg6c47EF2ZuIBpQwCPgXKrO2&export=download&authuser=0&confirm=t&uuid=dc3aa13c-c3a9-458f-983a-8586798cb635&at=APZUnTX25XMxi-z-3wBcgR93IGsL%3A1719235792953\" -c -O 'Dataset.zip'\n", "####解压Dataset.zip\n", "!unzip Dataset.zip\n", "####删除Dataset.zip\n", "!rm Dataset.zip\n", "####重命名Training_data文件名\n", "!mv Dataset/Training_data Dataset/Trainset_track_A"]}, {"cell_type": "markdown", "metadata": {"id": "hjHXYCzgw2v5"}, "source": ["赛道二"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "executionInfo": {"elapsed": 231508, "status": "ok", "timestamp": 1720765946210, "user": {"displayName": "<PERSON><PERSON>", "userId": "04820485600131748919"}, "user_tz": -480}, "id": "B88H3zRrnfil", "outputId": "b9243c63-5134-40a3-846b-910ab4a657b5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2024-07-12 06:28:36--  https://ai-studio-online.bj.bcebos.com/v1/38e9adf0fce84527aad3558cc3e82d0e9a251aac4c934297afae9b74d9b3d1e9?responseContentDisposition=attachment%3B%20filename%3Dtrain_track_B.zip&authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2024-06-04T03%3A21%3A02Z%2F-1%2F%2Facd359add161bace603a52c7a268467406cb3c1889a7114bbb687de8002b55f6\n", "Resolving ai-studio-online.bj.bcebos.com (ai-studio-online.bj.bcebos.com)... **************, 2409:8c04:1001:1203:0:ff:b0bb:4f27\n", "Connecting to ai-studio-online.bj.bcebos.com (ai-studio-online.bj.bcebos.com)|**************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 4740031429 (4.4G) [application/octet-stream]\n", "Saving to: ‘train_track_B.zip’\n", "\n", "train_track_B.zip   100%[===================>]   4.41G  27.9MB/s    in 2m 48s  \n", "\n", "2024-07-12 06:31:25 (26.9 MB/s) - ‘train_track_B.zip’ saved [4740031429/4740031429]\n", "\n", "Archive:  train_track_B.zip\n", "  inflating: area_0002.npy           \n", "  inflating: area_0003.npy           \n", "  inflating: area_0004.npy           \n", "  inflating: area_0005.npy           \n", "  inflating: area_0006.npy           \n", "  inflating: area_0011.npy           \n", "  inflating: area_0012.npy           \n", "  inflating: area_0013.npy           \n", "  inflating: area_0015.npy           \n", "  inflating: area_0017.npy           \n", "  inflating: area_0018.npy           \n", "  inflating: area_0020.npy           \n", "  inflating: area_0021.npy           \n", "  inflating: area_0022.npy           \n", "  inflating: area_0023.npy           \n", "  inflating: area_0024.npy           \n", "  inflating: area_0026.npy           \n", "  inflating: area_0029.npy           \n", "  inflating: area_0030.npy           \n", "  inflating: area_0036.npy           \n", "  inflating: area_0037.npy           \n", "  inflating: area_0038.npy           \n", "  inflating: area_0039.npy           \n", "  inflating: area_0040.npy           \n", "  inflating: area_0041.npy           \n", "  inflating: area_0042.npy           \n", "  inflating: area_0043.npy           \n", "  inflating: area_0044.npy           \n", "  inflating: area_0048.npy           \n", "  inflating: area_0049.npy           \n", "  inflating: area_0051.npy           \n", "  inflating: area_0052.npy           \n", "  inflating: area_0055.npy           \n", "  inflating: area_0056.npy           \n", "  inflating: area_0057.npy           \n", "  inflating: area_0059.npy           \n", "  inflating: area_0062.npy           \n", "  inflating: area_0064.npy           \n", "  inflating: area_0066.npy           \n", "  inflating: area_0067.npy           \n", "  inflating: area_0068.npy           \n", "  inflating: area_0071.npy           \n", "  inflating: area_0074.npy           \n", "  inflating: area_0075.npy           \n", "  inflating: area_0077.npy           \n", "  inflating: area_0078.npy           \n", "  inflating: area_0080.npy           \n", "  inflating: area_0081.npy           \n", "  inflating: area_0082.npy           \n", "  inflating: area_0084.npy           \n", "  inflating: area_0085.npy           \n", "  inflating: area_0086.npy           \n", "  inflating: area_0087.npy           \n", "  inflating: area_0088.npy           \n", "  inflating: area_0089.npy           \n", "  inflating: area_0090.npy           \n", "  inflating: area_0092.npy           \n", "  inflating: area_0093.npy           \n", "  inflating: area_0094.npy           \n", "  inflating: area_0095.npy           \n", "  inflating: area_0097.npy           \n", "  inflating: area_0098.npy           \n", "  inflating: area_0100.npy           \n", "  inflating: area_0101.npy           \n", "  inflating: area_0102.npy           \n", "  inflating: area_0103.npy           \n", "  inflating: area_0104.npy           \n", "  inflating: area_0106.npy           \n", "  inflating: area_0107.npy           \n", "  inflating: area_0108.npy           \n", "  inflating: area_0109.npy           \n", "  inflating: area_0110.npy           \n", "  inflating: area_0113.npy           \n", "  inflating: area_0114.npy           \n", "  inflating: area_0115.npy           \n", "  inflating: area_0116.npy           \n", "  inflating: area_0117.npy           \n", "  inflating: area_0118.npy           \n", "  inflating: area_0119.npy           \n", "  inflating: area_0120.npy           \n", "  inflating: area_0121.npy           \n", "  inflating: area_0122.npy           \n", "  inflating: area_0124.npy           \n", "  inflating: area_0125.npy           \n", "  inflating: area_0126.npy           \n", "  inflating: area_0128.npy           \n", "  inflating: area_0129.npy           \n", "  inflating: area_0130.npy           \n", "  inflating: area_0131.npy           \n", "  inflating: area_0132.npy           \n", "  inflating: area_0133.npy           \n", "  inflating: area_0134.npy           \n", "  inflating: area_0135.npy           \n", "  inflating: area_0136.npy           \n", "  inflating: area_0138.npy           \n", "  inflating: area_0139.npy           \n", "  inflating: area_0140.npy           \n", "  inflating: area_0141.npy           \n", "  inflating: area_0143.npy           \n", "  inflating: area_0145.npy           \n", "  inflating: area_0146.npy           \n", "  inflating: area_0148.npy           \n", "  inflating: area_0149.npy           \n", "  inflating: area_0150.npy           \n", "  inflating: area_0151.npy           \n", "  inflating: area_0153.npy           \n", "  inflating: area_0154.npy           \n", "  inflating: area_0156.npy           \n", "  inflating: area_0157.npy           \n", "  inflating: area_0158.npy           \n", "  inflating: area_0161.npy           \n", "  inflating: area_0162.npy           \n", "  inflating: area_0163.npy           \n", "  inflating: area_0164.npy           \n", "  inflating: area_0166.npy           \n", "  inflating: area_0167.npy           \n", "  inflating: area_0168.npy           \n", "  inflating: area_0170.npy           \n", "  inflating: area_0171.npy           \n", "  inflating: area_0172.npy           \n", "  inflating: area_0174.npy           \n", "  inflating: area_0175.npy           \n", "  inflating: area_0183.npy           \n", "  inflating: area_0184.npy           \n", "  inflating: area_0185.npy           \n", "  inflating: area_0189.npy           \n", "  inflating: area_0190.npy           \n", "  inflating: area_0193.npy           \n", "  inflating: area_0194.npy           \n", "  inflating: area_0195.npy           \n", "  inflating: area_0197.npy           \n", "  inflating: area_0201.npy           \n", "  inflating: area_0203.npy           \n", "  inflating: area_0204.npy           \n", "  inflating: area_0205.npy           \n", "  inflating: area_0206.npy           \n", "  inflating: area_0208.npy           \n", "  inflating: area_0210.npy           \n", "  inflating: area_0211.npy           \n", "  inflating: area_0216.npy           \n", "  inflating: area_0217.npy           \n", "  inflating: area_0219.npy           \n", "  inflating: area_0220.npy           \n", "  inflating: area_0227.npy           \n", "  inflating: area_0228.npy           \n", "  inflating: area_0229.npy           \n", "  inflating: area_0232.npy           \n", "  inflating: area_0234.npy           \n", "  inflating: area_0235.npy           \n", "  inflating: area_0236.npy           \n", "  inflating: area_0238.npy           \n", "  inflating: area_0239.npy           \n", "  inflating: area_0240.npy           \n", "  inflating: area_0241.npy           \n", "  inflating: area_0245.npy           \n", "  inflating: area_0246.npy           \n", "  inflating: area_0247.npy           \n", "  inflating: area_0248.npy           \n", "  inflating: area_0249.npy           \n", "  inflating: area_0252.npy           \n", "  inflating: area_0253.npy           \n", "  inflating: area_0254.npy           \n", "  inflating: area_0256.npy           \n", "  inflating: area_0257.npy           \n", "  inflating: area_0259.npy           \n", "  inflating: area_0264.npy           \n", "  inflating: area_0265.npy           \n", "  inflating: area_0266.npy           \n", "  inflating: area_0268.npy           \n", "  inflating: area_0269.npy           \n", "  inflating: area_0271.npy           \n", "  inflating: area_0272.npy           \n", "  inflating: area_0273.npy           \n", "  inflating: area_0275.npy           \n", "  inflating: area_0276.npy           \n", "  inflating: area_0277.npy           \n", "  inflating: area_0279.npy           \n", "  inflating: area_0280.npy           \n", "  inflating: area_0281.npy           \n", "  inflating: area_0284.npy           \n", "  inflating: area_0285.npy           \n", "  inflating: area_0286.npy           \n", "  inflating: area_0288.npy           \n", "  inflating: area_0289.npy           \n", "  inflating: area_0290.npy           \n", "  inflating: area_0291.npy           \n", "  inflating: area_0294.npy           \n", "  inflating: area_0296.npy           \n", "  inflating: area_0297.npy           \n", "  inflating: area_0298.npy           \n", "  inflating: area_0301.npy           \n", "  inflating: area_0304.npy           \n", "  inflating: area_0305.npy           \n", "  inflating: area_0306.npy           \n", "  inflating: area_0307.npy           \n", "  inflating: area_0308.npy           \n", "  inflating: area_0310.npy           \n", "  inflating: area_0311.npy           \n", "  inflating: area_0314.npy           \n", "  inflating: area_0315.npy           \n", "  inflating: area_0316.npy           \n", "  inflating: area_0320.npy           \n", "  inflating: area_0321.npy           \n", "  inflating: area_0323.npy           \n", "  inflating: area_0324.npy           \n", "  inflating: area_0327.npy           \n", "  inflating: area_0330.npy           \n", "  inflating: area_0331.npy           \n", "  inflating: area_0332.npy           \n", "  inflating: area_0333.npy           \n", "  inflating: area_0334.npy           \n", "  inflating: area_0337.npy           \n", "  inflating: area_0338.npy           \n", "  inflating: area_0339.npy           \n", "  inflating: area_0340.npy           \n", "  inflating: area_0341.npy           \n", "  inflating: area_0342.npy           \n", "  inflating: area_0343.npy           \n", "  inflating: area_0344.npy           \n", "  inflating: area_0345.npy           \n", "  inflating: area_0346.npy           \n", "  inflating: area_0348.npy           \n", "  inflating: area_0349.npy           \n", "  inflating: area_0351.npy           \n", "  inflating: area_0352.npy           \n", "  inflating: area_0353.npy           \n", "  inflating: area_0354.npy           \n", "  inflating: area_0356.npy           \n", "  inflating: area_0357.npy           \n", "  inflating: area_0359.npy           \n", "  inflating: area_0360.npy           \n", "  inflating: area_0361.npy           \n", "  inflating: area_0363.npy           \n", "  inflating: area_0364.npy           \n", "  inflating: area_0365.npy           \n", "  inflating: area_0366.npy           \n", "  inflating: area_0367.npy           \n", "  inflating: area_0368.npy           \n", "  inflating: area_0369.npy           \n", "  inflating: area_0371.npy           \n", "  inflating: area_0373.npy           \n", "  inflating: area_0376.npy           \n", "  inflating: area_0377.npy           \n", "  inflating: area_0378.npy           \n", "  inflating: area_0379.npy           \n", "  inflating: area_0381.npy           \n", "  inflating: area_0382.npy           \n", "  inflating: area_0383.npy           \n", "  inflating: area_0384.npy           \n", "  inflating: area_0385.npy           \n", "  inflating: area_0387.npy           \n", "  inflating: area_0388.npy           \n", "  inflating: area_0389.npy           \n", "  inflating: area_0392.npy           \n", "  inflating: area_0393.npy           \n", "  inflating: area_0394.npy           \n", "  inflating: area_0395.npy           \n", "  inflating: area_0396.npy           \n", "  inflating: area_0398.npy           \n", "  inflating: area_0399.npy           \n", "  inflating: area_0400.npy           \n", "  inflating: area_0401.npy           \n", "  inflating: area_0402.npy           \n", "  inflating: area_0403.npy           \n", "  inflating: area_0404.npy           \n", "  inflating: area_0405.npy           \n", "  inflating: area_0407.npy           \n", "  inflating: area_0408.npy           \n", "  inflating: area_0409.npy           \n", "  inflating: area_0410.npy           \n", "  inflating: area_0411.npy           \n", "  inflating: area_0413.npy           \n", "  inflating: area_0416.npy           \n", "  inflating: area_0417.npy           \n", "  inflating: area_0421.npy           \n", "  inflating: area_0422.npy           \n", "  inflating: area_0423.npy           \n", "  inflating: area_0424.npy           \n", "  inflating: area_0425.npy           \n", "  inflating: area_0428.npy           \n", "  inflating: area_0429.npy           \n", "  inflating: area_0430.npy           \n", "  inflating: area_0431.npy           \n", "  inflating: area_0432.npy           \n", "  inflating: area_0435.npy           \n", "  inflating: area_0438.npy           \n", "  inflating: area_0439.npy           \n", "  inflating: area_0441.npy           \n", "  inflating: area_0444.npy           \n", "  inflating: area_0445.npy           \n", "  inflating: area_0449.npy           \n", "  inflating: area_0450.npy           \n", "  inflating: area_0451.npy           \n", "  inflating: area_0452.npy           \n", "  inflating: area_0453.npy           \n", "  inflating: area_0456.npy           \n", "  inflating: area_0457.npy           \n", "  inflating: area_0458.npy           \n", "  inflating: area_0459.npy           \n", "  inflating: area_0460.npy           \n", "  inflating: area_0461.npy           \n", "  inflating: area_0463.npy           \n", "  inflating: area_0464.npy           \n", "  inflating: area_0465.npy           \n", "  inflating: area_0467.npy           \n", "  inflating: area_0469.npy           \n", "  inflating: area_0471.npy           \n", "  inflating: area_0472.npy           \n", "  inflating: area_0474.npy           \n", "  inflating: area_0475.npy           \n", "  inflating: area_0477.npy           \n", "  inflating: area_0478.npy           \n", "  inflating: area_0479.npy           \n", "  inflating: area_0480.npy           \n", "  inflating: area_0481.npy           \n", "  inflating: area_0482.npy           \n", "  inflating: area_0485.npy           \n", "  inflating: area_0486.npy           \n", "  inflating: area_0487.npy           \n", "  inflating: area_0488.npy           \n", "  inflating: area_0489.npy           \n", "  inflating: area_0492.npy           \n", "  inflating: area_0493.npy           \n", "  inflating: area_0494.npy           \n", "  inflating: area_0497.npy           \n", "  inflating: area_0498.npy           \n", "  inflating: area_0499.npy           \n", "  inflating: area_0501.npy           \n", "  inflating: area_0502.npy           \n", "  inflating: area_0503.npy           \n", "  inflating: area_0504.npy           \n", "  inflating: area_0507.npy           \n", "  inflating: area_0508.npy           \n", "  inflating: area_0509.npy           \n", "  inflating: area_0513.npy           \n", "  inflating: area_0514.npy           \n", "  inflating: area_0515.npy           \n", "  inflating: area_0517.npy           \n", "  inflating: area_0518.npy           \n", "  inflating: area_0519.npy           \n", "  inflating: area_0520.npy           \n", "  inflating: area_0521.npy           \n", "  inflating: area_0522.npy           \n", "  inflating: area_0523.npy           \n", "  inflating: area_0524.npy           \n", "  inflating: area_0525.npy           \n", "  inflating: area_0526.npy           \n", "  inflating: area_0527.npy           \n", "  inflating: area_0528.npy           \n", "  inflating: area_0529.npy           \n", "  inflating: area_0530.npy           \n", "  inflating: area_0531.npy           \n", "  inflating: area_0534.npy           \n", "  inflating: area_0535.npy           \n", "  inflating: area_0536.npy           \n", "  inflating: area_0538.npy           \n", "  inflating: area_0541.npy           \n", "  inflating: area_0542.npy           \n", "  inflating: area_0544.npy           \n", "  inflating: area_0545.npy           \n", "  inflating: area_0546.npy           \n", "  inflating: area_0547.npy           \n", "  inflating: area_0550.npy           \n", "  inflating: area_0551.npy           \n", "  inflating: area_0553.npy           \n", "  inflating: area_0555.npy           \n", "  inflating: area_0557.npy           \n", "  inflating: area_0558.npy           \n", "  inflating: area_0561.npy           \n", "  inflating: area_0563.npy           \n", "  inflating: area_0564.npy           \n", "  inflating: area_0565.npy           \n", "  inflating: area_0567.npy           \n", "  inflating: area_0568.npy           \n", "  inflating: area_0571.npy           \n", "  inflating: area_0574.npy           \n", "  inflating: area_0576.npy           \n", "  inflating: area_0579.npy           \n", "  inflating: area_0580.npy           \n", "  inflating: area_0582.npy           \n", "  inflating: area_0584.npy           \n", "  inflating: area_0585.npy           \n", "  inflating: area_0588.npy           \n", "  inflating: area_0589.npy           \n", "  inflating: area_0590.npy           \n", "  inflating: area_0591.npy           \n", "  inflating: area_0592.npy           \n", "  inflating: area_0593.npy           \n", "  inflating: area_0594.npy           \n", "  inflating: area_0595.npy           \n", "  inflating: area_0596.npy           \n", "  inflating: area_0597.npy           \n", "  inflating: area_0598.npy           \n", "  inflating: area_0600.npy           \n", "  inflating: area_0602.npy           \n", "  inflating: area_0605.npy           \n", "  inflating: area_0608.npy           \n", "  inflating: area_0609.npy           \n", "  inflating: area_0611.npy           \n", "  inflating: area_0612.npy           \n", "  inflating: area_0613.npy           \n", "  inflating: area_0614.npy           \n", "  inflating: area_0618.npy           \n", "  inflating: area_0619.npy           \n", "  inflating: area_0620.npy           \n", "  inflating: area_0621.npy           \n", "  inflating: area_0622.npy           \n", "  inflating: area_0623.npy           \n", "  inflating: area_0624.npy           \n", "  inflating: area_0625.npy           \n", "  inflating: area_0627.npy           \n", "  inflating: area_0628.npy           \n", "  inflating: area_0629.npy           \n", "  inflating: area_0630.npy           \n", "  inflating: area_0631.npy           \n", "  inflating: area_0632.npy           \n", "  inflating: area_0633.npy           \n", "  inflating: area_0634.npy           \n", "  inflating: area_0635.npy           \n", "  inflating: area_0637.npy           \n", "  inflating: area_0638.npy           \n", "  inflating: area_0639.npy           \n", "  inflating: area_0640.npy           \n", "  inflating: area_0641.npy           \n", "  inflating: area_0643.npy           \n", "  inflating: area_0644.npy           \n", "  inflating: area_0645.npy           \n", "  inflating: area_0646.npy           \n", "  inflating: area_0648.npy           \n", "  inflating: area_0650.npy           \n", "  inflating: area_0651.npy           \n", "  inflating: area_0652.npy           \n", "  inflating: area_0653.npy           \n", "  inflating: area_0654.npy           \n", "  inflating: area_0656.npy           \n", "  inflating: area_0657.npy           \n", "  inflating: area_0658.npy           \n", "  inflating: area_0661.npy           \n", "  inflating: area_0663.npy           \n", "  inflating: area_0664.npy           \n", "  inflating: area_0665.npy           \n", "  inflating: area_0666.npy           \n", "  inflating: area_0667.npy           \n", "  inflating: area_0668.npy           \n", "  inflating: area_0669.npy           \n", "  inflating: area_0671.npy           \n", "  inflating: area_0672.npy           \n", "  inflating: area_0673.npy           \n", "  inflating: area_0674.npy           \n", "  inflating: area_0676.npy           \n", "  inflating: area_0677.npy           \n", "  inflating: area_0678.npy           \n", "  inflating: area_0679.npy           \n", "  inflating: area_0680.npy           \n", "  inflating: area_0682.npy           \n", "  inflating: area_0686.npy           \n", "  inflating: area_0688.npy           \n", "  inflating: area_0689.npy           \n", "  inflating: area_0690.npy           \n", "  inflating: area_0691.npy           \n", "  inflating: area_0692.npy           \n", "  inflating: area_0693.npy           \n", "  inflating: area_0694.npy           \n", "  inflating: area_0695.npy           \n", "  inflating: area_0697.npy           \n", "  inflating: area_0699.npy           \n", "  inflating: area_0700.npy           \n", "  inflating: area_0701.npy           \n", "  inflating: area_0703.npy           \n", "  inflating: area_0704.npy           \n", "  inflating: area_0706.npy           \n", "  inflating: area_0707.npy           \n", "  inflating: area_0708.npy           \n", "  inflating: area_0709.npy           \n", "  inflating: area_0711.npy           \n", "  inflating: area_0712.npy           \n", "  inflating: area_0713.npy           \n", "  inflating: area_0714.npy           \n", "  inflating: area_0715.npy           \n", "  inflating: area_0716.npy           \n", "  inflating: area_0718.npy           \n", "  inflating: area_0719.npy           \n", "  inflating: area_0720.npy           \n", "  inflating: area_0721.npy           \n", "  inflating: area_0722.npy           \n", "  inflating: area_0724.npy           \n", "  inflating: area_0727.npy           \n", "  inflating: area_0728.npy           \n", "  inflating: area_0729.npy           \n", "  inflating: area_0730.npy           \n", "  inflating: area_0731.npy           \n", "  inflating: area_0733.npy           \n", "  inflating: area_0735.npy           \n", "  inflating: area_0736.npy           \n", "  inflating: area_0737.npy           \n", "  inflating: area_0740.npy           \n", "  inflating: area_0742.npy           \n", "  inflating: area_0743.npy           \n", "  inflating: area_0744.npy           \n", "  inflating: area_0745.npy           \n", "  inflating: centroid_0002.npy       \n", "  inflating: centroid_0003.npy       \n", "  inflating: centroid_0004.npy       \n", "  inflating: centroid_0005.npy       \n", "  inflating: centroid_0006.npy       \n", "  inflating: centroid_0011.npy       \n", "  inflating: centroid_0012.npy       \n", "  inflating: centroid_0013.npy       \n", "  inflating: centroid_0015.npy       \n", "  inflating: centroid_0017.npy       \n", "  inflating: centroid_0018.npy       \n", "  inflating: centroid_0020.npy       \n", "  inflating: centroid_0021.npy       \n", "  inflating: centroid_0022.npy       \n", "  inflating: centroid_0023.npy       \n", "  inflating: centroid_0024.npy       \n", "  inflating: centroid_0026.npy       \n", "  inflating: centroid_0029.npy       \n", "  inflating: centroid_0030.npy       \n", "  inflating: centroid_0036.npy       \n", "  inflating: centroid_0037.npy       \n", "  inflating: centroid_0038.npy       \n", "  inflating: centroid_0039.npy       \n", "  inflating: centroid_0040.npy       \n", "  inflating: centroid_0041.npy       \n", "  inflating: centroid_0042.npy       \n", "  inflating: centroid_0043.npy       \n", "  inflating: centroid_0044.npy       \n", "  inflating: centroid_0048.npy       \n", "  inflating: centroid_0049.npy       \n", "  inflating: centroid_0051.npy       \n", "  inflating: centroid_0052.npy       \n", "  inflating: centroid_0055.npy       \n", "  inflating: centroid_0056.npy       \n", "  inflating: centroid_0057.npy       \n", "  inflating: centroid_0059.npy       \n", "  inflating: centroid_0062.npy       \n", "  inflating: centroid_0064.npy       \n", "  inflating: centroid_0066.npy       \n", "  inflating: centroid_0067.npy       \n", "  inflating: centroid_0068.npy       \n", "  inflating: centroid_0071.npy       \n", "  inflating: centroid_0074.npy       \n", "  inflating: centroid_0075.npy       \n", "  inflating: centroid_0077.npy       \n", "  inflating: centroid_0078.npy       \n", "  inflating: centroid_0080.npy       \n", "  inflating: centroid_0081.npy       \n", "  inflating: centroid_0082.npy       \n", "  inflating: centroid_0084.npy       \n", "  inflating: centroid_0085.npy       \n", "  inflating: centroid_0086.npy       \n", "  inflating: centroid_0087.npy       \n", "  inflating: centroid_0088.npy       \n", "  inflating: centroid_0089.npy       \n", "  inflating: centroid_0090.npy       \n", "  inflating: centroid_0092.npy       \n", "  inflating: centroid_0093.npy       \n", "  inflating: centroid_0094.npy       \n", "  inflating: centroid_0095.npy       \n", "  inflating: centroid_0097.npy       \n", "  inflating: centroid_0098.npy       \n", "  inflating: centroid_0100.npy       \n", "  inflating: centroid_0101.npy       \n", "  inflating: centroid_0102.npy       \n", "  inflating: centroid_0103.npy       \n", "  inflating: centroid_0104.npy       \n", "  inflating: centroid_0106.npy       \n", "  inflating: centroid_0107.npy       \n", "  inflating: centroid_0108.npy       \n", "  inflating: centroid_0109.npy       \n", "  inflating: centroid_0110.npy       \n", "  inflating: centroid_0113.npy       \n", "  inflating: centroid_0114.npy       \n", "  inflating: centroid_0115.npy       \n", "  inflating: centroid_0116.npy       \n", "  inflating: centroid_0117.npy       \n", "  inflating: centroid_0118.npy       \n", "  inflating: centroid_0119.npy       \n", "  inflating: centroid_0120.npy       \n", "  inflating: centroid_0121.npy       \n", "  inflating: centroid_0122.npy       \n", "  inflating: centroid_0124.npy       \n", "  inflating: centroid_0125.npy       \n", "  inflating: centroid_0126.npy       \n", "  inflating: centroid_0128.npy       \n", "  inflating: centroid_0129.npy       \n", "  inflating: centroid_0130.npy       \n", "  inflating: centroid_0131.npy       \n", "  inflating: centroid_0132.npy       \n", "  inflating: centroid_0133.npy       \n", "  inflating: centroid_0134.npy       \n", "  inflating: centroid_0135.npy       \n", "  inflating: centroid_0136.npy       \n", "  inflating: centroid_0138.npy       \n", "  inflating: centroid_0139.npy       \n", "  inflating: centroid_0140.npy       \n", "  inflating: centroid_0141.npy       \n", "  inflating: centroid_0143.npy       \n", "  inflating: centroid_0145.npy       \n", "  inflating: centroid_0146.npy       \n", "  inflating: centroid_0148.npy       \n", "  inflating: centroid_0149.npy       \n", "  inflating: centroid_0150.npy       \n", "  inflating: centroid_0151.npy       \n", "  inflating: centroid_0153.npy       \n", "  inflating: centroid_0154.npy       \n", "  inflating: centroid_0156.npy       \n", "  inflating: centroid_0157.npy       \n", "  inflating: centroid_0158.npy       \n", "  inflating: centroid_0161.npy       \n", "  inflating: centroid_0162.npy       \n", "  inflating: centroid_0163.npy       \n", "  inflating: centroid_0164.npy       \n", "  inflating: centroid_0166.npy       \n", "  inflating: centroid_0167.npy       \n", "  inflating: centroid_0168.npy       \n", "  inflating: centroid_0170.npy       \n", "  inflating: centroid_0171.npy       \n", "  inflating: centroid_0172.npy       \n", "  inflating: centroid_0174.npy       \n", "  inflating: centroid_0175.npy       \n", "  inflating: centroid_0183.npy       \n", "  inflating: centroid_0184.npy       \n", "  inflating: centroid_0185.npy       \n", "  inflating: centroid_0189.npy       \n", "  inflating: centroid_0190.npy       \n", "  inflating: centroid_0193.npy       \n", "  inflating: centroid_0194.npy       \n", "  inflating: centroid_0195.npy       \n", "  inflating: centroid_0197.npy       \n", "  inflating: centroid_0201.npy       \n", "  inflating: centroid_0203.npy       \n", "  inflating: centroid_0204.npy       \n", "  inflating: centroid_0205.npy       \n", "  inflating: centroid_0206.npy       \n", "  inflating: centroid_0208.npy       \n", "  inflating: centroid_0210.npy       \n", "  inflating: centroid_0211.npy       \n", "  inflating: centroid_0216.npy       \n", "  inflating: centroid_0217.npy       \n", "  inflating: centroid_0219.npy       \n", "  inflating: centroid_0220.npy       \n", "  inflating: centroid_0227.npy       \n", "  inflating: centroid_0228.npy       \n", "  inflating: centroid_0229.npy       \n", "  inflating: centroid_0232.npy       \n", "  inflating: centroid_0234.npy       \n", "  inflating: centroid_0235.npy       \n", "  inflating: centroid_0236.npy       \n", "  inflating: centroid_0238.npy       \n", "  inflating: centroid_0239.npy       \n", "  inflating: centroid_0240.npy       \n", "  inflating: centroid_0241.npy       \n", "  inflating: centroid_0245.npy       \n", "  inflating: centroid_0246.npy       \n", "  inflating: centroid_0247.npy       \n", "  inflating: centroid_0248.npy       \n", "  inflating: centroid_0249.npy       \n", "  inflating: centroid_0252.npy       \n", "  inflating: centroid_0253.npy       \n", "  inflating: centroid_0254.npy       \n", "  inflating: centroid_0256.npy       \n", "  inflating: centroid_0257.npy       \n", "  inflating: centroid_0259.npy       \n", "  inflating: centroid_0264.npy       \n", "  inflating: centroid_0265.npy       \n", "  inflating: centroid_0266.npy       \n", "  inflating: centroid_0268.npy       \n", "  inflating: centroid_0269.npy       \n", "  inflating: centroid_0271.npy       \n", "  inflating: centroid_0272.npy       \n", "  inflating: centroid_0273.npy       \n", "  inflating: centroid_0275.npy       \n", "  inflating: centroid_0276.npy       \n", "  inflating: centroid_0277.npy       \n", "  inflating: centroid_0279.npy       \n", "  inflating: centroid_0280.npy       \n", "  inflating: centroid_0281.npy       \n", "  inflating: centroid_0284.npy       \n", "  inflating: centroid_0285.npy       \n", "  inflating: centroid_0286.npy       \n", "  inflating: centroid_0288.npy       \n", "  inflating: centroid_0289.npy       \n", "  inflating: centroid_0290.npy       \n", "  inflating: centroid_0291.npy       \n", "  inflating: centroid_0294.npy       \n", "  inflating: centroid_0296.npy       \n", "  inflating: centroid_0297.npy       \n", "  inflating: centroid_0298.npy       \n", "  inflating: centroid_0301.npy       \n", "  inflating: centroid_0304.npy       \n", "  inflating: centroid_0305.npy       \n", "  inflating: centroid_0306.npy       \n", "  inflating: centroid_0307.npy       \n", "  inflating: centroid_0308.npy       \n", "  inflating: centroid_0310.npy       \n", "  inflating: centroid_0311.npy       \n", "  inflating: centroid_0314.npy       \n", "  inflating: centroid_0315.npy       \n", "  inflating: centroid_0316.npy       \n", "  inflating: centroid_0320.npy       \n", "  inflating: centroid_0321.npy       \n", "  inflating: centroid_0323.npy       \n", "  inflating: centroid_0324.npy       \n", "  inflating: centroid_0327.npy       \n", "  inflating: centroid_0330.npy       \n", "  inflating: centroid_0331.npy       \n", "  inflating: centroid_0332.npy       \n", "  inflating: centroid_0333.npy       \n", "  inflating: centroid_0334.npy       \n", "  inflating: centroid_0337.npy       \n", "  inflating: centroid_0338.npy       \n", "  inflating: centroid_0339.npy       \n", "  inflating: centroid_0340.npy       \n", "  inflating: centroid_0341.npy       \n", "  inflating: centroid_0342.npy       \n", "  inflating: centroid_0343.npy       \n", "  inflating: centroid_0344.npy       \n", "  inflating: centroid_0345.npy       \n", "  inflating: centroid_0346.npy       \n", "  inflating: centroid_0348.npy       \n", "  inflating: centroid_0349.npy       \n", "  inflating: centroid_0351.npy       \n", "  inflating: centroid_0352.npy       \n", "  inflating: centroid_0353.npy       \n", "  inflating: centroid_0354.npy       \n", "  inflating: centroid_0356.npy       \n", "  inflating: centroid_0357.npy       \n", "  inflating: centroid_0359.npy       \n", "  inflating: centroid_0360.npy       \n", "  inflating: centroid_0361.npy       \n", "  inflating: centroid_0363.npy       \n", "  inflating: centroid_0364.npy       \n", "  inflating: centroid_0365.npy       \n", "  inflating: centroid_0366.npy       \n", "  inflating: centroid_0367.npy       \n", "  inflating: centroid_0368.npy       \n", "  inflating: centroid_0369.npy       \n", "  inflating: centroid_0371.npy       \n", "  inflating: centroid_0373.npy       \n", "  inflating: centroid_0376.npy       \n", "  inflating: centroid_0377.npy       \n", "  inflating: centroid_0378.npy       \n", "  inflating: centroid_0379.npy       \n", "  inflating: centroid_0381.npy       \n", "  inflating: centroid_0382.npy       \n", "  inflating: centroid_0383.npy       \n", "  inflating: centroid_0384.npy       \n", "  inflating: centroid_0385.npy       \n", "  inflating: centroid_0387.npy       \n", "  inflating: centroid_0388.npy       \n", "  inflating: centroid_0389.npy       \n", "  inflating: centroid_0392.npy       \n", "  inflating: centroid_0393.npy       \n", "  inflating: centroid_0394.npy       \n", "  inflating: centroid_0395.npy       \n", "  inflating: centroid_0396.npy       \n", "  inflating: centroid_0398.npy       \n", "  inflating: centroid_0399.npy       \n", "  inflating: centroid_0400.npy       \n", "  inflating: centroid_0401.npy       \n", "  inflating: centroid_0402.npy       \n", "  inflating: centroid_0403.npy       \n", "  inflating: centroid_0404.npy       \n", "  inflating: centroid_0405.npy       \n", "  inflating: centroid_0407.npy       \n", "  inflating: centroid_0408.npy       \n", "  inflating: centroid_0409.npy       \n", "  inflating: centroid_0410.npy       \n", "  inflating: centroid_0411.npy       \n", "  inflating: centroid_0413.npy       \n", "  inflating: centroid_0416.npy       \n", "  inflating: centroid_0417.npy       \n", "  inflating: centroid_0421.npy       \n", "  inflating: centroid_0422.npy       \n", "  inflating: centroid_0423.npy       \n", "  inflating: centroid_0424.npy       \n", "  inflating: centroid_0425.npy       \n", "  inflating: centroid_0428.npy       \n", "  inflating: centroid_0429.npy       \n", "  inflating: centroid_0430.npy       "]}], "source": ["####下载train_track_B.zip\n", "!wget --header=\"Host: ai-studio-online.bj.bcebos.com\" --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\" --header=\"Referer: https://aistudio.baidu.com/\" \"https://ai-studio-online.bj.bcebos.com/v1/38e9adf0fce84527aad3558cc3e82d0e9a251aac4c934297afae9b74d9b3d1e9?responseContentDisposition=attachment%3B%20filename%3Dtrain_track_B.zip&authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2024-06-04T03%3A21%3A02Z%2F-1%2F%2Facd359add161bace603a52c7a268467406cb3c1889a7114bbb687de8002b55f6\" -c -O 'train_track_B.zip'\n", "####解压到train_track_B\n", "!mkdir -p train_track_B && unzip -o train_track_B.zip -d train_track_B/\n", "####将train_track_B移到Dataset下\n", "!mv train_track_B Dataset/Trainset_track_B\n", "####删除train_track_B.zip\n", "!rm train_track_B.zip"]}, {"cell_type": "markdown", "metadata": {"id": "J6DqAplSlOd1"}, "source": ["# **额外数据导入**"]}, {"cell_type": "markdown", "metadata": {"id": "2RNGoSyovQpc"}, "source": ["导入赛道二额外数据\n", "\n", "数据来源：https://github.com/Mohamedelrefaie/DrivAerNet\n", "\n", "论文：<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. \"Drivaernet: A parametric car dataset for data-driven aerodynamic design and graph-based drag prediction.\" arXiv preprint arXiv:2403.08055 (2024).\n", "\n", "特别说明：额外数据中仅使用了id>745的数据，未踩到比赛测试数据，数据预处理见“centroidPressureFromDrivAerNet.py”"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###################### 第一部分：npy_dataset_id_to2090 ######################\n", "!curl 'https://drive.usercontent.google.com/download?id=1HrlAFfxmvidh4nmo5OA97hn7T1w2D3W2&export=download&authuser=0&confirm=t&uuid=fa8ce3e5-8e5e-47f0-bc88-a5255bb1d205&at=APZUnTX5vfaU7XGNnWVJO-3d3NnS%3A1721008623935' \\\n", "  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \\\n", "  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,de;q=0.7' \\\n", "  -H 'cookie: SID=g.a000lgi8bRQnt3R6LXRzzKoIKj5y34h1bzpwUabi2WXA1TVd6wJ_P1oOKYaH1BLrHCo3iS0ORAACgYKARkSARESFQHGX2MiWPVwYo8_viuWERHrHR1NdhoVAUF8yKruIZ_4e1ei2Ekggpa8y8tn0076; __Secure-1PSID=g.a000lgi8bRQnt3R6LXRzzKoIKj5y34h1bzpwUabi2WXA1TVd6wJ_N4Ewz7Dr3VAo_wx2LK2r8gACgYKATwSARESFQHGX2MiQ8tfWf2JaqKvrnUyQ45v3BoVAUF8yKq8rQ4q5pVZQVPFSWPsFpAL0076; __Secure-3PSID=g.a000lgi8bRQnt3R6LXRzzKoIKj5y34h1bzpwUabi2WXA1TVd6wJ_2Uk70D_VaOy1CAOn_-q02wACgYKAdMSARESFQHGX2Milt8gLaNlTE2LPd0GCHBY8RoVAUF8yKpwkzBbaFeOpamEruSyO4KB0076; HSID=Aze2C-18CaajQB5ZY; SSID=AcSGa_zv1UHXYWXjD; APISID=63spz5_adFMvKs3X/AnRG94TS2UMLY3-Jn; SAPISID=lv3zXMc-MpsJeRZr/ARn4Lxk4FrjjMbdSd; __Secure-1PAPISID=lv3zXMc-MpsJeRZr/ARn4Lxk4FrjjMbdSd; __Secure-3PAPISID=lv3zXMc-MpsJeRZr/ARn4Lxk4FrjjMbdSd; AEC=AVYB7cqCjRuyMKwjdDYgIZrykK3GrPvfMCHPw6aftmy-i3jUaK-D4PahKUY; NID=515=qmBvly9f2yPayL5i0BHfVfz668yIlBHAnFQ5N3qoP2gVxqUWPJSsLoUDxXX25G2CDW_FvJW3NOTZDjiCnxDnUQhYDsqIfwa82Zh6xWwzC43u0L25cZKfNGerS4-eyAuiQbBgUl8Rf3PYriRkKJoCMs25jhZ_9z0Wuvch5zkc5zJoL1w9NBRjbk-F6HB9GTuSTBEX-uBWQFQDH5sWuQxwsXtSGRZPQOkKPUNF-LWGa26P1eP2syJlBSOLXIEj2J30p-ahuTAwSgkisPo5YTPFh6gX9iBztpmEyyx9CF4OESec830Sxcu-DgkeNBIwGeKapNLRdg-hyUA6HE2T-tVWQEyhv0k-B5aq95ig81bpWRwDsfUCW8f22q1eZM4Js5nw6nND2hQwwfJ9RlA6orbvTm8DIZZf1ZvwDf1ooTLMA3TZeOv2PEvtlYIhBLX_JxcY_hIc_qvgYrRsJkwOKkykafiRw6F58W9lIQ; __Secure-1PSIDTS=sidts-CjEB4E2dkSE6NJDghNHP1m15TJhjhK_3gk6CbiZHkVbttFbJCYIB2BwTObN5Q77lMAC4EAA; __Secure-3PSIDTS=sidts-CjEB4E2dkSE6NJDghNHP1m15TJhjhK_3gk6CbiZHkVbttFbJCYIB2BwTObN5Q77lMAC4EAA; SIDCC=AKEyXzXx3xq69yhLhL6faYN-CslMkFWFN9k2VGCpxUBix4n1USKcT0K9k1kr9z4WWKxOSWsLX9pf; __Secure-1PSIDCC=AKEyXzVJ1otwaPYrh7P23QePj9Uci7oNGp8LoptrV4SuIpcFNgvHA4BhonL7WRlE7TTsE-FeNO0; __Secure-3PSIDCC=AKEyXzV2hkupH10qZ9QKzyDBrGprWcq6ovH37ctI_OU7s25hBteNzXDK3IHj0Im1nsm4DhqdYKY' \\\n", "  -H 'priority: u=0, i' \\\n", "  -H 'sec-ch-ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"' \\\n", "  -H 'sec-ch-ua-arch: \"x86\"' \\\n", "  -H 'sec-ch-ua-bitness: \"64\"' \\\n", "  -H 'sec-ch-ua-form-factors: \"Desktop\"' \\\n", "  -H 'sec-ch-ua-full-version: \"126.0.6478.127\"' \\\n", "  -H 'sec-ch-ua-full-version-list: \"Not/A)Brand\";v=\"*******\", \"Chromium\";v=\"126.0.6478.127\", \"Google Chrome\";v=\"126.0.6478.127\"' \\\n", "  -H 'sec-ch-ua-mobile: ?0' \\\n", "  -H 'sec-ch-ua-model: \"\"' \\\n", "  -H 'sec-ch-ua-platform: \"Windows\"' \\\n", "  -H 'sec-ch-ua-platform-version: \"15.0.0\"' \\\n", "  -H 'sec-ch-ua-wow64: ?0' \\\n", "  -H 'sec-fetch-dest: document' \\\n", "  -H 'sec-fetch-mode: navigate' \\\n", "  -H 'sec-fetch-site: cross-site' \\\n", "  -H 'sec-fetch-user: ?1' \\\n", "  -H 'upgrade-insecure-requests: 1' \\\n", "  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \\\n", "  -H 'x-client-data: CK61yQEIlbbJAQimtskBCKmdygEIsvXKAQiWocsBCJz+zAEI7ZjNAQiFoM0BCKaizgEIg6jOAQ==' -o ./npy_dataset_id_to2090.tar.gz"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tZRuUMtu4Cji"}, "outputs": [], "source": ["tar_path = './npy_dataset_id_to2090.tar.gz' # 压缩文件路径\n", "extract_dir = './Dataset/Extra_Trainset_track_B' # 解压目录\n", "!tar -xzf tar_path --strip-components=1 -C extract_dir\n", "!rm tar_path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###################### 第二部分：npy_dataset_last_part ######################\n", "!curl 'https://drive.usercontent.google.com/download?id=13LAHqAnjYpqcL33_PdBv71YiVTRi6Bgh&export=download&authuser=0&confirm=t&uuid=ecc5c29b-d551-4cae-955b-8bb3f5162f3f&at=APZUnTXV6vdX6nTQxVdPMOk0foy5%3A1721008953098' \\\n", "  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \\\n", "  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,de;q=0.7' \\\n", "  -H 'cookie: SID=g.a000lgi8bRQnt3R6LXRzzKoIKj5y34h1bzpwUabi2WXA1TVd6wJ_P1oOKYaH1BLrHCo3iS0ORAACgYKARkSARESFQHGX2MiWPVwYo8_viuWERHrHR1NdhoVAUF8yKruIZ_4e1ei2Ekggpa8y8tn0076; __Secure-1PSID=g.a000lgi8bRQnt3R6LXRzzKoIKj5y34h1bzpwUabi2WXA1TVd6wJ_N4Ewz7Dr3VAo_wx2LK2r8gACgYKATwSARESFQHGX2MiQ8tfWf2JaqKvrnUyQ45v3BoVAUF8yKq8rQ4q5pVZQVPFSWPsFpAL0076; __Secure-3PSID=g.a000lgi8bRQnt3R6LXRzzKoIKj5y34h1bzpwUabi2WXA1TVd6wJ_2Uk70D_VaOy1CAOn_-q02wACgYKAdMSARESFQHGX2Milt8gLaNlTE2LPd0GCHBY8RoVAUF8yKpwkzBbaFeOpamEruSyO4KB0076; HSID=Aze2C-18CaajQB5ZY; SSID=AcSGa_zv1UHXYWXjD; APISID=63spz5_adFMvKs3X/AnRG94TS2UMLY3-Jn; SAPISID=lv3zXMc-MpsJeRZr/ARn4Lxk4FrjjMbdSd; __Secure-1PAPISID=lv3zXMc-MpsJeRZr/ARn4Lxk4FrjjMbdSd; __Secure-3PAPISID=lv3zXMc-MpsJeRZr/ARn4Lxk4FrjjMbdSd; AEC=AVYB7cqCjRuyMKwjdDYgIZrykK3GrPvfMCHPw6aftmy-i3jUaK-D4PahKUY; NID=515=qmBvly9f2yPayL5i0BHfVfz668yIlBHAnFQ5N3qoP2gVxqUWPJSsLoUDxXX25G2CDW_FvJW3NOTZDjiCnxDnUQhYDsqIfwa82Zh6xWwzC43u0L25cZKfNGerS4-eyAuiQbBgUl8Rf3PYriRkKJoCMs25jhZ_9z0Wuvch5zkc5zJoL1w9NBRjbk-F6HB9GTuSTBEX-uBWQFQDH5sWuQxwsXtSGRZPQOkKPUNF-LWGa26P1eP2syJlBSOLXIEj2J30p-ahuTAwSgkisPo5YTPFh6gX9iBztpmEyyx9CF4OESec830Sxcu-DgkeNBIwGeKapNLRdg-hyUA6HE2T-tVWQEyhv0k-B5aq95ig81bpWRwDsfUCW8f22q1eZM4Js5nw6nND2hQwwfJ9RlA6orbvTm8DIZZf1ZvwDf1ooTLMA3TZeOv2PEvtlYIhBLX_JxcY_hIc_qvgYrRsJkwOKkykafiRw6F58W9lIQ; __Secure-1PSIDTS=sidts-CjEB4E2dkSE6NJDghNHP1m15TJhjhK_3gk6CbiZHkVbttFbJCYIB2BwTObN5Q77lMAC4EAA; __Secure-3PSIDTS=sidts-CjEB4E2dkSE6NJDghNHP1m15TJhjhK_3gk6CbiZHkVbttFbJCYIB2BwTObN5Q77lMAC4EAA; SIDCC=AKEyXzXxsM9syaIZNUoIGb2zN2lXB7rG8BpEsm9SJz87gq5gsNxwFe-lG63U6c74LYoXvbopHG0Z; __Secure-1PSIDCC=AKEyXzVyhAwFNnt6SkT2HO83gFjVIK8IxNxnondGM0u1COHMa-GBI0MSe0gjD1pG0cphSFwVl9s; __Secure-3PSIDCC=AKEyXzURcwg9KPPeDlIcZSKmqW6O9S42naV7FjBARhx6Zv3vCYTg-EdHEYgQl6tri4LgM_w-DFc' \\\n", "  -H 'priority: u=0, i' \\\n", "  -H 'sec-ch-ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"' \\\n", "  -H 'sec-ch-ua-arch: \"x86\"' \\\n", "  -H 'sec-ch-ua-bitness: \"64\"' \\\n", "  -H 'sec-ch-ua-form-factors: \"Desktop\"' \\\n", "  -H 'sec-ch-ua-full-version: \"126.0.6478.127\"' \\\n", "  -H 'sec-ch-ua-full-version-list: \"Not/A)Brand\";v=\"*******\", \"Chromium\";v=\"126.0.6478.127\", \"Google Chrome\";v=\"126.0.6478.127\"' \\\n", "  -H 'sec-ch-ua-mobile: ?0' \\\n", "  -H 'sec-ch-ua-model: \"\"' \\\n", "  -H 'sec-ch-ua-platform: \"Windows\"' \\\n", "  -H 'sec-ch-ua-platform-version: \"15.0.0\"' \\\n", "  -H 'sec-ch-ua-wow64: ?0' \\\n", "  -H 'sec-fetch-dest: document' \\\n", "  -H 'sec-fetch-mode: navigate' \\\n", "  -H 'sec-fetch-site: cross-site' \\\n", "  -H 'sec-fetch-user: ?1' \\\n", "  -H 'upgrade-insecure-requests: 1' \\\n", "  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \\\n", "  -H 'x-client-data: CK61yQEIlbbJAQimtskBCKmdygEIsvXKAQiWocsBCJz+zAEI7ZjNAQiFoM0BCKaizgEIg6jOAQ==' -o ./npy_dataset_last_part.tar.gz"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3fu-nayY3YZ-"}, "outputs": [], "source": ["tar_path = './npy_dataset_last_part.tar.gz' # 压缩文件路径\n", "extract_dir = './Dataset/Extra_Trainset_track_B' # 解压目录\n", "!tar -xzf tar_path --strip-components=1 -C extract_dir\n", "!rm tar_path"]}, {"cell_type": "markdown", "metadata": {"id": "ml0rN3NHvT8t"}, "source": ["导入PointBERT预训练模型\n", "\n", "数据来源：https://github.com/salesforce/ULIP\n", "\n", "论文：<PERSON>, <PERSON>, et al. \"Ulip-2: Towards scalable multimodal pre-training for 3d understanding.\" Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 2024."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!wget https://paddle-org.bj.bcebos.com/paddlescience/models/contrib/IJCAI_2024_ckpts.tar.gz\n", "!tar -zxvf IJCAI_2024_ckpts.tar.gz\n", "! mv ckpts/bju/geom/ckpt/checkpoint_pointbert.pdparams ./geom/ckpt/checkpoint_pointbert.pdparams"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "machine_shape": "hm", "name": "", "version": ""}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}