{"cells": [{"cell_type": "markdown", "metadata": {"id": "kBRw5QHhBkax"}, "source": ["# 数据集导入(预制链接)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "!wget --no-check-certificate 'https://drive.usercontent.google.com/download?id=1JwR0Q1ArTg6c47EF2ZuIBpQwCPgXKrO2&export=download&authuser=0&confirm=t&uuid=8ce890e0-0019-4e1e-ac63-14718948f612&at=APZUnTW-e7sn7C7k5UVU2BaxZPGT%3A1721020888524' -O dataset_1.zip\n", "!wget --no-check-certificate 'https://drive.usercontent.google.com/download?id=1izP72pHtoXpQvOV8WFCnh_LekzLunyG5&export=download&authuser=0&confirm=t&uuid=8e453e3d-84ac-4f51-9cbf-45d47cbdcc65&at=APZUnTVfJYZBQwnHawB72aq5MPvv%3A1721020973099' -O dataset_2.zip\n", "!wget --no-check-certificate 'https://drive.usercontent.google.com/download?id=1djT0tlmLBi15LYZG0dxci1RSjPI94sM8&export=download&authuser=0&confirm=t&uuid=4687dd5d-a001-47f2-bacd-e72d5c7361e4&at=APZUnTWWEM2OCtpaZNuS4UQjMzxc%3A1721021154071' -O dataset_3.zip\n"]}, {"cell_type": "markdown", "metadata": {"id": "gaD7ugivEL2R"}, "source": ["## 官方版本数据导入"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Unzip dataset file\n", "import os\n", "import shutil\n", "\n", "# 指定新文件夹的名称\n", "new_folder_A = 'unziped/dataset_1'\n", "new_folder_B = 'unziped/dataset_2'\n", "new_folder_C = 'unziped/dataset_3'\n", "\n", "# 在当前目录下创建新文件夹\n", "if not os.path.exists(new_folder_A):\n", "    os.makedirs(new_folder_A)\n", "if not os.path.exists(new_folder_B):\n", "    os.makedirs(new_folder_B)\n", "if not os.path.exists(new_folder_B):\n", "    os.makedirs(new_folder_B)\n", "\n", "# 使用!unzip命令将文件解压到新文件夹中\n", "!unzip dataset_1.zip -d {new_folder_A}\n", "!unzip dataset_2.zip -d {new_folder_B}\n", "!unzip dataset_3.zip -d {new_folder_C}\n"]}, {"cell_type": "markdown", "metadata": {"id": "z0Sek0wtEs5n"}, "source": ["## 百度Baseline版本数据导入"]}, {"cell_type": "markdown", "metadata": {"id": "kY81z-fCgPfK"}, "source": ["## 自定义导入(在下面代码块导入并解压您的数据集)"]}, {"cell_type": "markdown", "metadata": {"id": "ITzT8s2wgZG0"}, "source": ["下载我们自己的数据集"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!wget --no-check-certificate 'https://drive.usercontent.google.com/download?id=1lUV7mJaoWPfRks4DMhJgDCxTZwMr8LQI&export=download&authuser=0&confirm=t&uuid=fbd3c896-23e8-42e0-b09d-410cf3c91487&at=APZUnTXNoF-geyh6R_qbWipUdgeP%3A1721020120231' -O trackCtrain.zip\n", "!wget --no-check-certificate 'https://drive.usercontent.google.com/download?id=1imPyFjFqAj5WTT_K-lHe7_6lFJ4azJ1L&export=download&authuser=0&confirm=t&uuid=ca9d62d3-394a-4798-b2f3-03455a381cf0&at=APZUnTVrYJw4okYQJSrdDXLPqdSX%3A1721020169107' -O trackCtest.zip\n", "!wget --no-check-certificate 'https://drive.usercontent.google.com/download?id=1y23xCHepK-NamTm3OFCAy41RnYolCjzq&export=download&authuser=0' -O trackCsrc.zip"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!mkdir ./Datasets\n", "!unzip trackCtrain.zip -d ./Datasets\n", "!unzip trackCtest.zip -d ./Datasets\n", "!unzip trackCsrc.zip"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 0}