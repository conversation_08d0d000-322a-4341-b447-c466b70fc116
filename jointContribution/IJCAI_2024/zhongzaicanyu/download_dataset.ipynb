{"cells": [{"cell_type": "markdown", "metadata": {"id": "z0Sek0wtEs5n"}, "source": ["## 百度Baseline版本数据导入"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!mkdir Dataset\n", "!cd Dataset"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "executionInfo": {"elapsed": 255341, "status": "ok", "timestamp": 1720679943973, "user": {"displayName": "pei jian zeng", "userId": "06013928868849686113"}, "user_tz": -480}, "id": "GTV_YDaxEsd3", "outputId": "8554a8d9-ac54-49a7-c5d8-f56cd72953ba"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2024-07-11 06:34:48--  https://ai-studio-online.bj.bcebos.com/v1/38e9adf0fce84527aad3558cc3e82d0e9a251aac4c934297afae9b74d9b3d1e9?responseContentDisposition=attachment%3B%20filename%3Dtrain_track_B.zip&authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2024-06-04T03%3A21%3A02Z%2F-1%2F%2Facd359add161bace603a52c7a268467406cb3c1889a7114bbb687de8002b55f6\n", "Resolving ai-studio-online.bj.bcebos.com (ai-studio-online.bj.bcebos.com)... **************, 2409:8c04:1001:1203:0:ff:b0bb:4f27\n", "Connecting to ai-studio-online.bj.bcebos.com (ai-studio-online.bj.bcebos.com)|**************|:443... connected.\n", "HTTP request sent, awaiting response... 206 Partial Content\n", "Length: 4740031429 (4.4G), 2358336404 (2.2G) remaining [application/octet-stream]\n", "Saving to: ‘train_track_B.zip’\n", "\n", "train_track_B.zip   100%[++++++++++=========>]   4.41G  11.7MB/s    in 3m 23s  \n", "\n", "2024-07-11 06:38:12 (11.1 MB/s) - ‘train_track_B.zip’ saved [4740031429/4740031429]\n", "\n", "--2024-07-11 06:38:13--  https://ai-studio-online.bj.bcebos.com/v1/1638f9c292b9437bb46885186407a63e584856c91f9f4c18908b87abd46471e0?responseContentDisposition=attachment%3B%20filename%3Dtrack_B.zip&authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2024-05-05T03%3A02%3A25Z%2F-1%2F%2Fcfdfd6b6a9e096c761ee8e7d863d586741c69a9e6de89f9c3696706d35f8b265\n", "Resolving ai-studio-online.bj.bcebos.com (ai-studio-online.bj.bcebos.com)... **************, 2409:8c04:1001:1203:0:ff:b0bb:4f27\n", "Connecting to ai-studio-online.bj.bcebos.com (ai-studio-online.bj.bcebos.com)|**************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 1012191818 (965M) [application/octet-stream]\n", "Saving to: ‘track_B.zip’\n", "\n", "track_B.zip         100%[===================>] 965.30M  21.0MB/s    in 50s     \n", "\n", "2024-07-11 06:39:04 (19.5 MB/s) - ‘track_B.zip’ saved [1012191818/1012191818]\n", "\n"]}], "source": ["!wget --header=\"Host: ai-studio-online.bj.bcebos.com\" --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\" --header=\"Referer: https://aistudio.baidu.com/\" \"https://ai-studio-online.bj.bcebos.com/v1/38e9adf0fce84527aad3558cc3e82d0e9a251aac4c934297afae9b74d9b3d1e9?responseContentDisposition=attachment%3B%20filename%3Dtrain_track_B.zip&authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2024-06-04T03%3A21%3A02Z%2F-1%2F%2Facd359add161bace603a52c7a268467406cb3c1889a7114bbb687de8002b55f6\" -c -O 'train_track_B.zip'\n", "!wget --header=\"Host: ai-studio-online.bj.bcebos.com\" --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\" --header=\"Referer: https://aistudio.baidu.com/\" \"https://ai-studio-online.bj.bcebos.com/v1/1638f9c292b9437bb46885186407a63e584856c91f9f4c18908b87abd46471e0?responseContentDisposition=attachment%3B%20filename%3Dtrack_B.zip&authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2024-05-05T03%3A02%3A25Z%2F-1%2F%2Fcfdfd6b6a9e096c761ee8e7d863d586741c69a9e6de89f9c3696706d35f8b265\" -c -O 'track_B.zip'"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "executionInfo": {"elapsed": 153639, "status": "ok", "timestamp": 1720680617913, "user": {"displayName": "pei jian zeng", "userId": "06013928868849686113"}, "user_tz": -480}, "id": "OS4r3PcokLdA", "outputId": "525b6316-634c-410d-d582-528aa5698819"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Archive:  train_track_B.zip\n", "  inflating: data_track_B/area_0002.npy  \n", "  inflating: data_track_B/area_0003.npy  \n", "  inflating: data_track_B/area_0004.npy  \n", "  inflating: data_track_B/area_0005.npy  \n", "  inflating: data_track_B/area_0006.npy  \n", "  inflating: data_track_B/area_0011.npy  \n", "  inflating: data_track_B/area_0012.npy  \n", "  inflating: data_track_B/area_0013.npy  \n", "  inflating: data_track_B/area_0015.npy  \n", "  inflating: data_track_B/area_0017.npy  \n", "  inflating: data_track_B/area_0018.npy  \n", "  inflating: data_track_B/area_0020.npy  \n", "  inflating: data_track_B/area_0021.npy  \n", "  inflating: data_track_B/area_0022.npy  \n", "  inflating: data_track_B/area_0023.npy  \n", "  inflating: data_track_B/area_0024.npy  \n", "  inflating: data_track_B/area_0026.npy  \n", "  inflating: data_track_B/area_0029.npy  \n", "  inflating: data_track_B/area_0030.npy  \n", "  inflating: data_track_B/area_0036.npy  \n", "  inflating: data_track_B/area_0037.npy  \n", "  inflating: data_track_B/area_0038.npy  \n", "  inflating: data_track_B/area_0039.npy  \n", "  inflating: data_track_B/area_0040.npy  \n", "  inflating: data_track_B/area_0041.npy  \n", "  inflating: data_track_B/area_0042.npy  \n", "  inflating: data_track_B/area_0043.npy  \n", "  inflating: data_track_B/area_0044.npy  \n", "  inflating: data_track_B/area_0048.npy  \n", "  inflating: data_track_B/area_0049.npy  \n", "  inflating: data_track_B/area_0051.npy  \n", "  inflating: data_track_B/area_0052.npy  \n", "  inflating: data_track_B/area_0055.npy  \n", "  inflating: data_track_B/area_0056.npy  \n", "  inflating: data_track_B/area_0057.npy  \n", "  inflating: data_track_B/area_0059.npy  \n", "  inflating: data_track_B/area_0062.npy  \n", "  inflating: data_track_B/area_0064.npy  \n", "  inflating: data_track_B/area_0066.npy  \n", "  inflating: data_track_B/area_0067.npy  \n", "  inflating: data_track_B/area_0068.npy  \n", "  inflating: data_track_B/area_0071.npy  \n", "  inflating: data_track_B/area_0074.npy  \n", "  inflating: data_track_B/area_0075.npy  \n", "  inflating: data_track_B/area_0077.npy  \n", "  inflating: data_track_B/area_0078.npy  \n", "  inflating: data_track_B/area_0080.npy  \n", "  inflating: data_track_B/area_0081.npy  \n", "  inflating: data_track_B/area_0082.npy  \n", "  inflating: data_track_B/area_0084.npy  \n", "  inflating: data_track_B/area_0085.npy  \n", "  inflating: data_track_B/area_0086.npy  \n", "  inflating: data_track_B/area_0087.npy  \n", "  inflating: data_track_B/area_0088.npy  \n", "  inflating: data_track_B/area_0089.npy  \n", "  inflating: data_track_B/area_0090.npy  \n", "  inflating: data_track_B/area_0092.npy  \n", "  inflating: data_track_B/area_0093.npy  \n", "  inflating: data_track_B/area_0094.npy  \n", "  inflating: data_track_B/area_0095.npy  \n", "  inflating: data_track_B/area_0097.npy  \n", "  inflating: data_track_B/area_0098.npy  \n", "  inflating: data_track_B/area_0100.npy  \n", "  inflating: data_track_B/area_0101.npy  \n", "  inflating: data_track_B/area_0102.npy  \n", "  inflating: data_track_B/area_0103.npy  \n", "  inflating: data_track_B/area_0104.npy  \n", "  inflating: data_track_B/area_0106.npy  \n", "  inflating: data_track_B/area_0107.npy  \n", "  inflating: data_track_B/area_0108.npy  \n", "  inflating: data_track_B/area_0109.npy  \n", "  inflating: data_track_B/area_0110.npy  \n", "  inflating: data_track_B/area_0113.npy  \n", "  inflating: data_track_B/area_0114.npy  \n", "  inflating: data_track_B/area_0115.npy  \n", "  inflating: data_track_B/area_0116.npy  \n", "  inflating: data_track_B/area_0117.npy  \n", "  inflating: data_track_B/area_0118.npy  \n", "  inflating: data_track_B/area_0119.npy  \n", "  inflating: data_track_B/area_0120.npy  \n", "  inflating: data_track_B/area_0121.npy  \n", "  inflating: data_track_B/area_0122.npy  \n", "  inflating: data_track_B/area_0124.npy  \n", "  inflating: data_track_B/area_0125.npy  \n", "  inflating: data_track_B/area_0126.npy  \n", "  inflating: data_track_B/area_0128.npy  \n", "  inflating: data_track_B/area_0129.npy  \n", "  inflating: data_track_B/area_0130.npy  \n", "  inflating: data_track_B/area_0131.npy  \n", "  inflating: data_track_B/area_0132.npy  \n", "  inflating: data_track_B/area_0133.npy  \n", "  inflating: data_track_B/area_0134.npy  \n", "  inflating: data_track_B/area_0135.npy  \n", "  inflating: data_track_B/area_0136.npy  \n", "  inflating: data_track_B/area_0138.npy  \n", "  inflating: data_track_B/area_0139.npy  \n", "  inflating: data_track_B/area_0140.npy  \n", "  inflating: data_track_B/area_0141.npy  \n", "  inflating: data_track_B/area_0143.npy  \n", "  inflating: data_track_B/area_0145.npy  \n", "  inflating: data_track_B/area_0146.npy  \n", "  inflating: data_track_B/area_0148.npy  \n", "  inflating: data_track_B/area_0149.npy  \n", "  inflating: data_track_B/area_0150.npy  \n", "  inflating: data_track_B/area_0151.npy  \n", "  inflating: data_track_B/area_0153.npy  \n", "  inflating: data_track_B/area_0154.npy  \n", "  inflating: data_track_B/area_0156.npy  \n", "  inflating: data_track_B/area_0157.npy  \n", "  inflating: data_track_B/area_0158.npy  \n", "  inflating: data_track_B/area_0161.npy  \n", "  inflating: data_track_B/area_0162.npy  \n", "  inflating: data_track_B/area_0163.npy  \n", "  inflating: data_track_B/area_0164.npy  \n", "  inflating: data_track_B/area_0166.npy  \n", "  inflating: data_track_B/area_0167.npy  \n", "  inflating: data_track_B/area_0168.npy  \n", "  inflating: data_track_B/area_0170.npy  \n", "  inflating: data_track_B/area_0171.npy  \n", "  inflating: data_track_B/area_0172.npy  \n", "  inflating: data_track_B/area_0174.npy  \n", "  inflating: data_track_B/area_0175.npy  \n", "  inflating: data_track_B/area_0183.npy  \n", "  inflating: data_track_B/area_0184.npy  \n", "  inflating: data_track_B/area_0185.npy  \n", "  inflating: data_track_B/area_0189.npy  \n", "  inflating: data_track_B/area_0190.npy  \n", "  inflating: data_track_B/area_0193.npy  \n", "  inflating: data_track_B/area_0194.npy  \n", "  inflating: data_track_B/area_0195.npy  \n", "  inflating: data_track_B/area_0197.npy  \n", "  inflating: data_track_B/area_0201.npy  \n", "  inflating: data_track_B/area_0203.npy  \n", "  inflating: data_track_B/area_0204.npy  \n", "  inflating: data_track_B/area_0205.npy  \n", "  inflating: data_track_B/area_0206.npy  \n", "  inflating: data_track_B/area_0208.npy  \n", "  inflating: data_track_B/area_0210.npy  \n", "  inflating: data_track_B/area_0211.npy  \n", "  inflating: data_track_B/area_0216.npy  \n", "  inflating: data_track_B/area_0217.npy  \n", "  inflating: data_track_B/area_0219.npy  \n", "  inflating: data_track_B/area_0220.npy  \n", "  inflating: data_track_B/area_0227.npy  \n", "  inflating: data_track_B/area_0228.npy  \n", "  inflating: data_track_B/area_0229.npy  \n", "  inflating: data_track_B/area_0232.npy  \n", "  inflating: data_track_B/area_0234.npy  \n", "  inflating: data_track_B/area_0235.npy  \n", "  inflating: data_track_B/area_0236.npy  \n", "  inflating: data_track_B/area_0238.npy  \n", "  inflating: data_track_B/area_0239.npy  \n", "  inflating: data_track_B/area_0240.npy  \n", "  inflating: data_track_B/area_0241.npy  \n", "  inflating: data_track_B/area_0245.npy  \n", "  inflating: data_track_B/area_0246.npy  \n", "  inflating: data_track_B/area_0247.npy  \n", "  inflating: data_track_B/area_0248.npy  \n", "  inflating: data_track_B/area_0249.npy  \n", "  inflating: data_track_B/area_0252.npy  \n", "  inflating: data_track_B/area_0253.npy  \n", "  inflating: data_track_B/area_0254.npy  \n", "  inflating: data_track_B/area_0256.npy  \n", "  inflating: data_track_B/area_0257.npy  \n", "  inflating: data_track_B/area_0259.npy  \n", "  inflating: data_track_B/area_0264.npy  \n", "  inflating: data_track_B/area_0265.npy  \n", "  inflating: data_track_B/area_0266.npy  \n", "  inflating: data_track_B/area_0268.npy  \n", "  inflating: data_track_B/area_0269.npy  \n", "  inflating: data_track_B/area_0271.npy  \n", "  inflating: data_track_B/area_0272.npy  \n", "  inflating: data_track_B/area_0273.npy  \n", "  inflating: data_track_B/area_0275.npy  \n", "  inflating: data_track_B/area_0276.npy  \n", "  inflating: data_track_B/area_0277.npy  \n", "  inflating: data_track_B/area_0279.npy  \n", "  inflating: data_track_B/area_0280.npy  \n", "  inflating: data_track_B/area_0281.npy  \n", "  inflating: data_track_B/area_0284.npy  \n", "  inflating: data_track_B/area_0285.npy  \n", "  inflating: data_track_B/area_0286.npy  \n", "  inflating: data_track_B/area_0288.npy  \n", "  inflating: data_track_B/area_0289.npy  \n", "  inflating: data_track_B/area_0290.npy  \n", "  inflating: data_track_B/area_0291.npy  \n", "  inflating: data_track_B/area_0294.npy  \n", "  inflating: data_track_B/area_0296.npy  \n", "  inflating: data_track_B/area_0297.npy  \n", "  inflating: data_track_B/area_0298.npy  \n", "  inflating: data_track_B/area_0301.npy  \n", "  inflating: data_track_B/area_0304.npy  \n", "  inflating: data_track_B/area_0305.npy  \n", "  inflating: data_track_B/area_0306.npy  \n", "  inflating: data_track_B/area_0307.npy  \n", "  inflating: data_track_B/area_0308.npy  \n", "  inflating: data_track_B/area_0310.npy  \n", "  inflating: data_track_B/area_0311.npy  \n", "  inflating: data_track_B/area_0314.npy  \n", "  inflating: data_track_B/area_0315.npy  \n", "  inflating: data_track_B/area_0316.npy  \n", "  inflating: data_track_B/area_0320.npy  \n", "  inflating: data_track_B/area_0321.npy  \n", "  inflating: data_track_B/area_0323.npy  \n", "  inflating: data_track_B/area_0324.npy  \n", "  inflating: data_track_B/area_0327.npy  \n", "  inflating: data_track_B/area_0330.npy  \n", "  inflating: data_track_B/area_0331.npy  \n", "  inflating: data_track_B/area_0332.npy  \n", "  inflating: data_track_B/area_0333.npy  \n", "  inflating: data_track_B/area_0334.npy  \n", "  inflating: data_track_B/area_0337.npy  \n", "  inflating: data_track_B/area_0338.npy  \n", "  inflating: data_track_B/area_0339.npy  \n", "  inflating: data_track_B/area_0340.npy  \n", "  inflating: data_track_B/area_0341.npy  \n", "  inflating: data_track_B/area_0342.npy  \n", "  inflating: data_track_B/area_0343.npy  \n", "  inflating: data_track_B/area_0344.npy  \n", "  inflating: data_track_B/area_0345.npy  \n", "  inflating: data_track_B/area_0346.npy  \n", "  inflating: data_track_B/area_0348.npy  \n", "  inflating: data_track_B/area_0349.npy  \n", "  inflating: data_track_B/area_0351.npy  \n", "  inflating: data_track_B/area_0352.npy  \n", "  inflating: data_track_B/area_0353.npy  \n", "  inflating: data_track_B/area_0354.npy  \n", "  inflating: data_track_B/area_0356.npy  \n", "  inflating: data_track_B/area_0357.npy  \n", "  inflating: data_track_B/area_0359.npy  \n", "  inflating: data_track_B/area_0360.npy  \n", "  inflating: data_track_B/area_0361.npy  \n", "  inflating: data_track_B/area_0363.npy  \n", "  inflating: data_track_B/area_0364.npy  \n", "  inflating: data_track_B/area_0365.npy  \n", "  inflating: data_track_B/area_0366.npy  \n", "  inflating: data_track_B/area_0367.npy  \n", "  inflating: data_track_B/area_0368.npy  \n", "  inflating: data_track_B/area_0369.npy  \n", "  inflating: data_track_B/area_0371.npy  \n", "  inflating: data_track_B/area_0373.npy  \n", "  inflating: data_track_B/area_0376.npy  \n", "  inflating: data_track_B/area_0377.npy  \n", "  inflating: data_track_B/area_0378.npy  \n", "  inflating: data_track_B/area_0379.npy  \n", "  inflating: data_track_B/area_0381.npy  \n", "  inflating: data_track_B/area_0382.npy  \n", "  inflating: data_track_B/area_0383.npy  \n", "  inflating: data_track_B/area_0384.npy  \n", "  inflating: data_track_B/area_0385.npy  \n", "  inflating: data_track_B/area_0387.npy  \n", "  inflating: data_track_B/area_0388.npy  \n", "  inflating: data_track_B/area_0389.npy  \n", "  inflating: data_track_B/area_0392.npy  \n", "  inflating: data_track_B/area_0393.npy  \n", "  inflating: data_track_B/area_0394.npy  \n", "  inflating: data_track_B/area_0395.npy  \n", "  inflating: data_track_B/area_0396.npy  \n", "  inflating: data_track_B/area_0398.npy  \n", "  inflating: data_track_B/area_0399.npy  \n", "  inflating: data_track_B/area_0400.npy  \n", "  inflating: data_track_B/area_0401.npy  \n", "  inflating: data_track_B/area_0402.npy  \n", "  inflating: data_track_B/area_0403.npy  \n", "  inflating: data_track_B/area_0404.npy  \n", "  inflating: data_track_B/area_0405.npy  \n", "  inflating: data_track_B/area_0407.npy  \n", "  inflating: data_track_B/area_0408.npy  \n", "  inflating: data_track_B/area_0409.npy  \n", "  inflating: data_track_B/area_0410.npy  \n", "  inflating: data_track_B/area_0411.npy  \n", "  inflating: data_track_B/area_0413.npy  \n", "  inflating: data_track_B/area_0416.npy  \n", "  inflating: data_track_B/area_0417.npy  \n", "  inflating: data_track_B/area_0421.npy  \n", "  inflating: data_track_B/area_0422.npy  \n", "  inflating: data_track_B/area_0423.npy  \n", "  inflating: data_track_B/area_0424.npy  \n", "  inflating: data_track_B/area_0425.npy  \n", "  inflating: data_track_B/area_0428.npy  \n", "  inflating: data_track_B/area_0429.npy  \n", "  inflating: data_track_B/area_0430.npy  \n", "  inflating: data_track_B/area_0431.npy  \n", "  inflating: data_track_B/area_0432.npy  \n", "  inflating: data_track_B/area_0435.npy  \n", "  inflating: data_track_B/area_0438.npy  \n", "  inflating: data_track_B/area_0439.npy  \n", "  inflating: data_track_B/area_0441.npy  \n", "  inflating: data_track_B/area_0444.npy  \n", "  inflating: data_track_B/area_0445.npy  \n", "  inflating: data_track_B/area_0449.npy  \n", "  inflating: data_track_B/area_0450.npy  \n", "  inflating: data_track_B/area_0451.npy  \n", "  inflating: data_track_B/area_0452.npy  \n", "  inflating: data_track_B/area_0453.npy  \n", "  inflating: data_track_B/area_0456.npy  \n", "  inflating: data_track_B/area_0457.npy  \n", "  inflating: data_track_B/area_0458.npy  \n", "  inflating: data_track_B/area_0459.npy  \n", "  inflating: data_track_B/area_0460.npy  \n", "  inflating: data_track_B/area_0461.npy  \n", "  inflating: data_track_B/area_0463.npy  \n", "  inflating: data_track_B/area_0464.npy  \n", "  inflating: data_track_B/area_0465.npy  \n", "  inflating: data_track_B/area_0467.npy  \n", "  inflating: data_track_B/area_0469.npy  \n", "  inflating: data_track_B/area_0471.npy  \n", "  inflating: data_track_B/area_0472.npy  \n", "  inflating: data_track_B/area_0474.npy  \n", "  inflating: data_track_B/area_0475.npy  \n", "  inflating: data_track_B/area_0477.npy  \n", "  inflating: data_track_B/area_0478.npy  \n", "  inflating: data_track_B/area_0479.npy  \n", "  inflating: data_track_B/area_0480.npy  \n", "  inflating: data_track_B/area_0481.npy  \n", "  inflating: data_track_B/area_0482.npy  \n", "  inflating: data_track_B/area_0485.npy  \n", "  inflating: data_track_B/area_0486.npy  \n", "  inflating: data_track_B/area_0487.npy  \n", "  inflating: data_track_B/area_0488.npy  \n", "  inflating: data_track_B/area_0489.npy  \n", "  inflating: data_track_B/area_0492.npy  \n", "  inflating: data_track_B/area_0493.npy  \n", "  inflating: data_track_B/area_0494.npy  \n", "  inflating: data_track_B/area_0497.npy  \n", "  inflating: data_track_B/area_0498.npy  \n", "  inflating: data_track_B/area_0499.npy  \n", "  inflating: data_track_B/area_0501.npy  \n", "  inflating: data_track_B/area_0502.npy  \n", "  inflating: data_track_B/area_0503.npy  \n", "  inflating: data_track_B/area_0504.npy  \n", "  inflating: data_track_B/area_0507.npy  \n", "  inflating: data_track_B/area_0508.npy  \n", "  inflating: data_track_B/area_0509.npy  \n", "  inflating: data_track_B/area_0513.npy  \n", "  inflating: data_track_B/area_0514.npy  \n", "  inflating: data_track_B/area_0515.npy  \n", "  inflating: data_track_B/area_0517.npy  \n", "  inflating: data_track_B/area_0518.npy  \n", "  inflating: data_track_B/area_0519.npy  \n", "  inflating: data_track_B/area_0520.npy  \n", "  inflating: data_track_B/area_0521.npy  \n", "  inflating: data_track_B/area_0522.npy  \n", "  inflating: data_track_B/area_0523.npy  \n", "  inflating: data_track_B/area_0524.npy  \n", "  inflating: data_track_B/area_0525.npy  \n", "  inflating: data_track_B/area_0526.npy  \n", "  inflating: data_track_B/area_0527.npy  \n", "  inflating: data_track_B/area_0528.npy  \n", "  inflating: data_track_B/area_0529.npy  \n", "  inflating: data_track_B/area_0530.npy  \n", "  inflating: data_track_B/area_0531.npy  \n", "  inflating: data_track_B/area_0534.npy  \n", "  inflating: data_track_B/area_0535.npy  \n", "  inflating: data_track_B/area_0536.npy  \n", "  inflating: data_track_B/area_0538.npy  \n", "  inflating: data_track_B/area_0541.npy  \n", "  inflating: data_track_B/area_0542.npy  \n", "  inflating: data_track_B/area_0544.npy  \n", "  inflating: data_track_B/area_0545.npy  \n", "  inflating: data_track_B/area_0546.npy  \n", "  inflating: data_track_B/area_0547.npy  \n", "  inflating: data_track_B/area_0550.npy  \n", "  inflating: data_track_B/area_0551.npy  \n", "  inflating: data_track_B/area_0553.npy  \n", "  inflating: data_track_B/area_0555.npy  \n", "  inflating: data_track_B/area_0557.npy  \n", "  inflating: data_track_B/area_0558.npy  \n", "  inflating: data_track_B/area_0561.npy  \n", "  inflating: data_track_B/area_0563.npy  \n", "  inflating: data_track_B/area_0564.npy  \n", "  inflating: data_track_B/area_0565.npy  \n", "  inflating: data_track_B/area_0567.npy  \n", "  inflating: data_track_B/area_0568.npy  \n", "  inflating: data_track_B/area_0571.npy  \n", "  inflating: data_track_B/area_0574.npy  \n", "  inflating: data_track_B/area_0576.npy  \n", "  inflating: data_track_B/area_0579.npy  \n", "  inflating: data_track_B/area_0580.npy  \n", "  inflating: data_track_B/area_0582.npy  \n", "  inflating: data_track_B/area_0584.npy  \n", "  inflating: data_track_B/area_0585.npy  \n", "  inflating: data_track_B/area_0588.npy  \n", "  inflating: data_track_B/area_0589.npy  \n", "  inflating: data_track_B/area_0590.npy  \n", "  inflating: data_track_B/area_0591.npy  \n", "  inflating: data_track_B/area_0592.npy  \n", "  inflating: data_track_B/area_0593.npy  \n", "  inflating: data_track_B/area_0594.npy  \n", "  inflating: data_track_B/area_0595.npy  \n", "  inflating: data_track_B/area_0596.npy  \n", "  inflating: data_track_B/area_0597.npy  \n", "  inflating: data_track_B/area_0598.npy  \n", "  inflating: data_track_B/area_0600.npy  \n", "  inflating: data_track_B/area_0602.npy  \n", "  inflating: data_track_B/area_0605.npy  \n", "  inflating: data_track_B/area_0608.npy  \n", "  inflating: data_track_B/area_0609.npy  \n", "  inflating: data_track_B/area_0611.npy  \n", "  inflating: data_track_B/area_0612.npy  \n", "  inflating: data_track_B/area_0613.npy  \n", "  inflating: data_track_B/area_0614.npy  \n", "  inflating: data_track_B/area_0618.npy  \n", "  inflating: data_track_B/area_0619.npy  \n", "  inflating: data_track_B/area_0620.npy  \n", "  inflating: data_track_B/area_0621.npy  \n", "  inflating: data_track_B/area_0622.npy  \n", "  inflating: data_track_B/area_0623.npy  \n", "  inflating: data_track_B/area_0624.npy  \n", "  inflating: data_track_B/area_0625.npy  \n", "  inflating: data_track_B/area_0627.npy  \n", "  inflating: data_track_B/area_0628.npy  \n", "  inflating: data_track_B/area_0629.npy  \n", "  inflating: data_track_B/area_0630.npy  \n", "  inflating: data_track_B/area_0631.npy  \n", "  inflating: data_track_B/area_0632.npy  \n", "  inflating: data_track_B/area_0633.npy  \n", "  inflating: data_track_B/area_0634.npy  \n", "  inflating: data_track_B/area_0635.npy  \n", "  inflating: data_track_B/area_0637.npy  \n", "  inflating: data_track_B/area_0638.npy  \n", "  inflating: data_track_B/area_0639.npy  \n", "  inflating: data_track_B/area_0640.npy  \n", "  inflating: data_track_B/area_0641.npy  \n", "  inflating: data_track_B/area_0643.npy  \n", "  inflating: data_track_B/area_0644.npy  \n", "  inflating: data_track_B/area_0645.npy  \n", "  inflating: data_track_B/area_0646.npy  \n", "  inflating: data_track_B/area_0648.npy  \n", "  inflating: data_track_B/area_0650.npy  \n", "  inflating: data_track_B/area_0651.npy  \n", "  inflating: data_track_B/area_0652.npy  \n", "  inflating: data_track_B/area_0653.npy  \n", "  inflating: data_track_B/area_0654.npy  \n", "  inflating: data_track_B/area_0656.npy  \n", "  inflating: data_track_B/area_0657.npy  \n", "  inflating: data_track_B/area_0658.npy  \n", "  inflating: data_track_B/area_0661.npy  \n", "  inflating: data_track_B/area_0663.npy  \n", "  inflating: data_track_B/area_0664.npy  \n", "  inflating: data_track_B/area_0665.npy  \n", "  inflating: data_track_B/area_0666.npy  \n", "  inflating: data_track_B/area_0667.npy  \n", "  inflating: data_track_B/area_0668.npy  \n", "  inflating: data_track_B/area_0669.npy  \n", "  inflating: data_track_B/area_0671.npy  \n", "  inflating: data_track_B/area_0672.npy  \n", "  inflating: data_track_B/area_0673.npy  \n", "  inflating: data_track_B/area_0674.npy  \n", "  inflating: data_track_B/area_0676.npy  \n", "  inflating: data_track_B/area_0677.npy  \n", "  inflating: data_track_B/area_0678.npy  \n", "  inflating: data_track_B/area_0679.npy  \n", "  inflating: data_track_B/area_0680.npy  \n", "  inflating: data_track_B/area_0682.npy  \n", "  inflating: data_track_B/area_0686.npy  \n", "  inflating: data_track_B/area_0688.npy  \n", "  inflating: data_track_B/area_0689.npy  \n", "  inflating: data_track_B/area_0690.npy  \n", "  inflating: data_track_B/area_0691.npy  \n", "  inflating: data_track_B/area_0692.npy  \n", "  inflating: data_track_B/area_0693.npy  \n", "  inflating: data_track_B/area_0694.npy  \n", "  inflating: data_track_B/area_0695.npy  \n", "  inflating: data_track_B/area_0697.npy  \n", "  inflating: data_track_B/area_0699.npy  \n", "  inflating: data_track_B/area_0700.npy  \n", "  inflating: data_track_B/area_0701.npy  \n", "  inflating: data_track_B/area_0703.npy  \n", "  inflating: data_track_B/area_0704.npy  \n", "  inflating: data_track_B/area_0706.npy  \n", "  inflating: data_track_B/area_0707.npy  \n", "  inflating: data_track_B/area_0708.npy  \n", "  inflating: data_track_B/area_0709.npy  \n", "  inflating: data_track_B/area_0711.npy  \n", "  inflating: data_track_B/area_0712.npy  \n", "  inflating: data_track_B/area_0713.npy  \n", "  inflating: data_track_B/area_0714.npy  \n", "  inflating: data_track_B/area_0715.npy  \n", "  inflating: data_track_B/area_0716.npy  \n", "  inflating: data_track_B/area_0718.npy  \n", "  inflating: data_track_B/area_0719.npy  \n", "  inflating: data_track_B/area_0720.npy  \n", "  inflating: data_track_B/area_0721.npy  \n", "  inflating: data_track_B/area_0722.npy  \n", "  inflating: data_track_B/area_0724.npy  \n", "  inflating: data_track_B/area_0727.npy  \n", "  inflating: data_track_B/area_0728.npy  \n", "  inflating: data_track_B/area_0729.npy  \n", "  inflating: data_track_B/area_0730.npy  \n", "  inflating: data_track_B/area_0731.npy  \n", "  inflating: data_track_B/area_0733.npy  \n", "  inflating: data_track_B/area_0735.npy  \n", "  inflating: data_track_B/area_0736.npy  \n", "  inflating: data_track_B/area_0737.npy  \n", "  inflating: data_track_B/area_0740.npy  \n", "  inflating: data_track_B/area_0742.npy  \n", "  inflating: data_track_B/area_0743.npy  \n", "  inflating: data_track_B/area_0744.npy  \n", "  inflating: data_track_B/area_0745.npy  \n", "  inflating: data_track_B/centroid_0002.npy  \n", "  inflating: data_track_B/centroid_0003.npy  \n", "  inflating: data_track_B/centroid_0004.npy  \n", "  inflating: data_track_B/centroid_0005.npy  \n", "  inflating: data_track_B/centroid_0006.npy  \n", "  inflating: data_track_B/centroid_0011.npy  \n", "  inflating: data_track_B/centroid_0012.npy  \n", "  inflating: data_track_B/centroid_0013.npy  \n", "  inflating: data_track_B/centroid_0015.npy  \n", "  inflating: data_track_B/centroid_0017.npy  \n", "  inflating: data_track_B/centroid_0018.npy  \n", "  inflating: data_track_B/centroid_0020.npy  \n", "  inflating: data_track_B/centroid_0021.npy  \n", "  inflating: data_track_B/centroid_0022.npy  \n", "  inflating: data_track_B/centroid_0023.npy  \n", "  inflating: data_track_B/centroid_0024.npy  \n", "  inflating: data_track_B/centroid_0026.npy  \n", "  inflating: data_track_B/centroid_0029.npy  \n", "  inflating: data_track_B/centroid_0030.npy  \n", "  inflating: data_track_B/centroid_0036.npy  \n", "  inflating: data_track_B/centroid_0037.npy  \n", "  inflating: data_track_B/centroid_0038.npy  \n", "  inflating: data_track_B/centroid_0039.npy  \n", "  inflating: data_track_B/centroid_0040.npy  \n", "  inflating: data_track_B/centroid_0041.npy  \n", "  inflating: data_track_B/centroid_0042.npy  \n", "  inflating: data_track_B/centroid_0043.npy  \n", "  inflating: data_track_B/centroid_0044.npy  \n", "  inflating: data_track_B/centroid_0048.npy  \n", "  inflating: data_track_B/centroid_0049.npy  \n", "  inflating: data_track_B/centroid_0051.npy  \n", "  inflating: data_track_B/centroid_0052.npy  \n", "  inflating: data_track_B/centroid_0055.npy  \n", "  inflating: data_track_B/centroid_0056.npy  \n", "  inflating: data_track_B/centroid_0057.npy  \n", "  inflating: data_track_B/centroid_0059.npy  \n", "  inflating: data_track_B/centroid_0062.npy  \n", "  inflating: data_track_B/centroid_0064.npy  \n", "  inflating: data_track_B/centroid_0066.npy  \n", "  inflating: data_track_B/centroid_0067.npy  \n", "  inflating: data_track_B/centroid_0068.npy  \n", "  inflating: data_track_B/centroid_0071.npy  \n", "  inflating: data_track_B/centroid_0074.npy  \n", "  inflating: data_track_B/centroid_0075.npy  \n", "  inflating: data_track_B/centroid_0077.npy  \n", "  inflating: data_track_B/centroid_0078.npy  \n", "  inflating: data_track_B/centroid_0080.npy  \n", "  inflating: data_track_B/centroid_0081.npy  \n", "  inflating: data_track_B/centroid_0082.npy  \n", "  inflating: data_track_B/centroid_0084.npy  \n", "  inflating: data_track_B/centroid_0085.npy  \n", "  inflating: data_track_B/centroid_0086.npy  \n", "  inflating: data_track_B/centroid_0087.npy  \n", "  inflating: data_track_B/centroid_0088.npy  \n", "  inflating: data_track_B/centroid_0089.npy  \n", "  inflating: data_track_B/centroid_0090.npy  \n", "  inflating: data_track_B/centroid_0092.npy  \n", "  inflating: data_track_B/centroid_0093.npy  \n", "  inflating: data_track_B/centroid_0094.npy  \n", "  inflating: data_track_B/centroid_0095.npy  \n", "  inflating: data_track_B/centroid_0097.npy  \n", "  inflating: data_track_B/centroid_0098.npy  \n", "  inflating: data_track_B/centroid_0100.npy  \n", "  inflating: data_track_B/centroid_0101.npy  \n", "  inflating: data_track_B/centroid_0102.npy  \n", "  inflating: data_track_B/centroid_0103.npy  \n", "  inflating: data_track_B/centroid_0104.npy  \n", "  inflating: data_track_B/centroid_0106.npy  \n", "  inflating: data_track_B/centroid_0107.npy  \n", "  inflating: data_track_B/centroid_0108.npy  \n", "  inflating: data_track_B/centroid_0109.npy  \n", "  inflating: data_track_B/centroid_0110.npy  \n", "  inflating: data_track_B/centroid_0113.npy  \n", "  inflating: data_track_B/centroid_0114.npy  \n", "  inflating: data_track_B/centroid_0115.npy  \n", "  inflating: data_track_B/centroid_0116.npy  \n", "  inflating: data_track_B/centroid_0117.npy  \n", "  inflating: data_track_B/centroid_0118.npy  \n", "  inflating: data_track_B/centroid_0119.npy  \n", "  inflating: data_track_B/centroid_0120.npy  \n", "  inflating: data_track_B/centroid_0121.npy  \n", "  inflating: data_track_B/centroid_0122.npy  \n", "  inflating: data_track_B/centroid_0124.npy  \n", "  inflating: data_track_B/centroid_0125.npy  \n", "  inflating: data_track_B/centroid_0126.npy  \n", "  inflating: data_track_B/centroid_0128.npy  \n", "  inflating: data_track_B/centroid_0129.npy  \n", "  inflating: data_track_B/centroid_0130.npy  \n", "  inflating: data_track_B/centroid_0131.npy  \n", "  inflating: data_track_B/centroid_0132.npy  \n", "  inflating: data_track_B/centroid_0133.npy  \n", "  inflating: data_track_B/centroid_0134.npy  \n", "  inflating: data_track_B/centroid_0135.npy  \n", "  inflating: data_track_B/centroid_0136.npy  \n", "  inflating: data_track_B/centroid_0138.npy  \n", "  inflating: data_track_B/centroid_0139.npy  \n", "  inflating: data_track_B/centroid_0140.npy  \n", "  inflating: data_track_B/centroid_0141.npy  \n", "  inflating: data_track_B/centroid_0143.npy  \n", "  inflating: data_track_B/centroid_0145.npy  \n", "  inflating: data_track_B/centroid_0146.npy  \n", "  inflating: data_track_B/centroid_0148.npy  \n", "  inflating: data_track_B/centroid_0149.npy  \n", "  inflating: data_track_B/centroid_0150.npy  \n", "  inflating: data_track_B/centroid_0151.npy  \n", "  inflating: data_track_B/centroid_0153.npy  \n", "  inflating: data_track_B/centroid_0154.npy  \n", "  inflating: data_track_B/centroid_0156.npy  \n", "  inflating: data_track_B/centroid_0157.npy  \n", "  inflating: data_track_B/centroid_0158.npy  \n", "  inflating: data_track_B/centroid_0161.npy  \n", "  inflating: data_track_B/centroid_0162.npy  \n", "  inflating: data_track_B/centroid_0163.npy  \n", "  inflating: data_track_B/centroid_0164.npy  \n", "  inflating: data_track_B/centroid_0166.npy  \n", "  inflating: data_track_B/centroid_0167.npy  \n", "  inflating: data_track_B/centroid_0168.npy  \n", "  inflating: data_track_B/centroid_0170.npy  \n", "  inflating: data_track_B/centroid_0171.npy  \n", "  inflating: data_track_B/centroid_0172.npy  \n", "  inflating: data_track_B/centroid_0174.npy  \n", "  inflating: data_track_B/centroid_0175.npy  \n", "  inflating: data_track_B/centroid_0183.npy  \n", "  inflating: data_track_B/centroid_0184.npy  \n", "  inflating: data_track_B/centroid_0185.npy  \n", "  inflating: data_track_B/centroid_0189.npy  \n", "  inflating: data_track_B/centroid_0190.npy  \n", "  inflating: data_track_B/centroid_0193.npy  \n", "  inflating: data_track_B/centroid_0194.npy  \n", "  inflating: data_track_B/centroid_0195.npy  \n", "  inflating: data_track_B/centroid_0197.npy  \n", "  inflating: data_track_B/centroid_0201.npy  \n", "  inflating: data_track_B/centroid_0203.npy  \n", "  inflating: data_track_B/centroid_0204.npy  \n", "  inflating: data_track_B/centroid_0205.npy  \n", "  inflating: data_track_B/centroid_0206.npy  \n", "  inflating: data_track_B/centroid_0208.npy  \n", "  inflating: data_track_B/centroid_0210.npy  \n", "  inflating: data_track_B/centroid_0211.npy  \n", "  inflating: data_track_B/centroid_0216.npy  \n", "  inflating: data_track_B/centroid_0217.npy  \n", "  inflating: data_track_B/centroid_0219.npy  \n", "  inflating: data_track_B/centroid_0220.npy  \n", "  inflating: data_track_B/centroid_0227.npy  \n", "  inflating: data_track_B/centroid_0228.npy  \n", "  inflating: data_track_B/centroid_0229.npy  \n", "  inflating: data_track_B/centroid_0232.npy  \n", "  inflating: data_track_B/centroid_0234.npy  \n", "  inflating: data_track_B/centroid_0235.npy  \n", "  inflating: data_track_B/centroid_0236.npy  \n", "  inflating: data_track_B/centroid_0238.npy  \n", "  inflating: data_track_B/centroid_0239.npy  \n", "  inflating: data_track_B/centroid_0240.npy  \n", "  inflating: data_track_B/centroid_0241.npy  \n", "  inflating: data_track_B/centroid_0245.npy  \n", "  inflating: data_track_B/centroid_0246.npy  \n", "  inflating: data_track_B/centroid_0247.npy  \n", "  inflating: data_track_B/centroid_0248.npy  \n", "  inflating: data_track_B/centroid_0249.npy  \n", "  inflating: data_track_B/centroid_0252.npy  \n", "  inflating: data_track_B/centroid_0253.npy  \n", "  inflating: data_track_B/centroid_0254.npy  \n", "  inflating: data_track_B/centroid_0256.npy  \n", "  inflating: data_track_B/centroid_0257.npy  \n", "  inflating: data_track_B/centroid_0259.npy  \n", "  inflating: data_track_B/centroid_0264.npy  \n", "  inflating: data_track_B/centroid_0265.npy  \n", "  inflating: data_track_B/centroid_0266.npy  \n", "  inflating: data_track_B/centroid_0268.npy  \n", "  inflating: data_track_B/centroid_0269.npy  \n", "  inflating: data_track_B/centroid_0271.npy  \n", "  inflating: data_track_B/centroid_0272.npy  \n", "  inflating: data_track_B/centroid_0273.npy  \n", "  inflating: data_track_B/centroid_0275.npy  \n", "  inflating: data_track_B/centroid_0276.npy  \n", "  inflating: data_track_B/centroid_0277.npy  \n", "  inflating: data_track_B/centroid_0279.npy  \n", "  inflating: data_track_B/centroid_0280.npy  \n", "  inflating: data_track_B/centroid_0281.npy  \n", "  inflating: data_track_B/centroid_0284.npy  \n", "  inflating: data_track_B/centroid_0285.npy  \n", "  inflating: data_track_B/centroid_0286.npy  \n", "  inflating: data_track_B/centroid_0288.npy  \n", "  inflating: data_track_B/centroid_0289.npy  \n", "  inflating: data_track_B/centroid_0290.npy  \n", "  inflating: data_track_B/centroid_0291.npy  \n", "  inflating: data_track_B/centroid_0294.npy  \n", "  inflating: data_track_B/centroid_0296.npy  \n", "  inflating: data_track_B/centroid_0297.npy  \n", "  inflating: data_track_B/centroid_0298.npy  \n", "  inflating: data_track_B/centroid_0301.npy  \n", "  inflating: data_track_B/centroid_0304.npy  \n", "  inflating: data_track_B/centroid_0305.npy  \n", "  inflating: data_track_B/centroid_0306.npy  \n", "  inflating: data_track_B/centroid_0307.npy  \n", "  inflating: data_track_B/centroid_0308.npy  \n", "  inflating: data_track_B/centroid_0310.npy  \n", "  inflating: data_track_B/centroid_0311.npy  \n", "  inflating: data_track_B/centroid_0314.npy  \n", "  inflating: data_track_B/centroid_0315.npy  \n", "  inflating: data_track_B/centroid_0316.npy  \n", "  inflating: data_track_B/centroid_0320.npy  \n", "  inflating: data_track_B/centroid_0321.npy  \n", "  inflating: data_track_B/centroid_0323.npy  \n", "  inflating: data_track_B/centroid_0324.npy  \n", "  inflating: data_track_B/centroid_0327.npy  \n", "  inflating: data_track_B/centroid_0330.npy  \n", "  inflating: data_track_B/centroid_0331.npy  \n", "  inflating: data_track_B/centroid_0332.npy  \n", "  inflating: data_track_B/centroid_0333.npy  \n", "  inflating: data_track_B/centroid_0334.npy  \n", "  inflating: data_track_B/centroid_0337.npy  \n", "  inflating: data_track_B/centroid_0338.npy  \n", "  inflating: data_track_B/centroid_0339.npy  \n", "  inflating: data_track_B/centroid_0340.npy  \n", "  inflating: data_track_B/centroid_0341.npy  \n", "  inflating: data_track_B/centroid_0342.npy  \n", "  inflating: data_track_B/centroid_0343.npy  \n", "  inflating: data_track_B/centroid_0344.npy  \n", "  inflating: data_track_B/centroid_0345.npy  \n", "  inflating: data_track_B/centroid_0346.npy  \n", "  inflating: data_track_B/centroid_0348.npy  \n", "  inflating: data_track_B/centroid_0349.npy  \n", "  inflating: data_track_B/centroid_0351.npy  \n", "  inflating: data_track_B/centroid_0352.npy  \n", "  inflating: data_track_B/centroid_0353.npy  \n", "  inflating: data_track_B/centroid_0354.npy  \n", "  inflating: data_track_B/centroid_0356.npy  \n", "  inflating: data_track_B/centroid_0357.npy  \n", "  inflating: data_track_B/centroid_0359.npy  \n", "  inflating: data_track_B/centroid_0360.npy  \n", "  inflating: data_track_B/centroid_0361.npy  \n", "  inflating: data_track_B/centroid_0363.npy  \n", "  inflating: data_track_B/centroid_0364.npy  \n", "  inflating: data_track_B/centroid_0365.npy  \n", "  inflating: data_track_B/centroid_0366.npy  \n", "  inflating: data_track_B/centroid_0367.npy  \n", "  inflating: data_track_B/centroid_0368.npy  \n", "  inflating: data_track_B/centroid_0369.npy  \n", "  inflating: data_track_B/centroid_0371.npy  \n", "  inflating: data_track_B/centroid_0373.npy  \n", "  inflating: data_track_B/centroid_0376.npy  \n", "  inflating: data_track_B/centroid_0377.npy  \n", "  inflating: data_track_B/centroid_0378.npy  \n", "  inflating: data_track_B/centroid_0379.npy  \n", "  inflating: data_track_B/centroid_0381.npy  \n", "  inflating: data_track_B/centroid_0382.npy  \n", "  inflating: data_track_B/centroid_0383.npy  \n", "  inflating: data_track_B/centroid_0384.npy  \n", "  inflating: data_track_B/centroid_0385.npy  \n", "  inflating: data_track_B/centroid_0387.npy  \n", "  inflating: data_track_B/centroid_0388.npy  \n", "  inflating: data_track_B/centroid_0389.npy  \n", "  inflating: data_track_B/centroid_0392.npy  \n", "  inflating: data_track_B/centroid_0393.npy  \n", "  inflating: data_track_B/centroid_0394.npy  \n", "  inflating: data_track_B/centroid_0395.npy  \n", "  inflating: data_track_B/centroid_0396.npy  \n", "  inflating: data_track_B/centroid_0398.npy  \n", "  inflating: data_track_B/centroid_0399.npy  \n", "  inflating: data_track_B/centroid_0400.npy  \n", "  inflating: data_track_B/centroid_0401.npy  \n", "  inflating: data_track_B/centroid_0402.npy  \n", "  inflating: data_track_B/centroid_0403.npy  \n", "  inflating: data_track_B/centroid_0404.npy  \n", "  inflating: data_track_B/centroid_0405.npy  \n", "  inflating: data_track_B/centroid_0407.npy  \n", "  inflating: data_track_B/centroid_0408.npy  \n", "  inflating: data_track_B/centroid_0409.npy  \n", "  inflating: data_track_B/centroid_0410.npy  \n", "  inflating: data_track_B/centroid_0411.npy  \n", "  inflating: data_track_B/centroid_0413.npy  \n", "  inflating: data_track_B/centroid_0416.npy  \n", "  inflating: data_track_B/centroid_0417.npy  \n", "  inflating: data_track_B/centroid_0421.npy  \n", "  inflating: data_track_B/centroid_0422.npy  \n", "  inflating: data_track_B/centroid_0423.npy  \n", "  inflating: data_track_B/centroid_0424.npy  \n", "  inflating: data_track_B/centroid_0425.npy  \n", "  inflating: data_track_B/centroid_0428.npy  \n", "  inflating: data_track_B/centroid_0429.npy  \n", "  inflating: data_track_B/centroid_0430.npy  \n", "  inflating: data_track_B/centroid_0431.npy  \n", "  inflating: data_track_B/centroid_0432.npy  \n", "  inflating: data_track_B/centroid_0435.npy  \n", "  inflating: data_track_B/centroid_0438.npy  \n", "  inflating: data_track_B/centroid_0439.npy  \n", "  inflating: data_track_B/centroid_0441.npy  \n", "  inflating: data_track_B/centroid_0444.npy  \n", "  inflating: data_track_B/centroid_0445.npy  \n", "  inflating: data_track_B/centroid_0449.npy  \n", "  inflating: data_track_B/centroid_0450.npy  \n", "  inflating: data_track_B/centroid_0451.npy  \n", "  inflating: data_track_B/centroid_0452.npy  \n", "  inflating: data_track_B/centroid_0453.npy  \n", "  inflating: data_track_B/centroid_0456.npy  \n", "  inflating: data_track_B/centroid_0457.npy  \n", "  inflating: data_track_B/centroid_0458.npy  \n", "  inflating: data_track_B/centroid_0459.npy  \n", "  inflating: data_track_B/centroid_0460.npy  \n", "  inflating: data_track_B/centroid_0461.npy  \n", "  inflating: data_track_B/centroid_0463.npy  \n", "  inflating: data_track_B/centroid_0464.npy  \n", "  inflating: data_track_B/centroid_0465.npy  \n", "  inflating: data_track_B/centroid_0467.npy  \n", "  inflating: data_track_B/centroid_0469.npy  \n", "  inflating: data_track_B/centroid_0471.npy  \n", "  inflating: data_track_B/centroid_0472.npy  \n", "  inflating: data_track_B/centroid_0474.npy  \n", "  inflating: data_track_B/centroid_0475.npy  \n", "  inflating: data_track_B/centroid_0477.npy  \n", "  inflating: data_track_B/centroid_0478.npy  \n", "  inflating: data_track_B/centroid_0479.npy  \n", "  inflating: data_track_B/centroid_0480.npy  \n", "  inflating: data_track_B/centroid_0481.npy  \n", "  inflating: data_track_B/centroid_0482.npy  \n", "  inflating: data_track_B/centroid_0485.npy  \n", "  inflating: data_track_B/centroid_0486.npy  \n", "  inflating: data_track_B/centroid_0487.npy  \n", "  inflating: data_track_B/centroid_0488.npy  \n", "  inflating: data_track_B/centroid_0489.npy  \n", "  inflating: data_track_B/centroid_0492.npy  \n", "  inflating: data_track_B/centroid_0493.npy  \n", "  inflating: data_track_B/centroid_0494.npy  \n", "  inflating: data_track_B/centroid_0497.npy  \n", "  inflating: data_track_B/centroid_0498.npy  \n", "  inflating: data_track_B/centroid_0499.npy  \n", "  inflating: data_track_B/centroid_0501.npy  \n", "  inflating: data_track_B/centroid_0502.npy  \n", "  inflating: data_track_B/centroid_0503.npy  \n", "  inflating: data_track_B/centroid_0504.npy  \n", "  inflating: data_track_B/centroid_0507.npy  \n", "  inflating: data_track_B/centroid_0508.npy  \n", "  inflating: data_track_B/centroid_0509.npy  \n", "  inflating: data_track_B/centroid_0513.npy  \n", "  inflating: data_track_B/centroid_0514.npy  \n", "  inflating: data_track_B/centroid_0515.npy  \n", "  inflating: data_track_B/centroid_0517.npy  \n", "  inflating: data_track_B/centroid_0518.npy  \n", "  inflating: data_track_B/centroid_0519.npy  \n", "  inflating: data_track_B/centroid_0520.npy  \n", "  inflating: data_track_B/centroid_0521.npy  \n", "  inflating: data_track_B/centroid_0522.npy  \n", "  inflating: data_track_B/centroid_0523.npy  \n", "  inflating: data_track_B/centroid_0524.npy  \n", "  inflating: data_track_B/centroid_0525.npy  \n", "  inflating: data_track_B/centroid_0526.npy  \n", "  inflating: data_track_B/centroid_0527.npy  \n", "  inflating: data_track_B/centroid_0528.npy  \n", "  inflating: data_track_B/centroid_0529.npy  \n", "  inflating: data_track_B/centroid_0530.npy  \n", "  inflating: data_track_B/centroid_0531.npy  \n", "  inflating: data_track_B/centroid_0534.npy  \n", "  inflating: data_track_B/centroid_0535.npy  \n", "  inflating: data_track_B/centroid_0536.npy  \n", "  inflating: data_track_B/centroid_0538.npy  \n", "  inflating: data_track_B/centroid_0541.npy  \n", "  inflating: data_track_B/centroid_0542.npy  \n", "  inflating: data_track_B/centroid_0544.npy  \n", "  inflating: data_track_B/centroid_0545.npy  \n", "  inflating: data_track_B/centroid_0546.npy  \n", "  inflating: data_track_B/centroid_0547.npy  \n", "  inflating: data_track_B/centroid_0550.npy  \n", "  inflating: data_track_B/centroid_0551.npy  \n", "  inflating: data_track_B/centroid_0553.npy  \n", "  inflating: data_track_B/centroid_0555.npy  \n", "  inflating: data_track_B/centroid_0557.npy  \n", "  inflating: data_track_B/centroid_0558.npy  \n", "  inflating: data_track_B/centroid_0561.npy  \n", "  inflating: data_track_B/centroid_0563.npy  \n", "  inflating: data_track_B/centroid_0564.npy  \n", "  inflating: data_track_B/centroid_0565.npy  \n", "  inflating: data_track_B/centroid_0567.npy  \n", "  inflating: data_track_B/centroid_0568.npy  \n", "  inflating: data_track_B/centroid_0571.npy  \n", "  inflating: data_track_B/centroid_0574.npy  \n", "  inflating: data_track_B/centroid_0576.npy  \n", "  inflating: data_track_B/centroid_0579.npy  \n", "  inflating: data_track_B/centroid_0580.npy  \n", "  inflating: data_track_B/centroid_0582.npy  \n", "  inflating: data_track_B/centroid_0584.npy  \n", "  inflating: data_track_B/centroid_0585.npy  \n", "  inflating: data_track_B/centroid_0588.npy  \n", "  inflating: data_track_B/centroid_0589.npy  \n", "  inflating: data_track_B/centroid_0590.npy  \n", "  inflating: data_track_B/centroid_0591.npy  \n", "  inflating: data_track_B/centroid_0592.npy  \n", "  inflating: data_track_B/centroid_0593.npy  \n", "  inflating: data_track_B/centroid_0594.npy  \n", "  inflating: data_track_B/centroid_0595.npy  \n", "  inflating: data_track_B/centroid_0596.npy  \n", "  inflating: data_track_B/centroid_0597.npy  \n", "  inflating: data_track_B/centroid_0598.npy  \n", "  inflating: data_track_B/centroid_0600.npy  \n", "  inflating: data_track_B/centroid_0602.npy  \n", "  inflating: data_track_B/centroid_0605.npy  \n", "  inflating: data_track_B/centroid_0608.npy  \n", "  inflating: data_track_B/centroid_0609.npy  \n", "  inflating: data_track_B/centroid_0611.npy  \n", "  inflating: data_track_B/centroid_0612.npy  \n", "  inflating: data_track_B/centroid_0613.npy  \n", "  inflating: data_track_B/centroid_0614.npy  \n", "  inflating: data_track_B/centroid_0618.npy  \n", "  inflating: data_track_B/centroid_0619.npy  \n", "  inflating: data_track_B/centroid_0620.npy  \n", "  inflating: data_track_B/centroid_0621.npy  \n", "  inflating: data_track_B/centroid_0622.npy  \n", "  inflating: data_track_B/centroid_0623.npy  \n", "  inflating: data_track_B/centroid_0624.npy  \n", "  inflating: data_track_B/centroid_0625.npy  \n", "  inflating: data_track_B/centroid_0627.npy  \n", "  inflating: data_track_B/centroid_0628.npy  \n", "  inflating: data_track_B/centroid_0629.npy  \n", "  inflating: data_track_B/centroid_0630.npy  \n", "  inflating: data_track_B/centroid_0631.npy  \n", "  inflating: data_track_B/centroid_0632.npy  \n", "  inflating: data_track_B/centroid_0633.npy  \n", "  inflating: data_track_B/centroid_0634.npy  \n", "  inflating: data_track_B/centroid_0635.npy  \n", "  inflating: data_track_B/centroid_0637.npy  \n", "  inflating: data_track_B/centroid_0638.npy  \n", "  inflating: data_track_B/centroid_0639.npy  \n", "  inflating: data_track_B/centroid_0640.npy  \n", "  inflating: data_track_B/centroid_0641.npy  \n", "  inflating: data_track_B/centroid_0643.npy  \n", "  inflating: data_track_B/centroid_0644.npy  \n", "  inflating: data_track_B/centroid_0645.npy  \n", "  inflating: data_track_B/centroid_0646.npy  \n", "  inflating: data_track_B/centroid_0648.npy  \n", "  inflating: data_track_B/centroid_0650.npy  \n", "  inflating: data_track_B/centroid_0651.npy  \n", "  inflating: data_track_B/centroid_0652.npy  \n", "  inflating: data_track_B/centroid_0653.npy  \n", "  inflating: data_track_B/centroid_0654.npy  \n", "  inflating: data_track_B/centroid_0656.npy  \n", "  inflating: data_track_B/centroid_0657.npy  \n", "  inflating: data_track_B/centroid_0658.npy  \n", "  inflating: data_track_B/centroid_0661.npy  \n", "  inflating: data_track_B/centroid_0663.npy  \n", "  inflating: data_track_B/centroid_0664.npy  \n", "  inflating: data_track_B/centroid_0665.npy  \n", "  inflating: data_track_B/centroid_0666.npy  \n", "  inflating: data_track_B/centroid_0667.npy  \n", "  inflating: data_track_B/centroid_0668.npy  \n", "  inflating: data_track_B/centroid_0669.npy  \n", "  inflating: data_track_B/centroid_0671.npy  \n", "  inflating: data_track_B/centroid_0672.npy  \n", "  inflating: data_track_B/centroid_0673.npy  \n", "  inflating: data_track_B/centroid_0674.npy  \n", "  inflating: data_track_B/centroid_0676.npy  \n", "  inflating: data_track_B/centroid_0677.npy  \n", "  inflating: data_track_B/centroid_0678.npy  \n", "  inflating: data_track_B/centroid_0679.npy  \n", "  inflating: data_track_B/centroid_0680.npy  \n", "  inflating: data_track_B/centroid_0682.npy  \n", "  inflating: data_track_B/centroid_0686.npy  \n", "  inflating: data_track_B/centroid_0688.npy  \n", "  inflating: data_track_B/centroid_0689.npy  \n", "  inflating: data_track_B/centroid_0690.npy  \n", "  inflating: data_track_B/centroid_0691.npy  \n", "  inflating: data_track_B/centroid_0692.npy  \n", "  inflating: data_track_B/centroid_0693.npy  \n", "  inflating: data_track_B/centroid_0694.npy  \n", "  inflating: data_track_B/centroid_0695.npy  \n", "  inflating: data_track_B/centroid_0697.npy  \n", "  inflating: data_track_B/centroid_0699.npy  \n", "  inflating: data_track_B/centroid_0700.npy  \n", "  inflating: data_track_B/centroid_0701.npy  \n", "  inflating: data_track_B/centroid_0703.npy  \n", "  inflating: data_track_B/centroid_0704.npy  \n", "  inflating: data_track_B/centroid_0706.npy  \n", "  inflating: data_track_B/centroid_0707.npy  \n", "  inflating: data_track_B/centroid_0708.npy  \n", "  inflating: data_track_B/centroid_0709.npy  \n", "  inflating: data_track_B/centroid_0711.npy  \n", "  inflating: data_track_B/centroid_0712.npy  \n", "  inflating: data_track_B/centroid_0713.npy  \n", "  inflating: data_track_B/centroid_0714.npy  \n", "  inflating: data_track_B/centroid_0715.npy  \n", "  inflating: data_track_B/centroid_0716.npy  \n", "  inflating: data_track_B/centroid_0718.npy  \n", "  inflating: data_track_B/centroid_0719.npy  \n", "  inflating: data_track_B/centroid_0720.npy  \n", "  inflating: data_track_B/centroid_0721.npy  \n", "  inflating: data_track_B/centroid_0722.npy  \n", "  inflating: data_track_B/centroid_0724.npy  \n", "  inflating: data_track_B/centroid_0727.npy  \n", "  inflating: data_track_B/centroid_0728.npy  \n", "  inflating: data_track_B/centroid_0729.npy  \n", "  inflating: data_track_B/centroid_0730.npy  \n", "  inflating: data_track_B/centroid_0731.npy  \n", "  inflating: data_track_B/centroid_0733.npy  \n", "  inflating: data_track_B/centroid_0735.npy  \n", "  inflating: data_track_B/centroid_0736.npy  \n", "  inflating: data_track_B/centroid_0737.npy  \n", "  inflating: data_track_B/centroid_0740.npy  \n", "  inflating: data_track_B/centroid_0742.npy  \n", "  inflating: data_track_B/centroid_0743.npy  \n", "  inflating: data_track_B/centroid_0744.npy  \n", "  inflating: data_track_B/centroid_0745.npy  \n", "  inflating: data_track_B/press_0002.npy  \n", "  inflating: data_track_B/press_0003.npy  \n", "  inflating: data_track_B/press_0004.npy  \n", "  inflating: data_track_B/press_0005.npy  \n", "  inflating: data_track_B/press_0006.npy  \n", "  inflating: data_track_B/press_0011.npy  \n", "  inflating: data_track_B/press_0012.npy  \n", "  inflating: data_track_B/press_0013.npy  \n", "  inflating: data_track_B/press_0015.npy  \n", "  inflating: data_track_B/press_0017.npy  \n", "  inflating: data_track_B/press_0018.npy  \n", "  inflating: data_track_B/press_0020.npy  \n", "  inflating: data_track_B/press_0021.npy  \n", "  inflating: data_track_B/press_0022.npy  \n", "  inflating: data_track_B/press_0023.npy  \n", "  inflating: data_track_B/press_0024.npy  \n", "  inflating: data_track_B/press_0026.npy  \n", "  inflating: data_track_B/press_0029.npy  \n", "  inflating: data_track_B/press_0030.npy  \n", "  inflating: data_track_B/press_0036.npy  \n", "  inflating: data_track_B/press_0037.npy  \n", "  inflating: data_track_B/press_0038.npy  \n", "  inflating: data_track_B/press_0039.npy  \n", "  inflating: data_track_B/press_0040.npy  \n", "  inflating: data_track_B/press_0041.npy  \n", "  inflating: data_track_B/press_0042.npy  \n", "  inflating: data_track_B/press_0043.npy  \n", "  inflating: data_track_B/press_0044.npy  \n", "  inflating: data_track_B/press_0048.npy  \n", "  inflating: data_track_B/press_0049.npy  \n", "  inflating: data_track_B/press_0051.npy  \n", "  inflating: data_track_B/press_0052.npy  \n", "  inflating: data_track_B/press_0055.npy  \n", "  inflating: data_track_B/press_0056.npy  \n", "  inflating: data_track_B/press_0057.npy  \n", "  inflating: data_track_B/press_0059.npy  \n", "  inflating: data_track_B/press_0062.npy  \n", "  inflating: data_track_B/press_0064.npy  \n", "  inflating: data_track_B/press_0066.npy  \n", "  inflating: data_track_B/press_0067.npy  \n", "  inflating: data_track_B/press_0068.npy  \n", "  inflating: data_track_B/press_0071.npy  \n", "  inflating: data_track_B/press_0074.npy  \n", "  inflating: data_track_B/press_0075.npy  \n", "  inflating: data_track_B/press_0077.npy  \n", "  inflating: data_track_B/press_0078.npy  \n", "  inflating: data_track_B/press_0080.npy  \n", "  inflating: data_track_B/press_0081.npy  \n", "  inflating: data_track_B/press_0082.npy  \n", "  inflating: data_track_B/press_0084.npy  \n", "  inflating: data_track_B/press_0085.npy  \n", "  inflating: data_track_B/press_0086.npy  \n", "  inflating: data_track_B/press_0087.npy  \n", "  inflating: data_track_B/press_0088.npy  \n", "  inflating: data_track_B/press_0089.npy  \n", "  inflating: data_track_B/press_0090.npy  \n", "  inflating: data_track_B/press_0092.npy  \n", "  inflating: data_track_B/press_0093.npy  \n", "  inflating: data_track_B/press_0094.npy  \n", "  inflating: data_track_B/press_0095.npy  \n", "  inflating: data_track_B/press_0097.npy  \n", "  inflating: data_track_B/press_0098.npy  \n", "  inflating: data_track_B/press_0100.npy  \n", "  inflating: data_track_B/press_0101.npy  \n", "  inflating: data_track_B/press_0102.npy  \n", "  inflating: data_track_B/press_0103.npy  \n", "  inflating: data_track_B/press_0104.npy  \n", "  inflating: data_track_B/press_0106.npy  \n", "  inflating: data_track_B/press_0107.npy  \n", "  inflating: data_track_B/press_0108.npy  \n", "  inflating: data_track_B/press_0109.npy  \n", "  inflating: data_track_B/press_0110.npy  \n", "  inflating: data_track_B/press_0113.npy  \n", "  inflating: data_track_B/press_0114.npy  \n", "  inflating: data_track_B/press_0115.npy  \n", "  inflating: data_track_B/press_0116.npy  \n", "  inflating: data_track_B/press_0117.npy  \n", "  inflating: data_track_B/press_0118.npy  \n", "  inflating: data_track_B/press_0119.npy  \n", "  inflating: data_track_B/press_0120.npy  \n", "  inflating: data_track_B/press_0121.npy  \n", "  inflating: data_track_B/press_0122.npy  \n", "  inflating: data_track_B/press_0124.npy  \n", "  inflating: data_track_B/press_0125.npy  \n", "  inflating: data_track_B/press_0126.npy  \n", "  inflating: data_track_B/press_0128.npy  \n", "  inflating: data_track_B/press_0129.npy  \n", "  inflating: data_track_B/press_0130.npy  \n", "  inflating: data_track_B/press_0131.npy  \n", "  inflating: data_track_B/press_0132.npy  \n", "  inflating: data_track_B/press_0133.npy  \n", "  inflating: data_track_B/press_0134.npy  \n", "  inflating: data_track_B/press_0135.npy  \n", "  inflating: data_track_B/press_0136.npy  \n", "  inflating: data_track_B/press_0138.npy  \n", "  inflating: data_track_B/press_0139.npy  \n", "  inflating: data_track_B/press_0140.npy  \n", "  inflating: data_track_B/press_0141.npy  \n", "  inflating: data_track_B/press_0143.npy  \n", "  inflating: data_track_B/press_0145.npy  \n", "  inflating: data_track_B/press_0146.npy  \n", "  inflating: data_track_B/press_0148.npy  \n", "  inflating: data_track_B/press_0149.npy  \n", "  inflating: data_track_B/press_0150.npy  \n", "  inflating: data_track_B/press_0151.npy  \n", "  inflating: data_track_B/press_0153.npy  \n", "  inflating: data_track_B/press_0154.npy  \n", "  inflating: data_track_B/press_0156.npy  \n", "  inflating: data_track_B/press_0157.npy  \n", "  inflating: data_track_B/press_0158.npy  \n", "  inflating: data_track_B/press_0161.npy  \n", "  inflating: data_track_B/press_0162.npy  \n", "  inflating: data_track_B/press_0163.npy  \n", "  inflating: data_track_B/press_0164.npy  \n", "  inflating: data_track_B/press_0166.npy  \n", "  inflating: data_track_B/press_0167.npy  \n", "  inflating: data_track_B/press_0168.npy  \n", "  inflating: data_track_B/press_0170.npy  \n", "  inflating: data_track_B/press_0171.npy  \n", "  inflating: data_track_B/press_0172.npy  \n", "  inflating: data_track_B/press_0174.npy  \n", "  inflating: data_track_B/press_0175.npy  \n", "  inflating: data_track_B/press_0183.npy  \n", "  inflating: data_track_B/press_0184.npy  \n", "  inflating: data_track_B/press_0185.npy  \n", "  inflating: data_track_B/press_0189.npy  \n", "  inflating: data_track_B/press_0190.npy  \n", "  inflating: data_track_B/press_0193.npy  \n", "  inflating: data_track_B/press_0194.npy  \n", "  inflating: data_track_B/press_0195.npy  \n", "  inflating: data_track_B/press_0197.npy  \n", "  inflating: data_track_B/press_0201.npy  \n", "  inflating: data_track_B/press_0203.npy  \n", "  inflating: data_track_B/press_0204.npy  \n", "  inflating: data_track_B/press_0205.npy  \n", "  inflating: data_track_B/press_0206.npy  \n", "  inflating: data_track_B/press_0208.npy  \n", "  inflating: data_track_B/press_0210.npy  \n", "  inflating: data_track_B/press_0211.npy  \n", "  inflating: data_track_B/press_0216.npy  \n", "  inflating: data_track_B/press_0217.npy  \n", "  inflating: data_track_B/press_0219.npy  \n", "  inflating: data_track_B/press_0220.npy  \n", "  inflating: data_track_B/press_0227.npy  \n", "  inflating: data_track_B/press_0228.npy  \n", "  inflating: data_track_B/press_0229.npy  \n", "  inflating: data_track_B/press_0232.npy  \n", "  inflating: data_track_B/press_0234.npy  \n", "  inflating: data_track_B/press_0235.npy  \n", "  inflating: data_track_B/press_0236.npy  \n", "  inflating: data_track_B/press_0238.npy  \n", "  inflating: data_track_B/press_0239.npy  \n", "  inflating: data_track_B/press_0240.npy  \n", "  inflating: data_track_B/press_0241.npy  \n", "  inflating: data_track_B/press_0245.npy  \n", "  inflating: data_track_B/press_0246.npy  \n", "  inflating: data_track_B/press_0247.npy  \n", "  inflating: data_track_B/press_0248.npy  \n", "  inflating: data_track_B/press_0249.npy  \n", "  inflating: data_track_B/press_0252.npy  \n", "  inflating: data_track_B/press_0253.npy  \n", "  inflating: data_track_B/press_0254.npy  \n", "  inflating: data_track_B/press_0256.npy  \n", "  inflating: data_track_B/press_0257.npy  \n", "  inflating: data_track_B/press_0259.npy  \n", "  inflating: data_track_B/press_0264.npy  \n", "  inflating: data_track_B/press_0265.npy  \n", "  inflating: data_track_B/press_0266.npy  \n", "  inflating: data_track_B/press_0268.npy  \n", "  inflating: data_track_B/press_0269.npy  \n", "  inflating: data_track_B/press_0271.npy  \n", "  inflating: data_track_B/press_0272.npy  \n", "  inflating: data_track_B/press_0273.npy  \n", "  inflating: data_track_B/press_0275.npy  \n", "  inflating: data_track_B/press_0276.npy  \n", "  inflating: data_track_B/press_0277.npy  \n", "  inflating: data_track_B/press_0279.npy  \n", "  inflating: data_track_B/press_0280.npy  \n", "  inflating: data_track_B/press_0281.npy  \n", "  inflating: data_track_B/press_0284.npy  \n", "  inflating: data_track_B/press_0285.npy  \n", "  inflating: data_track_B/press_0286.npy  \n", "  inflating: data_track_B/press_0288.npy  \n", "  inflating: data_track_B/press_0289.npy  \n", "  inflating: data_track_B/press_0290.npy  \n", "  inflating: data_track_B/press_0291.npy  \n", "  inflating: data_track_B/press_0294.npy  \n", "  inflating: data_track_B/press_0296.npy  \n", "  inflating: data_track_B/press_0297.npy  \n", "  inflating: data_track_B/press_0298.npy  \n", "  inflating: data_track_B/press_0301.npy  \n", "  inflating: data_track_B/press_0304.npy  \n", "  inflating: data_track_B/press_0305.npy  \n", "  inflating: data_track_B/press_0306.npy  \n", "  inflating: data_track_B/press_0307.npy  \n", "  inflating: data_track_B/press_0308.npy  \n", "  inflating: data_track_B/press_0310.npy  \n", "  inflating: data_track_B/press_0311.npy  \n", "  inflating: data_track_B/press_0314.npy  \n", "  inflating: data_track_B/press_0315.npy  \n", "  inflating: data_track_B/press_0316.npy  \n", "  inflating: data_track_B/press_0320.npy  \n", "  inflating: data_track_B/press_0321.npy  \n", "  inflating: data_track_B/press_0323.npy  \n", "  inflating: data_track_B/press_0324.npy  \n", "  inflating: data_track_B/press_0327.npy  \n", "  inflating: data_track_B/press_0330.npy  \n", "  inflating: data_track_B/press_0331.npy  \n", "  inflating: data_track_B/press_0332.npy  \n", "  inflating: data_track_B/press_0333.npy  \n", "  inflating: data_track_B/press_0334.npy  \n", "  inflating: data_track_B/press_0337.npy  \n", "  inflating: data_track_B/press_0338.npy  \n", "  inflating: data_track_B/press_0339.npy  \n", "  inflating: data_track_B/press_0340.npy  \n", "  inflating: data_track_B/press_0341.npy  \n", "  inflating: data_track_B/press_0342.npy  \n", "  inflating: data_track_B/press_0343.npy  \n", "  inflating: data_track_B/press_0344.npy  \n", "  inflating: data_track_B/press_0345.npy  \n", "  inflating: data_track_B/press_0346.npy  \n", "  inflating: data_track_B/press_0348.npy  \n", "  inflating: data_track_B/press_0349.npy  \n", "  inflating: data_track_B/press_0351.npy  \n", "  inflating: data_track_B/press_0352.npy  \n", "  inflating: data_track_B/press_0353.npy  \n", "  inflating: data_track_B/press_0354.npy  \n", "  inflating: data_track_B/press_0356.npy  \n", "  inflating: data_track_B/press_0357.npy  \n", "  inflating: data_track_B/press_0359.npy  \n", "  inflating: data_track_B/press_0360.npy  \n", "  inflating: data_track_B/press_0361.npy  \n", "  inflating: data_track_B/press_0363.npy  \n", "  inflating: data_track_B/press_0364.npy  \n", "  inflating: data_track_B/press_0365.npy  \n", "  inflating: data_track_B/press_0366.npy  \n", "  inflating: data_track_B/press_0367.npy  \n", "  inflating: data_track_B/press_0368.npy  \n", "  inflating: data_track_B/press_0369.npy  \n", "  inflating: data_track_B/press_0371.npy  \n", "  inflating: data_track_B/press_0373.npy  \n", "  inflating: data_track_B/press_0376.npy  \n", "  inflating: data_track_B/press_0377.npy  \n", "  inflating: data_track_B/press_0378.npy  \n", "  inflating: data_track_B/press_0379.npy  \n", "  inflating: data_track_B/press_0381.npy  \n", "  inflating: data_track_B/press_0382.npy  \n", "  inflating: data_track_B/press_0383.npy  \n", "  inflating: data_track_B/press_0384.npy  \n", "  inflating: data_track_B/press_0385.npy  \n", "  inflating: data_track_B/press_0387.npy  \n", "  inflating: data_track_B/press_0388.npy  \n", "  inflating: data_track_B/press_0389.npy  \n", "  inflating: data_track_B/press_0392.npy  \n", "  inflating: data_track_B/press_0393.npy  \n", "  inflating: data_track_B/press_0394.npy  \n", "  inflating: data_track_B/press_0395.npy  \n", "  inflating: data_track_B/press_0396.npy  \n", "  inflating: data_track_B/press_0398.npy  \n", "  inflating: data_track_B/press_0399.npy  \n", "  inflating: data_track_B/press_0400.npy  \n", "  inflating: data_track_B/press_0401.npy  \n", "  inflating: data_track_B/press_0402.npy  \n", "  inflating: data_track_B/press_0403.npy  \n", "  inflating: data_track_B/press_0404.npy  \n", "  inflating: data_track_B/press_0405.npy  \n", "  inflating: data_track_B/press_0407.npy  \n", "  inflating: data_track_B/press_0408.npy  \n", "  inflating: data_track_B/press_0409.npy  \n", "  inflating: data_track_B/press_0410.npy  \n", "  inflating: data_track_B/press_0411.npy  \n", "  inflating: data_track_B/press_0413.npy  \n", "  inflating: data_track_B/press_0416.npy  \n", "  inflating: data_track_B/press_0417.npy  \n", "  inflating: data_track_B/press_0421.npy  \n", "  inflating: data_track_B/press_0422.npy  \n", "  inflating: data_track_B/press_0423.npy  \n", "  inflating: data_track_B/press_0424.npy  \n", "  inflating: data_track_B/press_0425.npy  \n", "  inflating: data_track_B/press_0428.npy  \n", "  inflating: data_track_B/press_0429.npy  \n", "  inflating: data_track_B/press_0430.npy  \n", "  inflating: data_track_B/press_0431.npy  \n", "  inflating: data_track_B/press_0432.npy  \n", "  inflating: data_track_B/press_0435.npy  \n", "  inflating: data_track_B/press_0438.npy  \n", "  inflating: data_track_B/press_0439.npy  \n", "  inflating: data_track_B/press_0441.npy  \n", "  inflating: data_track_B/press_0444.npy  \n", "  inflating: data_track_B/press_0445.npy  \n", "  inflating: data_track_B/press_0449.npy  \n", "  inflating: data_track_B/press_0450.npy  \n", "  inflating: data_track_B/press_0451.npy  \n", "  inflating: data_track_B/press_0452.npy  \n", "  inflating: data_track_B/press_0453.npy  \n", "  inflating: data_track_B/press_0456.npy  \n", "  inflating: data_track_B/press_0457.npy  \n", "  inflating: data_track_B/press_0458.npy  \n", "  inflating: data_track_B/press_0459.npy  \n", "  inflating: data_track_B/press_0460.npy  \n", "  inflating: data_track_B/press_0461.npy  \n", "  inflating: data_track_B/press_0463.npy  \n", "  inflating: data_track_B/press_0464.npy  \n", "  inflating: data_track_B/press_0465.npy  \n", "  inflating: data_track_B/press_0467.npy  \n", "  inflating: data_track_B/press_0469.npy  \n", "  inflating: data_track_B/press_0471.npy  \n", "  inflating: data_track_B/press_0472.npy  \n", "  inflating: data_track_B/press_0474.npy  \n", "  inflating: data_track_B/press_0475.npy  \n", "  inflating: data_track_B/press_0477.npy  \n", "  inflating: data_track_B/press_0478.npy  \n", "  inflating: data_track_B/press_0479.npy  \n", "  inflating: data_track_B/press_0480.npy  \n", "  inflating: data_track_B/press_0481.npy  \n", "  inflating: data_track_B/press_0482.npy  \n", "  inflating: data_track_B/press_0485.npy  \n", "  inflating: data_track_B/press_0486.npy  \n", "  inflating: data_track_B/press_0487.npy  \n", "  inflating: data_track_B/press_0488.npy  \n", "  inflating: data_track_B/press_0489.npy  \n", "  inflating: data_track_B/press_0492.npy  \n", "  inflating: data_track_B/press_0493.npy  \n", "  inflating: data_track_B/press_0494.npy  \n", "  inflating: data_track_B/press_0497.npy  \n", "  inflating: data_track_B/press_0498.npy  \n", "  inflating: data_track_B/press_0499.npy  \n", "  inflating: data_track_B/press_0501.npy  \n", "  inflating: data_track_B/press_0502.npy  \n", "  inflating: data_track_B/press_0503.npy  \n", "  inflating: data_track_B/press_0504.npy  \n", "  inflating: data_track_B/press_0507.npy  \n", "  inflating: data_track_B/press_0508.npy  \n", "  inflating: data_track_B/press_0509.npy  \n", "  inflating: data_track_B/press_0513.npy  \n", "  inflating: data_track_B/press_0514.npy  \n", "  inflating: data_track_B/press_0515.npy  \n", "  inflating: data_track_B/press_0517.npy  \n", "  inflating: data_track_B/press_0518.npy  \n", "  inflating: data_track_B/press_0519.npy  \n", "  inflating: data_track_B/press_0520.npy  \n", "  inflating: data_track_B/press_0521.npy  \n", "  inflating: data_track_B/press_0522.npy  \n", "  inflating: data_track_B/press_0523.npy  \n", "  inflating: data_track_B/press_0524.npy  \n", "  inflating: data_track_B/press_0525.npy  \n", "  inflating: data_track_B/press_0526.npy  \n", "  inflating: data_track_B/press_0527.npy  \n", "  inflating: data_track_B/press_0528.npy  \n", "  inflating: data_track_B/press_0529.npy  \n", "  inflating: data_track_B/press_0530.npy  \n", "  inflating: data_track_B/press_0531.npy  \n", "  inflating: data_track_B/press_0534.npy  \n", "  inflating: data_track_B/press_0535.npy  \n", "  inflating: data_track_B/press_0536.npy  \n", "  inflating: data_track_B/press_0538.npy  \n", "  inflating: data_track_B/press_0541.npy  \n", "  inflating: data_track_B/press_0542.npy  \n", "  inflating: data_track_B/press_0544.npy  \n", "  inflating: data_track_B/press_0545.npy  \n", "  inflating: data_track_B/press_0546.npy  \n", "  inflating: data_track_B/press_0547.npy  \n", "  inflating: data_track_B/press_0550.npy  \n", "  inflating: data_track_B/press_0551.npy  \n", "  inflating: data_track_B/press_0553.npy  \n", "  inflating: data_track_B/press_0555.npy  \n", "  inflating: data_track_B/press_0557.npy  \n", "  inflating: data_track_B/press_0558.npy  \n", "  inflating: data_track_B/press_0561.npy  \n", "  inflating: data_track_B/press_0563.npy  \n", "  inflating: data_track_B/press_0564.npy  \n", "  inflating: data_track_B/press_0565.npy  \n", "  inflating: data_track_B/press_0567.npy  \n", "  inflating: data_track_B/press_0568.npy  \n", "  inflating: data_track_B/press_0571.npy  \n", "  inflating: data_track_B/press_0574.npy  \n", "  inflating: data_track_B/press_0576.npy  \n", "  inflating: data_track_B/press_0579.npy  \n", "  inflating: data_track_B/press_0580.npy  \n", "  inflating: data_track_B/press_0582.npy  \n", "  inflating: data_track_B/press_0584.npy  \n", "  inflating: data_track_B/press_0585.npy  \n", "  inflating: data_track_B/press_0588.npy  \n", "  inflating: data_track_B/press_0589.npy  \n", "  inflating: data_track_B/press_0590.npy  \n", "  inflating: data_track_B/press_0591.npy  \n", "  inflating: data_track_B/press_0592.npy  \n", "  inflating: data_track_B/press_0593.npy  \n", "  inflating: data_track_B/press_0594.npy  \n", "  inflating: data_track_B/press_0595.npy  \n", "  inflating: data_track_B/press_0596.npy  \n", "  inflating: data_track_B/press_0597.npy  \n", "  inflating: data_track_B/press_0598.npy  \n", "  inflating: data_track_B/press_0600.npy  \n", "  inflating: data_track_B/press_0602.npy  \n", "  inflating: data_track_B/press_0605.npy  \n", "  inflating: data_track_B/press_0608.npy  \n", "  inflating: data_track_B/press_0609.npy  \n", "  inflating: data_track_B/press_0611.npy  \n", "  inflating: data_track_B/press_0612.npy  \n", "  inflating: data_track_B/press_0613.npy  \n", "  inflating: data_track_B/press_0614.npy  \n", "  inflating: data_track_B/press_0618.npy  \n", "  inflating: data_track_B/press_0619.npy  \n", "  inflating: data_track_B/press_0620.npy  \n", "  inflating: data_track_B/press_0621.npy  \n", "  inflating: data_track_B/press_0622.npy  \n", "  inflating: data_track_B/press_0623.npy  \n", "  inflating: data_track_B/press_0624.npy  \n", "  inflating: data_track_B/press_0625.npy  \n", "  inflating: data_track_B/press_0627.npy  \n", "  inflating: data_track_B/press_0628.npy  \n", "  inflating: data_track_B/press_0629.npy  \n", "  inflating: data_track_B/press_0630.npy  \n", "  inflating: data_track_B/press_0631.npy  \n", "  inflating: data_track_B/press_0632.npy  \n", "  inflating: data_track_B/press_0633.npy  \n", "  inflating: data_track_B/press_0634.npy  \n", "  inflating: data_track_B/press_0635.npy  \n", "  inflating: data_track_B/press_0637.npy  \n", "  inflating: data_track_B/press_0638.npy  \n", "  inflating: data_track_B/press_0639.npy  \n", "  inflating: data_track_B/press_0640.npy  \n", "  inflating: data_track_B/press_0641.npy  \n", "  inflating: data_track_B/press_0643.npy  \n", "  inflating: data_track_B/press_0644.npy  \n", "  inflating: data_track_B/press_0645.npy  \n", "  inflating: data_track_B/press_0646.npy  \n", "  inflating: data_track_B/press_0648.npy  \n", "  inflating: data_track_B/press_0650.npy  \n", "  inflating: data_track_B/press_0651.npy  \n", "  inflating: data_track_B/press_0652.npy  \n", "  inflating: data_track_B/press_0653.npy  \n", "  inflating: data_track_B/press_0654.npy  \n", "  inflating: data_track_B/press_0656.npy  \n", "  inflating: data_track_B/press_0657.npy  \n", "  inflating: data_track_B/press_0658.npy  \n", "  inflating: data_track_B/press_0661.npy  \n", "  inflating: data_track_B/press_0663.npy  \n", "  inflating: data_track_B/press_0664.npy  \n", "  inflating: data_track_B/press_0665.npy  \n", "  inflating: data_track_B/press_0666.npy  \n", "  inflating: data_track_B/press_0667.npy  \n", "  inflating: data_track_B/press_0668.npy  \n", "  inflating: data_track_B/press_0669.npy  \n", "  inflating: data_track_B/press_0671.npy  \n", "  inflating: data_track_B/press_0672.npy  \n", "  inflating: data_track_B/press_0673.npy  \n", "  inflating: data_track_B/press_0674.npy  \n", "  inflating: data_track_B/press_0676.npy  \n", "  inflating: data_track_B/press_0677.npy  \n", "  inflating: data_track_B/press_0678.npy  \n", "  inflating: data_track_B/press_0679.npy  \n", "  inflating: data_track_B/press_0680.npy  \n", "  inflating: data_track_B/press_0682.npy  \n", "  inflating: data_track_B/press_0686.npy  \n", "  inflating: data_track_B/press_0688.npy  \n", "  inflating: data_track_B/press_0689.npy  \n", "  inflating: data_track_B/press_0690.npy  \n", "  inflating: data_track_B/press_0691.npy  \n", "  inflating: data_track_B/press_0692.npy  \n", "  inflating: data_track_B/press_0693.npy  \n", "  inflating: data_track_B/press_0694.npy  \n", "  inflating: data_track_B/press_0695.npy  \n", "  inflating: data_track_B/press_0697.npy  \n", "  inflating: data_track_B/press_0699.npy  \n", "  inflating: data_track_B/press_0700.npy  \n", "  inflating: data_track_B/press_0701.npy  \n", "  inflating: data_track_B/press_0703.npy  \n", "  inflating: data_track_B/press_0704.npy  \n", "  inflating: data_track_B/press_0706.npy  \n", "  inflating: data_track_B/press_0707.npy  \n", "  inflating: data_track_B/press_0708.npy  \n", "  inflating: data_track_B/press_0709.npy  \n", "  inflating: data_track_B/press_0711.npy  \n", "  inflating: data_track_B/press_0712.npy  \n", "  inflating: data_track_B/press_0713.npy  \n", "  inflating: data_track_B/press_0714.npy  \n", "  inflating: data_track_B/press_0715.npy  \n", "  inflating: data_track_B/press_0716.npy  \n", "  inflating: data_track_B/press_0718.npy  \n", "  inflating: data_track_B/press_0719.npy  \n", "  inflating: data_track_B/press_0720.npy  \n", "  inflating: data_track_B/press_0721.npy  \n", "  inflating: data_track_B/press_0722.npy  \n", "  inflating: data_track_B/press_0724.npy  \n", "  inflating: data_track_B/press_0727.npy  \n", "  inflating: data_track_B/press_0728.npy  \n", "  inflating: data_track_B/press_0729.npy  \n", "  inflating: data_track_B/press_0730.npy  \n", "  inflating: data_track_B/press_0731.npy  \n", "  inflating: data_track_B/press_0733.npy  \n", "  inflating: data_track_B/press_0735.npy  \n", "  inflating: data_track_B/press_0736.npy  \n", "  inflating: data_track_B/press_0737.npy  \n", "  inflating: data_track_B/press_0740.npy  \n", "  inflating: data_track_B/press_0742.npy  \n", "  inflating: data_track_B/press_0743.npy  \n", "  inflating: data_track_B/press_0744.npy  \n", "  inflating: data_track_B/press_0745.npy  \n", "Archive:  track_B.zip\n", "  inflating: track_B/area_1.npy      \n", "  inflating: track_B/area_10.npy     \n", "  inflating: track_B/area_11.npy     \n", "  inflating: track_B/area_12.npy     \n", "  inflating: track_B/area_13.npy     \n", "  inflating: track_B/area_14.npy     \n", "  inflating: track_B/area_15.npy     \n", "  inflating: track_B/area_16.npy     \n", "  inflating: track_B/area_17.npy     \n", "  inflating: track_B/area_18.npy     \n", "  inflating: track_B/area_19.npy     \n", "  inflating: track_B/area_2.npy      \n", "  inflating: track_B/area_20.npy     \n", "  inflating: track_B/area_21.npy     \n", "  inflating: track_B/area_22.npy     \n", "  inflating: track_B/area_23.npy     \n", "  inflating: track_B/area_24.npy     \n", "  inflating: track_B/area_25.npy     \n", "  inflating: track_B/area_26.npy     \n", "  inflating: track_B/area_27.npy     \n", "  inflating: track_B/area_28.npy     \n", "  inflating: track_B/area_29.npy     \n", "  inflating: track_B/area_3.npy      \n", "  inflating: track_B/area_30.npy     \n", "  inflating: track_B/area_31.npy     \n", "  inflating: track_B/area_32.npy     \n", "  inflating: track_B/area_33.npy     \n", "  inflating: track_B/area_34.npy     \n", "  inflating: track_B/area_35.npy     \n", "  inflating: track_B/area_36.npy     \n", "  inflating: track_B/area_37.npy     \n", "  inflating: track_B/area_38.npy     \n", "  inflating: track_B/area_39.npy     \n", "  inflating: track_B/area_4.npy      \n", "  inflating: track_B/area_40.npy     \n", "  inflating: track_B/area_41.npy     \n", "  inflating: track_B/area_42.npy     \n", "  inflating: track_B/area_43.npy     \n", "  inflating: track_B/area_44.npy     \n", "  inflating: track_B/area_45.npy     \n", "  inflating: track_B/area_46.npy     \n", "  inflating: track_B/area_47.npy     \n", "  inflating: track_B/area_48.npy     \n", "  inflating: track_B/area_49.npy     \n", "  inflating: track_B/area_5.npy      \n", "  inflating: track_B/area_50.npy     \n", "  inflating: track_B/area_6.npy      \n", "  inflating: track_B/area_7.npy      \n", "  inflating: track_B/area_8.npy      \n", "  inflating: track_B/area_9.npy      \n", "  inflating: track_B/area_bounds.txt  \n", "  inflating: track_B/centroid_1.npy  \n", "  inflating: track_B/centroid_10.npy  \n", "  inflating: track_B/centroid_11.npy  \n", "  inflating: track_B/centroid_12.npy  \n", "  inflating: track_B/centroid_13.npy  \n", "  inflating: track_B/centroid_14.npy  \n", "  inflating: track_B/centroid_15.npy  \n", "  inflating: track_B/centroid_16.npy  \n", "  inflating: track_B/centroid_17.npy  \n", "  inflating: track_B/centroid_18.npy  \n", "  inflating: track_B/centroid_19.npy  \n", "  inflating: track_B/centroid_2.npy  \n", "  inflating: track_B/centroid_20.npy  \n", "  inflating: track_B/centroid_21.npy  \n", "  inflating: track_B/centroid_22.npy  \n", "  inflating: track_B/centroid_23.npy  \n", "  inflating: track_B/centroid_24.npy  \n", "  inflating: track_B/centroid_25.npy  \n", "  inflating: track_B/centroid_26.npy  \n", "  inflating: track_B/centroid_27.npy  \n", "  inflating: track_B/centroid_28.npy  \n", "  inflating: track_B/centroid_29.npy  \n", "  inflating: track_B/centroid_3.npy  \n", "  inflating: track_B/centroid_30.npy  \n", "  inflating: track_B/centroid_31.npy  \n", "  inflating: track_B/centroid_32.npy  \n", "  inflating: track_B/centroid_33.npy  \n", "  inflating: track_B/centroid_34.npy  \n", "  inflating: track_B/centroid_35.npy  \n", "  inflating: track_B/centroid_36.npy  \n", "  inflating: track_B/centroid_37.npy  \n", "  inflating: track_B/centroid_38.npy  \n", "  inflating: track_B/centroid_39.npy  \n", "  inflating: track_B/centroid_4.npy  \n", "  inflating: track_B/centroid_40.npy  \n", "  inflating: track_B/centroid_41.npy  \n", "  inflating: track_B/centroid_42.npy  \n", "  inflating: track_B/centroid_43.npy  \n", "  inflating: track_B/centroid_44.npy  \n", "  inflating: track_B/centroid_45.npy  \n", "  inflating: track_B/centroid_46.npy  \n", "  inflating: track_B/centroid_47.npy  \n", "  inflating: track_B/centroid_48.npy  \n", "  inflating: track_B/centroid_49.npy  \n", "  inflating: track_B/centroid_5.npy  \n", "  inflating: track_B/centroid_50.npy  \n", "  inflating: track_B/centroid_6.npy  \n", "  inflating: track_B/centroid_7.npy  \n", "  inflating: track_B/centroid_8.npy  \n", "  inflating: track_B/centroid_9.npy  \n", "  inflating: track_B/global_bounds.txt  \n", "  inflating: track_B/info_1.npy      \n", "  inflating: track_B/info_10.npy     \n", "  inflating: track_B/info_11.npy     \n", "  inflating: track_B/info_12.npy     \n", "  inflating: track_B/info_13.npy     \n", "  inflating: track_B/info_14.npy     \n", "  inflating: track_B/info_15.npy     \n", "  inflating: track_B/info_16.npy     \n", "  inflating: track_B/info_17.npy     \n", "  inflating: track_B/info_18.npy     \n", "  inflating: track_B/info_19.npy     \n", "  inflating: track_B/info_2.npy      \n", "  inflating: track_B/info_20.npy     \n", "  inflating: track_B/info_21.npy     \n", "  inflating: track_B/info_22.npy     \n", "  inflating: track_B/info_23.npy     \n", "  inflating: track_B/info_24.npy     \n", "  inflating: track_B/info_25.npy     \n", "  inflating: track_B/info_26.npy     \n", "  inflating: track_B/info_27.npy     \n", "  inflating: track_B/info_28.npy     \n", "  inflating: track_B/info_29.npy     \n", "  inflating: track_B/info_3.npy      \n", "  inflating: track_B/info_30.npy     \n", "  inflating: track_B/info_31.npy     \n", "  inflating: track_B/info_32.npy     \n", "  inflating: track_B/info_33.npy     \n", "  inflating: track_B/info_34.npy     \n", "  inflating: track_B/info_35.npy     \n", "  inflating: track_B/info_36.npy     \n", "  inflating: track_B/info_37.npy     \n", "  inflating: track_B/info_38.npy     \n", "  inflating: track_B/info_39.npy     \n", "  inflating: track_B/info_4.npy      \n", "  inflating: track_B/info_40.npy     \n", "  inflating: track_B/info_41.npy     \n", "  inflating: track_B/info_42.npy     \n", "  inflating: track_B/info_43.npy     \n", "  inflating: track_B/info_44.npy     \n", "  inflating: track_B/info_45.npy     \n", "  inflating: track_B/info_46.npy     \n", "  inflating: track_B/info_47.npy     \n", "  inflating: track_B/info_48.npy     \n", "  inflating: track_B/info_49.npy     \n", "  inflating: track_B/info_5.npy      \n", "  inflating: track_B/info_50.npy     \n", "  inflating: track_B/info_6.npy      \n", "  inflating: track_B/info_7.npy      \n", "  inflating: track_B/info_8.npy      \n", "  inflating: track_B/info_9.npy      \n", "  inflating: track_B/info_bounds.txt  \n", "  inflating: track_B/mesh_1.ply      \n", "  inflating: track_B/mesh_10.ply     \n", "  inflating: track_B/mesh_11.ply     \n", "  inflating: track_B/mesh_12.ply     \n", "  inflating: track_B/mesh_13.ply     \n", "  inflating: track_B/mesh_14.ply     \n", "  inflating: track_B/mesh_15.ply     \n", "  inflating: track_B/mesh_16.ply     \n", "  inflating: track_B/mesh_17.ply     \n", "  inflating: track_B/mesh_18.ply     \n", "  inflating: track_B/mesh_19.ply     \n", "  inflating: track_B/mesh_2.ply      \n", "  inflating: track_B/mesh_20.ply     \n", "  inflating: track_B/mesh_21.ply     \n", "  inflating: track_B/mesh_22.ply     \n", "  inflating: track_B/mesh_23.ply     \n", "  inflating: track_B/mesh_24.ply     \n", "  inflating: track_B/mesh_25.ply     \n", "  inflating: track_B/mesh_26.ply     \n", "  inflating: track_B/mesh_27.ply     \n", "  inflating: track_B/mesh_28.ply     \n", "  inflating: track_B/mesh_29.ply     \n", "  inflating: track_B/mesh_3.ply      \n", "  inflating: track_B/mesh_30.ply     \n", "  inflating: track_B/mesh_31.ply     \n", "  inflating: track_B/mesh_32.ply     \n", "  inflating: track_B/mesh_33.ply     \n", "  inflating: track_B/mesh_34.ply     \n", "  inflating: track_B/mesh_35.ply     \n", "  inflating: track_B/mesh_36.ply     \n", "  inflating: track_B/mesh_37.ply     \n", "  inflating: track_B/mesh_38.ply     \n", "  inflating: track_B/mesh_39.ply     \n", "  inflating: track_B/mesh_4.ply      \n", "  inflating: track_B/mesh_40.ply     \n", "  inflating: track_B/mesh_41.ply     \n", "  inflating: track_B/mesh_42.ply     \n", "  inflating: track_B/mesh_43.ply     \n", "  inflating: track_B/mesh_44.ply     \n", "  inflating: track_B/mesh_45.ply     \n", "  inflating: track_B/mesh_46.ply     \n", "  inflating: track_B/mesh_47.ply     \n", "  inflating: track_B/mesh_48.ply     \n", "  inflating: track_B/mesh_49.ply     \n", "  inflating: track_B/mesh_5.ply      \n", "  inflating: track_B/mesh_50.ply     \n", "  inflating: track_B/mesh_6.ply      \n", "  inflating: track_B/mesh_7.ply      \n", "  inflating: track_B/mesh_8.ply      \n", "  inflating: track_B/mesh_9.ply      \n", "  inflating: track_B/normal_1.npy    \n", "  inflating: track_B/normal_10.npy   \n", "  inflating: track_B/normal_11.npy   \n", "  inflating: track_B/normal_12.npy   \n", "  inflating: track_B/normal_13.npy   \n", "  inflating: track_B/normal_14.npy   \n", "  inflating: track_B/normal_15.npy   \n", "  inflating: track_B/normal_16.npy   \n", "  inflating: track_B/normal_17.npy   \n", "  inflating: track_B/normal_18.npy   \n", "  inflating: track_B/normal_19.npy   \n", "  inflating: track_B/normal_2.npy    \n", "  inflating: track_B/normal_20.npy   \n", "  inflating: track_B/normal_21.npy   \n", "  inflating: track_B/normal_22.npy   \n", "  inflating: track_B/normal_23.npy   \n", "  inflating: track_B/normal_24.npy   \n", "  inflating: track_B/normal_25.npy   \n", "  inflating: track_B/normal_26.npy   \n", "  inflating: track_B/normal_27.npy   \n", "  inflating: track_B/normal_28.npy   \n", "  inflating: track_B/normal_29.npy   \n", "  inflating: track_B/normal_3.npy    \n", "  inflating: track_B/normal_30.npy   \n", "  inflating: track_B/normal_31.npy   \n", "  inflating: track_B/normal_32.npy   \n", "  inflating: track_B/normal_33.npy   \n", "  inflating: track_B/normal_34.npy   \n", "  inflating: track_B/normal_35.npy   \n", "  inflating: track_B/normal_36.npy   \n", "  inflating: track_B/normal_37.npy   \n", "  inflating: track_B/normal_38.npy   \n", "  inflating: track_B/normal_39.npy   \n", "  inflating: track_B/normal_4.npy    \n", "  inflating: track_B/normal_40.npy   \n", "  inflating: track_B/normal_41.npy   \n", "  inflating: track_B/normal_42.npy   \n", "  inflating: track_B/normal_43.npy   \n", "  inflating: track_B/normal_44.npy   \n", "  inflating: track_B/normal_45.npy   \n", "  inflating: track_B/normal_46.npy   \n", "  inflating: track_B/normal_47.npy   \n", "  inflating: track_B/normal_48.npy   \n", "  inflating: track_B/normal_49.npy   \n", "  inflating: track_B/normal_5.npy    \n", "  inflating: track_B/normal_50.npy   \n", "  inflating: track_B/normal_6.npy    \n", "  inflating: track_B/normal_7.npy    \n", "  inflating: track_B/normal_8.npy    \n", "  inflating: track_B/normal_9.npy    \n", "  inflating: track_B/train_pressure_mean_std.txt  \n", "mkdir: cannot create directory ‘track_B_vtk’: File exists\n", "mkdir: cannot create directory ‘data_centroid_track_B_vtk’: File exists\n", "mkdir: cannot create directory ‘data_centroid_track_B_vtk_preprocessed_data’: File exists\n"]}], "source": ["!mkdir -p train_track_B && unzip -o train_track_B.zip -d data_track_B/\n", "!mkdir -p track_B && unzip -o track_B.zip\n", "!mkdir track_B_vtk\n", "!mkdir data_centroid_track_B_vtk\n", "!mkdir data_centroid_track_B_vtk_preprocessed_data"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}