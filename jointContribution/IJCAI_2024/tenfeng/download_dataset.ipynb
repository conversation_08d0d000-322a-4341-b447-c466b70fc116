{"cells": [{"cell_type": "markdown", "metadata": {"id": "kBRw5QHhBkax"}, "source": ["# 数据集导入(预制链接)"]}, {"cell_type": "markdown", "metadata": {"id": "DUxmPjWWV1sr"}, "source": ["# 额外数据导入\n", "（此处导入权重文件和额外数据集，在此之外的导入将有被判违规的风险，这里以导入随机生成的Track C的A榜样例提交的zip为例子）"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "H8YjMlYcYmei"}, "outputs": [], "source": ["# !pip install gdown\n", "# import gdown\n", "# !wget -P /content/train_track_B_e/press/  --user-agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36\" https://drive.google.com/uc?export=download&id=1fED8VAaC8QyL2AUCCAaq39WFLh7PanNH\n", "# !wget --quiet --save-cookies cookies.txt --keep-session-cookies --no-check-certificate 'https://docs.google.com/uc?export=download&id=1fED8VAaC8QyL2AUCCAaq39WFLh7PanNH' -O- | sed -rn 's/.*confirm=([0-9A-Za-z_]+).*/\\1\\n/p'\n", "# !wget --load-cookies cookies.txt \"https://docs.google.com/uc?export=download&confirm=$(wget --quiet --save-cookies /tmp/cookies.txt --keep-session-cookies --no-check-certificate 'https://docs.google.com/uc?export=download&id=1fED8VAaC8QyL2AUCCAaq39WFLh7PanNH' -O- | sed -rn 's/.*confirm=([0-9A-Za-z_]+).*/\\1\\n/p')&id=1fED8VAaC8QyL2AUCCAaq39WFLh7PanNH\" -O press.zip && rm -rf /tmp/cookies.txt\n", "# --header=\"Host: drive.usercontent.google.com\"\n", "# --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "# --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh,zh-CN;q=0.9,en;q=0.8\" --header=\"Cookie: AEC=AVYB7coqqJpbAt5TWooVNVp_aDFiZNB57t_gUQNwIT4IG1KVhaFStFKcVRM; SID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7sEcXEqMo-cDulaOAV9QiRAACgYKATASARYSFQHGX2MicthHYExwzkuJRXpDLmC_XxoVAUF8yKpkSuSLMm7wN8kPiklDLWpD0076; __Secure-1PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7eTxKEPCbtKJP-WpB0sS7IwACgYKAQ0SARYSFQHGX2MimRYfxLjZZnsSbRUyLnXj-xoVAUF8yKpK6mH60avbvj7dWu_Wtvny0076; __Secure-3PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7uXG5Vbh6OrUmdFmX1D1k3gACgYKAWMSARYSFQHGX2MiFsS_t8MOTqmRAcbHsxqG8RoVAUF8yKp0GCuI4xnwYrlT9L1pS6Ol0076; HSID=Ak0_J4cMdWxYYLsNT; SSID=AIMT0qtQWmO9_ImAP; APISID=h9-r_ylvYL8SAAMr/A-iHpJeO9Apsuge6w; SAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-1PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-3PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; S=billing-ui-v3=bUPMkFOb_-XdG1QunQWLPvT97nV4ggJL:billing-ui-v3-efe=bUPMkFOb_-XdG1QunQWLPvT97nV4ggJL; __Secure-1PSIDTS=sidts-CjEB4E2dkTePrvGZ34Uqd-fiwJVuKQfsKn-m0FkRIk67VXXobf9pe45X49TD9mxOTck4EAA; __Secure-3PSIDTS=sidts-CjEB4E2dkTePrvGZ34Uqd-fiwJVuKQfsKn-m0FkRIk67VXXobf9pe45X49TD9mxOTck4EAA; NID=515=NM4LfLVBoYJ9i81Kc-NBiwctEbhMRSl5wmR6n307UZGSzQGEMmfjGt37rGvWmr0kpME0asDPCVyOzRzWOQAE6tAeXOUlI-t456WBIvImfRgmWWMOsHrakcWQkh6tJpukjL302wlmwg58aKKzGf0A9FLjP_8ICtkhzJyPGNP9-UrXbObciHjanJe9QQ7HUQWK6o-ykNOyEMQdYRUyxuAiUNGs-J8CYs_FMGoehXTFhvCDRwk6BJc5fAFm7UJP6rqztlNPRsJwt04Zd3Gvhd_flNhUeLCSR8ZHFzeAbO4805TE9uuSCcny3CLGv9zsEsfbDtZEAGfDY8EzT_xm7jr7jzOts146k2NvdI2RvHJww81i_fz7uI494PwzXUgAKH4yM1ja9fZjwApQVPs0y17j-DzJoToC50bYt7DR4HpQK6on2AOu4q16ve9wyP0RDDNHfP9USVLb36gOCqfNU9At6EqTpXSJntuJOdCrF0IkO324uDeByiXpRDeDWTuFOuaD48lp0Gj91REUikYIEgsSL1De2Zrml_UCEYJrYUaRYvwZBkGpBJeb9rCVp-oipGOkSZ3ATKaIMOpviqPQEefRiK5xPf91m3eFcT4u-kRUuCIYmlZaP_fXXFPh459B6eYmhRwSAPwSZe1BczwQy9fAo6R_ZxrZKR-QHQvnJ9BOsuIvLNdygZCVWAdJji1wQp4BPxyRpHI1zwblH7vT7MDk_nQ6dcFnK-JNQM-ss4v8LQ_t7zzwg-cAOkNpek9wGSaLxAx9un2nY9bJpP9xkT5FrJjQfpQ7631XXdZP9YrTfJeM30_Cwk8HjeCa0A14Y7e3sDlCQFSEWKjsgvVKtZQPVU-dT4qy2hZku2m6xn6bqDCJ4S51qavae8ciYHJ5u06-kxoH9H2sPBkGHmNxl4uqa4E4LNG2cmK0OP0hSC_lIvPL1jMlTN9NPoI17U6Pk6z4uEa-rwZGaBc3tw0xCgZ5aNw9LHniHxefjXWFyEYuOXbtItfKYwe3KlBYHyPjX1WFvOifr99tnvKwwR-LFmTJ0_J6HzsUfXCQ6hBXfr7inmrRn8BBin-4NLpRKKUJTrE615Ltf7Rt_duO7XtInAeASNce9hyN3aB2sFD5gmEiHlXAxO1oEiuYbmBCBE3p-5pqJ994EzbTJd3_QQ; SIDCC=AKEyXzWUXvaLxcEWoUr7xDsHNqI9YlS2B0CD4n3sQW20AEbXjfwUamDojGOHRk_EUx-eeHbEAw; __Secure-1PSIDCC=AKEyXzWnzBM9s4vVnoJOnyQ6Am5ICp50PfclBvvvp2ftfri0PlYsvEF8X56Y-xoGbSgl-p7lIA; __Secure-3PSIDCC=AKEyXzWnlf6Wa8I0Hck8U3WeLdJ1nbz2W9cNUupMH0BW6wH57dNls4LLysU7cghw22IvvZKdIG8\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\" --header=\"Cookie: __Secure-ENID=12.SE=Yd0Bj-CLJ14fnd4qzdJHmwUs4B5zz46UaPC1cPJigNqqFV9PtM2CYyBpSbCkOyzUwzlEdZ1nZFf-igtGi7wSdJ_gqQSfQfh84r9egqFQAy9-GKayCRbdQKdera-2mkpuIT-c64CyR9vfNojM3hxZ9Dej-dGvtxlGjal9ttEHybw; __gsas=ID=ae0421b9a34b478c:T=1710758437:RT=1710758437:S=ALNI_MZP13R9ZOHbCzC0rgHSMrGXj6GCsg; HSID=A-4I-ZudDNUIB6EKH; SSID=A7v_1v9un6xAwVNku; APISID=ctK8IbLjeuDUmgys/AFnMSLWt9KddceDI6; SAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; __Secure-1PAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; __Secure-3PAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; SID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_kzuBV1TvOhAIC8VF1e9fpgACgYKATQSARQSFQHGX2Mi8LXUwWoIwNCEPU8Sy3mXUxoVAUF8yKqGXVfjTGz9gQal7nwGr4Pl0076; __Secure-1PSID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_PDa-DzVmbdGFPyxMQpk9_QACgYKAewSARQSFQHGX2MiAeee4fn0OWglWZfAygqkyBoVAUF8yKp-Sfmtnueimxc-0QbJRF9I0076; __Secure-3PSID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_g9IrMeU98APBo9Stp6wEnAACgYKAQASARQSFQHGX2MiFWtc9ucONXnpxBzlRdudEhoVAUF8yKoeZwCpJDnjfAFjGssHSUGm0076; NID=515=GQhY9nKKFCx3qFDjE0MA4ubjWNdef6xCIY_RfWOPWKEtyfBN3nAUl8WHI2VczjNQ4rVkj1XBAY8WNWHXyqSK10CfT4FxsFlPzrHIJpeTtm1nWRNBd9AAfBKJHz4XpESszntVUTE_59RklZuKo0vk1poReVi2da1PZKC3CTKH2Ll3gB5xuB9wf4bmq8ylVUuIROPJczr0XnCuUHV3qLdBvgy9_870b6UwOq1iOlIxFQFm01EZ4pqF4q1Ub3QRSWpEMLh4LSZFpJ5O255R5OV7krmEdDvH_sHoTEPZAg2PoEpwAyGK6Xp9qcLIlldgx5-5V86N8Wtb93uTlQuA_CFXb5_2eP3bgeX8txwlJ5SrldVjg9ctzYtBU2RwJKTSvdHfIG7lpOkg6XlkvDOcJpR3DihT_OlqnPn7drCAJpvVDv29hZn5XPMXaSrNdbG64OJ9urJEw5odEwsLYkkpC1vmlUcuoo52S5f6RQu0Z8kZiV8iRW6XIqHsSmQHunVaxk6xWCStUg; __Secure-1PSIDTS=sidts-CjEB3EgAEtTS0OazynCofIH4RCBstiRP5flEcvYW3z4Fg9oGd5QOESDOZt1wO2iqUYHjEAA; __Secure-3PSIDTS=sidts-CjEB3EgAEtTS0OazynCofIH4RCBstiRP5flEcvYW3z4Fg9oGd5QOESDOZt1wO2iqUYHjEAA; SIDCC=AKEyXzVI6aMX8lSDja86Yts3FBAtBzPCzVNgaX5BCz78NWsWzlT3yFWKUV7ZE46SFzE1GiBI-cHdTw; __Secure-1PSIDCC=AKEyXzUo4NQAwqqPMxP2eye-MFEbZmBIm_sZqRU1amttg0YoQkc8ZKSNXdHl5jNCMEbhrUHhS9-K; __Secure-3PSIDCC=AKEyXzWf2lIdmDLeZKpXSi9GytVQb6XudrYiNUBA5gW952YuLh8kL6T3IbBlu8zOTfGEcdUp5O1R\" --header=\"Connection: keep-alive\" \"https://drive.usercontent.google.com/download?id=1W0jmrRX11DnRqU4QvGMlWFSlcItyeh0R&export=download&authuser=0&confirm=t&uuid=615334ea-9569-476f-8bb4-727516c76591&at=APZUnTWDW32examprS0NvVx3_v7o%3A1720881303406\"\n", "# !wget --header=\"Host: drive.usercontent.google.com\" --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" --header=\"Cookie: AEC=AVYB7coqqJpbAt5TWooVNVp_aDFiZNB57t_gUQNwIT4IG1KVhaFStFKcVRM; SID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7sEcXEqMo-cDulaOAV9QiRAACgYKATASARYSFQHGX2MicthHYExwzkuJRXpDLmC_XxoVAUF8yKpkSuSLMm7wN8kPiklDLWpD0076; __Secure-1PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7eTxKEPCbtKJP-WpB0sS7IwACgYKAQ0SARYSFQHGX2MimRYfxLjZZnsSbRUyLnXj-xoVAUF8yKpK6mH60avbvj7dWu_Wtvny0076; __Secure-3PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7uXG5Vbh6OrUmdFmX1D1k3gACgYKAWMSARYSFQHGX2MiFsS_t8MOTqmRAcbHsxqG8RoVAUF8yKp0GCuI4xnwYrlT9L1pS6Ol0076; HSID=Ak0_J4cMdWxYYLsNT; SSID=AIMT0qtQWmO9_ImAP; APISID=h9-r_ylvYL8SAAMr/A-iHpJeO9Apsuge6w; SAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-1PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-3PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; S=billing-ui-v3=bUPMkFOb_-XdG1QunQWLPvT97nV4ggJL:billing-ui-v3-efe=bUPMkFOb_-XdG1QunQWLPvT97nV4ggJL; __Secure-1PSIDTS=sidts-CjEB4E2dkTePrvGZ34Uqd-fiwJVuKQfsKn-m0FkRIk67VXXobf9pe45X49TD9mxOTck4EAA; __Secure-3PSIDTS=sidts-CjEB4E2dkTePrvGZ34Uqd-fiwJVuKQfsKn-m0FkRIk67VXXobf9pe45X49TD9mxOTck4EAA; NID=515=NM4LfLVBoYJ9i81Kc-NBiwctEbhMRSl5wmR6n307UZGSzQGEMmfjGt37rGvWmr0kpME0asDPCVyOzRzWOQAE6tAeXOUlI-t456WBIvImfRgmWWMOsHrakcWQkh6tJpukjL302wlmwg58aKKzGf0A9FLjP_8ICtkhzJyPGNP9-UrXbObciHjanJe9QQ7HUQWK6o-ykNOyEMQdYRUyxuAiUNGs-J8CYs_FMGoehXTFhvCDRwk6BJc5fAFm7UJP6rqztlNPRsJwt04Zd3Gvhd_flNhUeLCSR8ZHFzeAbO4805TE9uuSCcny3CLGv9zsEsfbDtZEAGfDY8EzT_xm7jr7jzOts146k2NvdI2RvHJww81i_fz7uI494PwzXUgAKH4yM1ja9fZjwApQVPs0y17j-DzJoToC50bYt7DR4HpQK6on2AOu4q16ve9wyP0RDDNHfP9USVLb36gOCqfNU9At6EqTpXSJntuJOdCrF0IkO324uDeByiXpRDeDWTuFOuaD48lp0Gj91REUikYIEgsSL1De2Zrml_UCEYJrYUaRYvwZBkGpBJeb9rCVp-oipGOkSZ3ATKaIMOpviqPQEefRiK5xPf91m3eFcT4u-kRUuCIYmlZaP_fXXFPh459B6eYmhRwSAPwSZe1BczwQy9fAo6R_ZxrZKR-QHQvnJ9BOsuIvLNdygZCVWAdJji1wQp4BPxyRpHI1zwblH7vT7MDk_nQ6dcFnK-JNQM-ss4v8LQ_t7zzwg-cAOkNpek9wGSaLxAx9un2nY9bJpP9xkT5FrJjQfpQ7631XXdZP9YrTfJeM30_Cwk8HjeCa0A14Y7e3sDlCQFSEWKjsgvVKtZQPVU-dT4qy2hZku2m6xn6bqDCJ4S51qavae8ciYHJ5u06-kxoH9H2sPBkGHmNxl4uqa4E4LNG2cmK0OP0hSC_lIvPL1jMlTN9NPoI17U6Pk6z4uEa-rwZGaBc3tw0xCgZ5aNw9LHniHxefjXWFyEYuOXbtItfKYwe3KlBYHyPjX1WFvOifr99tnvKwwR-LFmTJ0_J6HzsUfXCQ6hBXfr7inmrRn8BBin-4NLpRKKUJTrE615Ltf7Rt_duO7XtInAeASNce9hyN3aB2sFD5gmEiHlXAxO1oEiuYbmBCBE3p-5pqJ994EzbTJd3_QQ; SIDCC=AKEyXzWUXvaLxcEWoUr7xDsHNqI9YlS2B0CD4n3sQW20AEbXjfwUamDojGOHRk_EUx-eeHbEAw; __Secure-1PSIDCC=AKEyXzWnzBM9s4vVnoJOnyQ6Am5ICp50PfclBvvvp2ftfri0PlYsvEF8X56Y-xoGbSgl-p7lIA; __Secure-3PSIDCC=AKEyXzWnlf6Wa8I0Hck8U3WeLdJ1nbz2W9cNUupMH0BW6wH57dNls4LLysU7cghw22IvvZKdIG8\"  --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\"  --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\"  --header=\"Connection: keep-alive\" \"https://drive.usercontent.google.com/download?id=1W0jmrRX11DnRqU4QvGMlWFSlcItyeh0R&export=download&authuser=0&confirm=t&uuid=23a03d1a-abc8-42c8-b2e9-ac15544ce09b&at=APZUnTUcKjp9S2N1MeUHjjEMQv8Z%3A1720882064537\"  -c -O 'centroid_3.zip'\n", "\n", "# !wget --header=\"Host: drive.usercontent.google.com\" --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh,zh-CN;q=0.9,en;q=0.8\" --header=\"Cookie: AEC=AVYB7coqqJpbAt5TWooVNVp_aDFiZNB57t_gUQNwIT4IG1KVhaFStFKcVRM; SID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7sEcXEqMo-cDulaOAV9QiRAACgYKATASARYSFQHGX2MicthHYExwzkuJRXpDLmC_XxoVAUF8yKpkSuSLMm7wN8kPiklDLWpD0076; __Secure-1PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7eTxKEPCbtKJP-WpB0sS7IwACgYKAQ0SARYSFQHGX2MimRYfxLjZZnsSbRUyLnXj-xoVAUF8yKpK6mH60avbvj7dWu_Wtvny0076; __Secure-3PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7uXG5Vbh6OrUmdFmX1D1k3gACgYKAWMSARYSFQHGX2MiFsS_t8MOTqmRAcbHsxqG8RoVAUF8yKp0GCuI4xnwYrlT9L1pS6Ol0076; HSID=Ak0_J4cMdWxYYLsNT; SSID=AIMT0qtQWmO9_ImAP; APISID=h9-r_ylvYL8SAAMr/A-iHpJeO9Apsuge6w; SAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-1PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-3PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; S=billing-ui-v3=bUPMkFOb_-XdG1QunQWLPvT97nV4ggJL:billing-ui-v3-efe=bUPMkFOb_-XdG1QunQWLPvT97nV4ggJL; __Secure-1PSIDTS=sidts-CjEB4E2dkTePrvGZ34Uqd-fiwJVuKQfsKn-m0FkRIk67VXXobf9pe45X49TD9mxOTck4EAA; __Secure-3PSIDTS=sidts-CjEB4E2dkTePrvGZ34Uqd-fiwJVuKQfsKn-m0FkRIk67VXXobf9pe45X49TD9mxOTck4EAA; NID=515=NM4LfLVBoYJ9i81Kc-NBiwctEbhMRSl5wmR6n307UZGSzQGEMmfjGt37rGvWmr0kpME0asDPCVyOzRzWOQAE6tAeXOUlI-t456WBIvImfRgmWWMOsHrakcWQkh6tJpukjL302wlmwg58aKKzGf0A9FLjP_8ICtkhzJyPGNP9-UrXbObciHjanJe9QQ7HUQWK6o-ykNOyEMQdYRUyxuAiUNGs-J8CYs_FMGoehXTFhvCDRwk6BJc5fAFm7UJP6rqztlNPRsJwt04Zd3Gvhd_flNhUeLCSR8ZHFzeAbO4805TE9uuSCcny3CLGv9zsEsfbDtZEAGfDY8EzT_xm7jr7jzOts146k2NvdI2RvHJww81i_fz7uI494PwzXUgAKH4yM1ja9fZjwApQVPs0y17j-DzJoToC50bYt7DR4HpQK6on2AOu4q16ve9wyP0RDDNHfP9USVLb36gOCqfNU9At6EqTpXSJntuJOdCrF0IkO324uDeByiXpRDeDWTuFOuaD48lp0Gj91REUikYIEgsSL1De2Zrml_UCEYJrYUaRYvwZBkGpBJeb9rCVp-oipGOkSZ3ATKaIMOpviqPQEefRiK5xPf91m3eFcT4u-kRUuCIYmlZaP_fXXFPh459B6eYmhRwSAPwSZe1BczwQy9fAo6R_ZxrZKR-QHQvnJ9BOsuIvLNdygZCVWAdJji1wQp4BPxyRpHI1zwblH7vT7MDk_nQ6dcFnK-JNQM-ss4v8LQ_t7zzwg-cAOkNpek9wGSaLxAx9un2nY9bJpP9xkT5FrJjQfpQ7631XXdZP9YrTfJeM30_Cwk8HjeCa0A14Y7e3sDlCQFSEWKjsgvVKtZQPVU-dT4qy2hZku2m6xn6bqDCJ4S51qavae8ciYHJ5u06-kxoH9H2sPBkGHmNxl4uqa4E4LNG2cmK0OP0hSC_lIvPL1jMlTN9NPoI17U6Pk6z4uEa-rwZGaBc3tw0xCgZ5aNw9LHniHxefjXWFyEYuOXbtItfKYwe3KlBYHyPjX1WFvOifr99tnvKwwR-LFmTJ0_J6HzsUfXCQ6hBXfr7inmrRn8BBin-4NLpRKKUJTrE615Ltf7Rt_duO7XtInAeASNce9hyN3aB2sFD5gmEiHlXAxO1oEiuYbmBCBE3p-5pqJ994EzbTJd3_QQ; SIDCC=AKEyXzWUXvaLxcEWoUr7xDsHNqI9YlS2B0CD4n3sQW20AEbXjfwUamDojGOHRk_EUx-eeHbEAw; __Secure-1PSIDCC=AKEyXzWnzBM9s4vVnoJOnyQ6Am5ICp50PfclBvvvp2ftfri0PlYsvEF8X56Y-xoGbSgl-p7lIA; __Secure-3PSIDCC=AKEyXzWnlf6Wa8I0Hck8U3WeLdJ1nbz2W9cNUupMH0BW6wH57dNls4LLysU7cghw22IvvZKdIG8\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\" --header=\"Cookie: __Secure-ENID=12.SE=Yd0Bj-CLJ14fnd4qzdJHmwUs4B5zz46UaPC1cPJigNqqFV9PtM2CYyBpSbCkOyzUwzlEdZ1nZFf-igtGi7wSdJ_gqQSfQfh84r9egqFQAy9-GKayCRbdQKdera-2mkpuIT-c64CyR9vfNojM3hxZ9Dej-dGvtxlGjal9ttEHybw; __gsas=ID=ae0421b9a34b478c:T=1710758437:RT=1710758437:S=ALNI_MZP13R9ZOHbCzC0rgHSMrGXj6GCsg; HSID=A-4I-ZudDNUIB6EKH; SSID=A7v_1v9un6xAwVNku; APISID=ctK8IbLjeuDUmgys/AFnMSLWt9KddceDI6; SAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; __Secure-1PAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; __Secure-3PAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; SID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_kzuBV1TvOhAIC8VF1e9fpgACgYKATQSARQSFQHGX2Mi8LXUwWoIwNCEPU8Sy3mXUxoVAUF8yKqGXVfjTGz9gQal7nwGr4Pl0076; __Secure-1PSID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_PDa-DzVmbdGFPyxMQpk9_QACgYKAewSARQSFQHGX2MiAeee4fn0OWglWZfAygqkyBoVAUF8yKp-Sfmtnueimxc-0QbJRF9I0076; __Secure-3PSID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_g9IrMeU98APBo9Stp6wEnAACgYKAQASARQSFQHGX2MiFWtc9ucONXnpxBzlRdudEhoVAUF8yKoeZwCpJDnjfAFjGssHSUGm0076; NID=515=GQhY9nKKFCx3qFDjE0MA4ubjWNdef6xCIY_RfWOPWKEtyfBN3nAUl8WHI2VczjNQ4rVkj1XBAY8WNWHXyqSK10CfT4FxsFlPzrHIJpeTtm1nWRNBd9AAfBKJHz4XpESszntVUTE_59RklZuKo0vk1poReVi2da1PZKC3CTKH2Ll3gB5xuB9wf4bmq8ylVUuIROPJczr0XnCuUHV3qLdBvgy9_870b6UwOq1iOlIxFQFm01EZ4pqF4q1Ub3QRSWpEMLh4LSZFpJ5O255R5OV7krmEdDvH_sHoTEPZAg2PoEpwAyGK6Xp9qcLIlldgx5-5V86N8Wtb93uTlQuA_CFXb5_2eP3bgeX8txwlJ5SrldVjg9ctzYtBU2RwJKTSvdHfIG7lpOkg6XlkvDOcJpR3DihT_OlqnPn7drCAJpvVDv29hZn5XPMXaSrNdbG64OJ9urJEw5odEwsLYkkpC1vmlUcuoo52S5f6RQu0Z8kZiV8iRW6XIqHsSmQHunVaxk6xWCStUg; __Secure-1PSIDTS=sidts-CjEB3EgAEtTS0OazynCofIH4RCBstiRP5flEcvYW3z4Fg9oGd5QOESDOZt1wO2iqUYHjEAA; __Secure-3PSIDTS=sidts-CjEB3EgAEtTS0OazynCofIH4RCBstiRP5flEcvYW3z4Fg9oGd5QOESDOZt1wO2iqUYHjEAA; SIDCC=AKEyXzVI6aMX8lSDja86Yts3FBAtBzPCzVNgaX5BCz78NWsWzlT3yFWKUV7ZE46SFzE1GiBI-cHdTw; __Secure-1PSIDCC=AKEyXzUo4NQAwqqPMxP2eye-MFEbZmBIm_sZqRU1amttg0YoQkc8ZKSNXdHl5jNCMEbhrUHhS9-K; __Secure-3PSIDCC=AKEyXzWf2lIdmDLeZKpXSi9GytVQb6XudrYiNUBA5gW952YuLh8kL6T3IbBlu8zOTfGEcdUp5O1R\" --header=\"Connection: keep-alive\" \"https://drive.usercontent.google.com/download?id=1W0jmrRX11DnRqU4QvGMlWFSlcItyeh0R&export=download&authuser=0&confirm=t&uuid=615334ea-9569-476f-8bb4-727516c76591&at=APZUnTWDW32examprS0NvVx3_v7o%3A1720881303406\" -c -O 'centroid_3.zip'"]}, {"cell_type": "markdown", "metadata": {"id": "wDM42uay7X8A"}, "source": ["赛道二额外数据来源于：https://github.com/Mohamedelrefaie/DrivAerNet\n", "\n", "赛道二额外数据的论文：<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. \"Drivaernet: A parametric car dataset for data-driven aerodynamic design and graph-based drag prediction.\" arXiv preprint arXiv:2403.08055 (2024).\n", "\n", "**额外数据排除测试集：额外数据中仅使用了id>745的数据，未使用比赛测试数据，共计3711条数据。**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 159215, "status": "ok", "timestamp": 1721011960293, "user": {"displayName": "刘野", "userId": "06353679841549898578"}, "user_tz": -480}, "id": "qrLUfXxxKrTW", "outputId": "18f9e7db-1f98-4ef7-98e8-************"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", " 78 15.7G   78 12.2G    0     0  79.6M      0  0:03:22  0:02:37  0:00:45 54.1M^C\n"]}], "source": ["!curl 'https://drive.usercontent.google.com/download?id=1zEL0a_o0DBuSzRncgtxtkrFCPiegZqJd&export=download&authuser=1&confirm=t&uuid=f4cd6774-28c7-4899-beb1-e9271a130f19&at=APZUnTWt_RC0KHds95ggRJczRF9J:1721011736859' \\\n", "  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \\\n", "  -H 'accept-language: zh,zh-CN;q=0.9,en;q=0.8' \\\n", "  -H 'cookie: SEARCH_SAMESITE=CgQIzJsB; AEC=AVYB7coqqJpbAt5TWooVNVp_aDFiZNB57t_gUQNwIT4IG1KVhaFStFKcVRM; SID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7sEcXEqMo-cDulaOAV9QiRAACgYKATASARYSFQHGX2MicthHYExwzkuJRXpDLmC_XxoVAUF8yKpkSuSLMm7wN8kPiklDLWpD0076; __Secure-1PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7eTxKEPCbtKJP-WpB0sS7IwACgYKAQ0SARYSFQHGX2MimRYfxLjZZnsSbRUyLnXj-xoVAUF8yKpK6mH60avbvj7dWu_Wtvny0076; __Secure-3PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7uXG5Vbh6OrUmdFmX1D1k3gACgYKAWMSARYSFQHGX2MiFsS_t8MOTqmRAcbHsxqG8RoVAUF8yKp0GCuI4xnwYrlT9L1pS6Ol0076; HSID=Ak0_J4cMdWxYYLsNT; SSID=AIMT0qtQWmO9_ImAP; APISID=h9-r_ylvYL8SAAMr/A-iHpJeO9Apsuge6w; SAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-1PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-3PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; S=billing-ui-v3=myiOE0YlM8lN8iYqqDFVV1DzcE9ILBKL:billing-ui-v3-efe=myiOE0YlM8lN8iYqqDFVV1DzcE9ILBKL; NID=515=f7DxlXMHGv5snCa0o2Rtf5PaclKJPFPfs4bzQYWoP6-12yc61N_aaDD1dxrg_RZ9gAo4sd2DtQX3LjE_zIJmfycvHEAT8y_NDwj97mJ9RoEpHPflVyjkLedO5HixxuVPESgUY6RJSJagWgWEz4I3z_u4YHnGNh97LoYHEhIPEaWKiJPOu_LbcJH0W0JypqoUI-Wq3fnXMbkdQDYn8jYfmvwX1E64Tm4J_0TL2p9SontzJU7zHyD0AqMmsy_8bGF_tPSIzWDaoUQe_jhJBEfZTMObdKKsWH1tONlvFtvXOH9PR2K9ELhsj76I2rxRIkyj9a2J68rvfn2pWFl87DttRagRQo4OB3iBbEW5E73M3NdqHhDNy-GA9Gxee0gDTcXrESwdv_GzNC8fzH-4z5-aHCT3iAhIndphEwLJb-hXmjt4TG2yHdptS4WeE1cHT3QYuclxlMU_CF6-jK3FmwZDjuwTliqzqlOw9TdAbJuG66v_PdRLwgp3CYPAQjyMoPQRS2XXXZj-JclCNcB7cRX445gUTm9IojyQjy5GJHxxXB6jr-QNNWggOf8Ox-EtjMLKzsFqTkcJD3YQ6Bm-KPwGRIrgYi4MXAfDBDWQ4LkU1N5GcdY5dFYa7n5AaQQK98QlIfj08iluZtuntxfAHFA4RnOCPhM_ve3CYtmVa6ZeMcSOgAepO-oYLB_IgVzmWcPJIaAMBcwaeN4TSA5-zbgOjdSGb5QX9Oitq8pNFTDgXSlu_yQUQCZueGbkCuh0G1D8Tdxm1U_1x5bG44VQzp5dO1csLR9YJtShx_VDaQi2Oo1znXNzq-cRNIOIW1tnEEsaHSBjZmRNKLFK92xE93upumeeAqFg8IW3dCT42IHkBA7U8mZw5zjz35suCSX8opjaVG3h776NSj4-hq3217g1jGMPDcHkd1Ruzi-8-yfh5slwmkxyO4a-6dlU77smmhjZRhZYFVdkvzkld6D4yFr1ZwCujT3sJjS1YKwBzgGYwp_L-n5-fb8wKD5GwVrgelLMoMq4ZW_iWg8wbLXCW_CnBgsD0GDp97_y1BhjQ0ISYAefkgRudMnxPwGhLvvByzC_2TAKfOHpDscOnIQZlk9vAImBJQ_BwkjV2qvQW3xWNrDskRyQ7sp5zgZV8ZBOKUvs0RzQuk-9acLl9Q; __Secure-1PSIDTS=sidts-CjEB4E2dkSar0V5K52eCTBzJJ_9xOGD9L_GO2fYUQ-D53J-yUCJkpPUipO1K2pk8KDmYEAA; __Secure-3PSIDTS=sidts-CjEB4E2dkSar0V5K52eCTBzJJ_9xOGD9L_GO2fYUQ-D53J-yUCJkpPUipO1K2pk8KDmYEAA; SIDCC=AKEyXzVVd4bq-8eK0Ht4kPhv4m8AlVIgPDd2FxW7cenVq0oPzrD2ov4oNHOWlvosWvN-QLt4VQ; __Secure-1PSIDCC=AKEyXzXh9XzrRW3khhZ7nNG7s_-FttVPwYk5oVse3vTT2fCoSQkJgDrY7vazg1j8O1omAKPbSw; __Secure-3PSIDCC=AKEyXzVEYSAsIpUVMfnPqWxZnGDSvp-dEK5Dep35xn6oogrNNN_IskFB3yPDmaHFyfP2aMl247E' \\\n", "  -H 'priority: u=0, i' \\\n", "  -H 'sec-ch-ua: \"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"' \\\n", "  -H 'sec-ch-ua-mobile: ?0' \\\n", "  -H 'sec-ch-ua-platform: \"Windows\"' \\\n", "  -H 'sec-fetch-dest: iframe' \\\n", "  -H 'sec-fetch-mode: navigate' \\\n", "  -H 'sec-fetch-site: same-site' \\\n", "  -H 'upgrade-insecure-requests: 1' \\\n", "  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \\\n", "  -H 'x-client-data: CKy1yQEIlbbJAQiitskBCKmdygEItoHLAQiSocsBCIWgzQEIuMjNAQimis4BCOKTzgEI6JPOAQjum84BCJWdzgEIxZ3OAQiyn84BGPXJzQEY1+vNARihnc4B' -o centroid.zip\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 74051, "status": "ok", "timestamp": 1720931143410, "user": {"displayName": "刘野", "userId": "06353679841549898578"}, "user_tz": -480}, "id": "cQJI_BcgNccz", "outputId": "92c47a16-9c33-43a7-d466-03ee07949baf"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "100 5534M  100 5534M    0     0  74.6M      0  0:01:14  0:01:14 --:--:-- 83.3M\n"]}], "source": ["\n", "!curl 'https://drive.usercontent.google.com/download?id=1kplQ1qGMI8toknT74vQZGdmdWxA71SiL&export=download&authuser=1&confirm=t&uuid=5ad4d34a-c765-4ab9-bc70-0696d277d61e&at=APZUnTUmDANwNPtvdqkZOT1dXioc:1721011901290' \\\n", "  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \\\n", "  -H 'accept-language: zh,zh-CN;q=0.9,en;q=0.8' \\\n", "  -H 'cookie: SEARCH_SAMESITE=CgQIzJsB; AEC=AVYB7coqqJpbAt5TWooVNVp_aDFiZNB57t_gUQNwIT4IG1KVhaFStFKcVRM; SID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7sEcXEqMo-cDulaOAV9QiRAACgYKATASARYSFQHGX2MicthHYExwzkuJRXpDLmC_XxoVAUF8yKpkSuSLMm7wN8kPiklDLWpD0076; __Secure-1PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7eTxKEPCbtKJP-WpB0sS7IwACgYKAQ0SARYSFQHGX2MimRYfxLjZZnsSbRUyLnXj-xoVAUF8yKpK6mH60avbvj7dWu_Wtvny0076; __Secure-3PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7uXG5Vbh6OrUmdFmX1D1k3gACgYKAWMSARYSFQHGX2MiFsS_t8MOTqmRAcbHsxqG8RoVAUF8yKp0GCuI4xnwYrlT9L1pS6Ol0076; HSID=Ak0_J4cMdWxYYLsNT; SSID=AIMT0qtQWmO9_ImAP; APISID=h9-r_ylvYL8SAAMr/A-iHpJeO9Apsuge6w; SAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-1PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-3PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; S=billing-ui-v3=myiOE0YlM8lN8iYqqDFVV1DzcE9ILBKL:billing-ui-v3-efe=myiOE0YlM8lN8iYqqDFVV1DzcE9ILBKL; NID=515=f7DxlXMHGv5snCa0o2Rtf5PaclKJPFPfs4bzQYWoP6-12yc61N_aaDD1dxrg_RZ9gAo4sd2DtQX3LjE_zIJmfycvHEAT8y_NDwj97mJ9RoEpHPflVyjkLedO5HixxuVPESgUY6RJSJagWgWEz4I3z_u4YHnGNh97LoYHEhIPEaWKiJPOu_LbcJH0W0JypqoUI-Wq3fnXMbkdQDYn8jYfmvwX1E64Tm4J_0TL2p9SontzJU7zHyD0AqMmsy_8bGF_tPSIzWDaoUQe_jhJBEfZTMObdKKsWH1tONlvFtvXOH9PR2K9ELhsj76I2rxRIkyj9a2J68rvfn2pWFl87DttRagRQo4OB3iBbEW5E73M3NdqHhDNy-GA9Gxee0gDTcXrESwdv_GzNC8fzH-4z5-aHCT3iAhIndphEwLJb-hXmjt4TG2yHdptS4WeE1cHT3QYuclxlMU_CF6-jK3FmwZDjuwTliqzqlOw9TdAbJuG66v_PdRLwgp3CYPAQjyMoPQRS2XXXZj-JclCNcB7cRX445gUTm9IojyQjy5GJHxxXB6jr-QNNWggOf8Ox-EtjMLKzsFqTkcJD3YQ6Bm-KPwGRIrgYi4MXAfDBDWQ4LkU1N5GcdY5dFYa7n5AaQQK98QlIfj08iluZtuntxfAHFA4RnOCPhM_ve3CYtmVa6ZeMcSOgAepO-oYLB_IgVzmWcPJIaAMBcwaeN4TSA5-zbgOjdSGb5QX9Oitq8pNFTDgXSlu_yQUQCZueGbkCuh0G1D8Tdxm1U_1x5bG44VQzp5dO1csLR9YJtShx_VDaQi2Oo1znXNzq-cRNIOIW1tnEEsaHSBjZmRNKLFK92xE93upumeeAqFg8IW3dCT42IHkBA7U8mZw5zjz35suCSX8opjaVG3h776NSj4-hq3217g1jGMPDcHkd1Ruzi-8-yfh5slwmkxyO4a-6dlU77smmhjZRhZYFVdkvzkld6D4yFr1ZwCujT3sJjS1YKwBzgGYwp_L-n5-fb8wKD5GwVrgelLMoMq4ZW_iWg8wbLXCW_CnBgsD0GDp97_y1BhjQ0ISYAefkgRudMnxPwGhLvvByzC_2TAKfOHpDscOnIQZlk9vAImBJQ_BwkjV2qvQW3xWNrDskRyQ7sp5zgZV8ZBOKUvs0RzQuk-9acLl9Q; __Secure-1PSIDTS=sidts-CjEB4E2dkcyeG-706DIirRL4ZT6d1Ey7zOxcpjH0gIMP-LGrv_2vD6ZYpCNB-zbi6FD1EAA; __Secure-3PSIDTS=sidts-CjEB4E2dkcyeG-706DIirRL4ZT6d1Ey7zOxcpjH0gIMP-LGrv_2vD6ZYpCNB-zbi6FD1EAA; SIDCC=AKEyXzUO6ATMbyLDKf0JONH_EZ9q18dNAh4W2BnM-J4vWJXZIWn62ug9d3TdYBYHq_wZGYaQ9g; __Secure-1PSIDCC=AKEyXzVaBb1Cde3SKjz4O9dj3_TJYqgA6V0wzhjeI7tzYynz8DfGJqCyAlTI5wfk3BSO5jeg3g; __Secure-3PSIDCC=AKEyXzVhNQN1oY0uCd7QSXfCDNSJCAv920LjcKWTt-641d2O0ITi6ng5t_AaspWvj3D8kv_cG-8' \\\n", "  -H 'priority: u=0, i' \\\n", "  -H 'sec-ch-ua: \"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"' \\\n", "  -H 'sec-ch-ua-mobile: ?0' \\\n", "  -H 'sec-ch-ua-platform: \"Windows\"' \\\n", "  -H 'sec-fetch-dest: iframe' \\\n", "  -H 'sec-fetch-mode: navigate' \\\n", "  -H 'sec-fetch-site: same-site' \\\n", "  -H 'upgrade-insecure-requests: 1' \\\n", "  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \\\n", "  -H 'x-client-data: CKy1yQEIlbbJAQiitskBCKmdygEItoHLAQiSocsBCIWgzQEIuMjNAQimis4BCOKTzgEI6JPOAQjum84BCJWdzgEIxZ3OAQiyn84BGPXJzQEY1+vNARihnc4B' -o press.zip\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4302, "status": "ok", "timestamp": 1720931148267, "user": {"displayName": "刘野", "userId": "06353679841549898578"}, "user_tz": -480}, "id": "zSkqKjB4NeQi", "outputId": "56d831df-b746-42d4-ce2b-ba509c66a8d6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "100  312M  100  312M    0     0  72.0M      0  0:00:04  0:00:04 --:--:--  100M\n"]}], "source": ["\n", "!curl 'https://drive.usercontent.google.com/download?id=1Pu9XSyRj47SFrmdmxLNxZMb8f3LK6oi2&export=download&authuser=1&confirm=t&uuid=03fc13af-efab-4e23-8505-b60212c68185&at=APZUnTUBVyyVybTwVQg3uylUsD5F:1721008361811' \\\n", "  -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \\\n", "  -H 'accept-language: zh,zh-CN;q=0.9,en;q=0.8' \\\n", "  -H 'cookie: SEARCH_SAMESITE=CgQIzJsB; AEC=AVYB7coqqJpbAt5TWooVNVp_aDFiZNB57t_gUQNwIT4IG1KVhaFStFKcVRM; SID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7sEcXEqMo-cDulaOAV9QiRAACgYKATASARYSFQHGX2MicthHYExwzkuJRXpDLmC_XxoVAUF8yKpkSuSLMm7wN8kPiklDLWpD0076; __Secure-1PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7eTxKEPCbtKJP-WpB0sS7IwACgYKAQ0SARYSFQHGX2MimRYfxLjZZnsSbRUyLnXj-xoVAUF8yKpK6mH60avbvj7dWu_Wtvny0076; __Secure-3PSID=g.a000lwhzPIqBzg0W97B8nl4OMFLevLbxH1Qlfz-Q-ETz1Zt9KdI7uXG5Vbh6OrUmdFmX1D1k3gACgYKAWMSARYSFQHGX2MiFsS_t8MOTqmRAcbHsxqG8RoVAUF8yKp0GCuI4xnwYrlT9L1pS6Ol0076; HSID=Ak0_J4cMdWxYYLsNT; SSID=AIMT0qtQWmO9_ImAP; APISID=h9-r_ylvYL8SAAMr/A-iHpJeO9Apsuge6w; SAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-1PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; __Secure-3PAPISID=GiGcvVVJAk8U30rD/ARLtq0ckd5r4NZAku; S=billing-ui-v3=myiOE0YlM8lN8iYqqDFVV1DzcE9ILBKL:billing-ui-v3-efe=myiOE0YlM8lN8iYqqDFVV1DzcE9ILBKL; NID=515=f7DxlXMHGv5snCa0o2Rtf5PaclKJPFPfs4bzQYWoP6-12yc61N_aaDD1dxrg_RZ9gAo4sd2DtQX3LjE_zIJmfycvHEAT8y_NDwj97mJ9RoEpHPflVyjkLedO5HixxuVPESgUY6RJSJagWgWEz4I3z_u4YHnGNh97LoYHEhIPEaWKiJPOu_LbcJH0W0JypqoUI-Wq3fnXMbkdQDYn8jYfmvwX1E64Tm4J_0TL2p9SontzJU7zHyD0AqMmsy_8bGF_tPSIzWDaoUQe_jhJBEfZTMObdKKsWH1tONlvFtvXOH9PR2K9ELhsj76I2rxRIkyj9a2J68rvfn2pWFl87DttRagRQo4OB3iBbEW5E73M3NdqHhDNy-GA9Gxee0gDTcXrESwdv_GzNC8fzH-4z5-aHCT3iAhIndphEwLJb-hXmjt4TG2yHdptS4WeE1cHT3QYuclxlMU_CF6-jK3FmwZDjuwTliqzqlOw9TdAbJuG66v_PdRLwgp3CYPAQjyMoPQRS2XXXZj-JclCNcB7cRX445gUTm9IojyQjy5GJHxxXB6jr-QNNWggOf8Ox-EtjMLKzsFqTkcJD3YQ6Bm-KPwGRIrgYi4MXAfDBDWQ4LkU1N5GcdY5dFYa7n5AaQQK98QlIfj08iluZtuntxfAHFA4RnOCPhM_ve3CYtmVa6ZeMcSOgAepO-oYLB_IgVzmWcPJIaAMBcwaeN4TSA5-zbgOjdSGb5QX9Oitq8pNFTDgXSlu_yQUQCZueGbkCuh0G1D8Tdxm1U_1x5bG44VQzp5dO1csLR9YJtShx_VDaQi2Oo1znXNzq-cRNIOIW1tnEEsaHSBjZmRNKLFK92xE93upumeeAqFg8IW3dCT42IHkBA7U8mZw5zjz35suCSX8opjaVG3h776NSj4-hq3217g1jGMPDcHkd1Ruzi-8-yfh5slwmkxyO4a-6dlU77smmhjZRhZYFVdkvzkld6D4yFr1ZwCujT3sJjS1YKwBzgGYwp_L-n5-fb8wKD5GwVrgelLMoMq4ZW_iWg8wbLXCW_CnBgsD0GDp97_y1BhjQ0ISYAefkgRudMnxPwGhLvvByzC_2TAKfOHpDscOnIQZlk9vAImBJQ_BwkjV2qvQW3xWNrDskRyQ7sp5zgZV8ZBOKUvs0RzQuk-9acLl9Q; __Secure-1PSIDTS=sidts-CjEB4E2dkRl-e2cLhhwXDzDk-rzgd2xbWfhFsDhTMeF5dTlXdEZtgRRv9KxOg3aKbw94EAA; __Secure-3PSIDTS=sidts-CjEB4E2dkRl-e2cLhhwXDzDk-rzgd2xbWfhFsDhTMeF5dTlXdEZtgRRv9KxOg3aKbw94EAA; SIDCC=AKEyXzUQGR2IEvc_8XXe0ihqfc_i1mjVc2ver1o3_qABcV9RkiYy9oq63ESbdypx118N_ffR3w; __Secure-1PSIDCC=AKEyXzUbVzm8IfocDFc9TkyP7h23tZ-QdwdtDuRlCuwZFq8GH-fcpnzNFi2mhTZJbGbiHzw26w; __Secure-3PSIDCC=AKEyXzX2kWbD1r-wN6CXPXeroB7juxTpokNrOsycIfomuT-E3grtkX0ktBuHs8ut788dbPuNMlg' \\\n", "  -H 'priority: u=0, i' \\\n", "  -H 'sec-ch-ua: \"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"' \\\n", "  -H 'sec-ch-ua-mobile: ?0' \\\n", "  -H 'sec-ch-ua-platform: \"Windows\"' \\\n", "  -H 'sec-fetch-dest: iframe' \\\n", "  -H 'sec-fetch-mode: navigate' \\\n", "  -H 'sec-fetch-site: same-site' \\\n", "  -H 'upgrade-insecure-requests: 1' \\\n", "  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \\\n", "  -H 'x-client-data: CKy1yQEIlbbJAQiitskBCKmdygEItoHLAQiSocsBCIWgzQEIuMjNAQimis4BCOKTzgEI6JPOAQjum84BCJWdzgEIxZ3OAQiyn84BGPXJzQEY1+vNARihnc4B' -o Testset_track_B.zip\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KAa3QqklR_fu"}, "outputs": [], "source": ["\n", "import zipfile\n", "import os\n", "\n", "def list_and_unzip(zip_folder, extract_folder):\n", "    # 确保解压目录存在\n", "    if not os.path.exists(extract_folder):\n", "        os.makedirs(extract_folder)\n", "\n", "    # 列出指定目录下所有文件和文件夹\n", "    files = os.listdir(zip_folder)\n", "\n", "    # 过滤出.zip文件\n", "    zip_files = [file for file in files if file.endswith('.zip')]\n", "\n", "    # 输出.zip文件列表\n", "    print(\"Zip files found:\", zip_files)\n", "\n", "    # 逐一解压每个.zip文件\n", "    for zip_file in zip_files:\n", "        zip_path = os.path.join(zip_folder, zip_file)\n", "        with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "            zip_ref.extractall(extract_folder)\n", "            print(f\"Extracted {zip_file} to {extract_folder}\")\n", "\n", "\n", "def unzip_file(zip_path, extract_to):\n", "    \"\"\"\n", "    解压 ZIP 文件到指定目录。\n", "    Args:\n", "    zip_path (str): ZIP 文件的路径。\n", "    extract_to (str): 文件解压的目标目录。\n", "    \"\"\"\n", "    # 确保解压目标目录存在\n", "    if not os.path.exists(extract_to):\n", "        os.makedirs(extract_to)\n", "\n", "    with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "        zip_ref.extractall(extract_to)\n", "        print(f\"Files extracted to: {extract_to}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 126225, "status": "ok", "timestamp": 1720931623482, "user": {"displayName": "刘野", "userId": "06353679841549898578"}, "user_tz": -480}, "id": "YrH8j7XOSBsp", "outputId": "c355cf90-a49e-470f-ed3a-a06b4a41fb44"}, "outputs": [], "source": ["\n", "# centroid\n", "# centroid_zip_folder = '/content/train_track_B_e/'  # 替换为你的.zip文件所在目录\n", "# centroid_folder = '/content/train_track_B_e/'  # 替换为你希望解压到的目录\n", "# list_and_unzip(centroid_zip_folder, centroid_folder)\n", "\n", "unzip_file('/centroid.zip','/Dataset/train_track_B_e/')\n", "os.remove('/centroid.zip')\n", "unzip_file('/press.zip','/Dataset/train_track_B_e/')\n", "os.remove('/press.zip')\n", "unzip_file('/Testset_track_B.zip','/Dataset/')\n", "os.remove('/Testset_track_B.zip')\n", "# os.remove('/content/train_track_B_e/centroid.zip')\n", "# os.remove('/content/train_track_B_e/press.zip')"]}], "metadata": {"colab": {"collapsed_sections": ["z0Sek0wtEs5n", "kY81z-fCgPfK", "PmlOGK6yPVGu"], "gpuType": "T4", "provenance": [{"file_id": "1cqAJrxi3BXDeizZAYTIu0GJxkb-1HrNZ", "timestamp": 1721015572352}]}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}