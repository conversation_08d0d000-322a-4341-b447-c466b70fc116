# This file is generated by PaConvert ToolKit, please Don't edit it!
import paddle


def view(self, *args, **kwargs):
    if args:
        if len(args) == 1:
            if isinstance(args[0], (tuple, list)):
                return paddle.reshape(self, args[0])  # To change reshape => view
            elif isinstance(args[0], str):
                return paddle.view(self, args[0])
            else:
                return paddle.reshape(self, list(args))  # To change reshape => view
        else:
            return paddle.reshape(self, list(args))  # To change reshape => view
    elif kwargs:
        key = [k for k in kwargs.keys()]
        if "dtype" in kwargs:
            return paddle.view(self, shape_or_dtype=kwargs[key[0]])
        else:
            return paddle.reshape(
                self, shape=kwargs[key[0]]
            )  # To change reshape => view


setattr(paddle.Tensor, "view", view)


def split_tensor_func(self, split_size, dim=0):
    if isinstance(split_size, int):
        return paddle.split(self, self.shape[dim] // split_size, dim)
    else:
        return paddle.split(self, split_size, dim)


setattr(paddle.Tensor, "split", split_tensor_func)

# def uneven_chunk(self, chunks):
#     tensor = self
#     total_size = tensor.shape[0]
#     chunk_size = total_size // chunks
#     remainder = total_size % chunks
#     chunks_indices = []
#     start = 0
#     for i in range(chunks):
#         end = start + chunk_size + (1 if i < remainder else 0)
#         chunks_indices.append((start, end))
#         start = end
#     return [tensor[start:end] for start, end in chunks_indices]


def uneven_chunk(self, chunks):
    remainder = self.shape[0] % chunks
    chunks = paddle.chunk(self[:-remainder], chunks)
    chunks.append(self[-remainder:])
    return chunks


setattr(paddle.Tensor, "chunk", uneven_chunk)
