{"cells": [{"cell_type": "markdown", "metadata": {"id": "d25Ges-BIcKg"}, "source": ["# 下载比赛数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "executionInfo": {"elapsed": 40227, "status": "ok", "timestamp": 1720876228678, "user": {"displayName": "<PERSON><PERSON><PERSON> Li", "userId": "11727574012572911053"}, "user_tz": -480}, "id": "8zLTCo4-DJUW", "outputId": "83f5ac5f-74a7-4811-a72b-0976ab099a08"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2024-07-13 13:09:47--  https://drive.usercontent.google.com/download?id=1JwR0Q1ArTg6c47EF2ZuIBpQwCPgXKrO2&export=download&authuser=0&confirm=t&uuid=dc3aa13c-c3a9-458f-983a-8586798cb635&at=APZUnTX25XMxi-z-3wBcgR93IGsL%3A1719235792953\n", "Resolving drive.usercontent.google.com (drive.usercontent.google.com)... **************, 2607:f8b0:400e:c05::84\n", "Connecting to drive.usercontent.google.com (drive.usercontent.google.com)|**************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 1084182095 (1.0G) [application/octet-stream]\n", "Saving to: ‘Dataset.zip’\n", "\n", "Dataset.zip         100%[===================>]   1.01G  47.6MB/s    in 15s     \n", "\n", "2024-07-13 13:10:03 (71.2 MB/s) - ‘Dataset.zip’ saved [1084182095/1084182095]\n", "\n", "Archive:  Dataset.zip\n", "   creating: Dataset/Dataset/\n", "   creating: Dataset/Dataset/Testset_track_A/\n", "   creating: Dataset/Dataset/Testset_track_A/Inference/\n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_658.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_659.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_660.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_662.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_663.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_664.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_665.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_666.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_667.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_668.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_672.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_673.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_674.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_675.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_676.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_677.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_678.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_679.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_681.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_683.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_684.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_686.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_687.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_688.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_689.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_690.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_691.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_692.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_693.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_695.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_696.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_697.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_700.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_701.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_702.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_703.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_704.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_705.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_708.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_709.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_710.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_711.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_712.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_713.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_715.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_717.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_718.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_719.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_721.ply  \n", "  inflating: Dataset/Dataset/Testset_track_A/Inference/mesh_722.ply  \n", "   creating: Dataset/Dataset/Testset_track_B/\n", "   creating: Dataset/Dataset/Testset_track_B/Auxiliary/\n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_1.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_10.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_11.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_12.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_13.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_14.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_15.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_16.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_17.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_18.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_19.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_2.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_20.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_21.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_22.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_23.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_24.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_25.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_26.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_27.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_28.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_29.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_3.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_30.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_31.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_32.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_33.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_34.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_35.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_36.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_37.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_38.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_39.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_4.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_40.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_41.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_42.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_43.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_44.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_45.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_46.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_47.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_48.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_49.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_5.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_50.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_6.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_7.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_8.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_9.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/area_bounds.txt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/global_bounds.txt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_1.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_10.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_11.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_12.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_13.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_14.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_15.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_16.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_17.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_18.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_19.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_2.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_20.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_21.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_22.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_23.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_24.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_25.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_26.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_27.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_28.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_29.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_3.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_30.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_31.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_32.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_33.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_34.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_35.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_36.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_37.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_38.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_39.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_4.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_40.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_41.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_42.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_43.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_44.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_45.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_46.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_47.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_48.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_49.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_5.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_50.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_6.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_7.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_8.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_9.pt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/info_bounds.txt  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_1.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_10.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_11.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_12.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_13.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_14.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_15.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_16.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_17.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_18.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_19.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_2.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_20.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_21.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_22.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_23.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_24.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_25.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_26.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_27.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_28.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_29.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_3.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_30.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_31.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_32.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_33.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_34.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_35.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_36.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_37.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_38.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_39.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_4.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_40.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_41.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_42.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_43.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_44.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_45.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_46.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_47.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_48.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_49.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_5.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_50.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_6.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_7.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_8.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/normal_9.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Auxiliary/train_pressure_mean_std.txt  \n", "  inflating: Dataset/Dataset/Testset_track_B/IJCAI_data_doc_v1.pdf  \n", "   creating: Dataset/Dataset/Testset_track_B/Inference/\n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_1.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_10.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_11.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_12.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_13.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_14.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_15.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_16.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_17.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_18.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_19.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_2.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_20.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_21.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_22.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_23.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_24.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_25.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_26.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_27.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_28.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_29.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_3.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_30.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_31.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_32.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_33.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_34.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_35.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_36.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_37.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_38.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_39.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_4.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_40.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_41.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_42.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_43.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_44.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_45.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_46.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_47.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_48.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_49.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_5.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_50.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_6.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_7.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_8.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/centroid_9.npy  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_1.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_10.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_11.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_12.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_13.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_14.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_15.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_16.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_17.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_18.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_19.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_2.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_20.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_21.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_22.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_23.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_24.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_25.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_26.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_27.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_28.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_29.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_3.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_30.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_31.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_32.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_33.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_34.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_35.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_36.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_37.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_38.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_39.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_4.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_40.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_41.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_42.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_43.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_44.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_45.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_46.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_47.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_48.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_49.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_5.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_50.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_6.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_7.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_8.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/Inference/mesh_9.ply  \n", "  inflating: Dataset/Dataset/Testset_track_B/track_B_data_dict.xlsx  \n", "   creating: Dataset/Dataset/Training_data/\n", "   creating: Dataset/Dataset/Training_data/Feature/\n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_001.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_002.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_004.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_005.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_006.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_007.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_008.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_010.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_012.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_013.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_017.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_018.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_021.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_022.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_023.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_025.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_026.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_027.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_028.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_029.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_030.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_031.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_032.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_034.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_035.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_039.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_040.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_043.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_044.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_045.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_046.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_047.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_048.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_049.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_050.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_051.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_052.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_054.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_055.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_056.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_058.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_059.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_060.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_061.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_062.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_063.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_064.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_065.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_067.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_069.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_070.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_071.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_072.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_073.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_074.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_075.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_076.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_077.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_078.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_079.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_080.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_081.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_083.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_084.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_085.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_086.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_087.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_088.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_090.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_091.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_092.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_094.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_095.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_096.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_097.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_100.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_101.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_102.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_105.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_106.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_107.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_109.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_110.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_111.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_112.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_113.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_114.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_115.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_116.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_117.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_118.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_119.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_120.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_121.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_123.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_124.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_125.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_126.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_127.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_128.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_129.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_130.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_131.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_133.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_134.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_136.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_137.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_138.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_139.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_140.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_141.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_142.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_143.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_144.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_145.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_146.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_147.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_148.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_149.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_150.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_151.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_152.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_153.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_155.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_156.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_157.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_158.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_159.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_160.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_161.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_162.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_163.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_165.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_166.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_170.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_172.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_173.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_175.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_176.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_177.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_178.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_179.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_180.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_181.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_182.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_183.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_184.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_186.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_190.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_191.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_192.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_193.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_195.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_196.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_198.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_199.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_200.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_201.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_202.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_203.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_205.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_207.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_210.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_211.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_212.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_213.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_214.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_215.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_217.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_219.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_220.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_221.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_222.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_223.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_224.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_225.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_227.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_228.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_229.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_230.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_231.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_232.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_233.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_234.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_235.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_236.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_237.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_241.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_243.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_244.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_245.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_246.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_247.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_248.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_249.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_251.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_252.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_253.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_255.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_257.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_258.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_259.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_260.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_261.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_262.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_263.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_264.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_266.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_267.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_268.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_269.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_271.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_272.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_273.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_274.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_275.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_276.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_277.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_278.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_279.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_280.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_281.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_282.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_283.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_285.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_286.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_289.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_290.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_291.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_292.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_293.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_294.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_295.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_296.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_297.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_298.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_299.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_300.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_301.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_302.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_304.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_305.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_306.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_308.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_309.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_310.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_311.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_312.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_313.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_314.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_315.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_319.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_320.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_321.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_322.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_323.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_324.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_325.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_327.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_328.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_329.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_331.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_332.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_333.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_334.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_335.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_337.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_338.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_339.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_340.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_341.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_344.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_345.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_347.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_348.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_349.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_350.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_352.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_353.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_354.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_355.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_356.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_357.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_358.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_360.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_362.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_364.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_365.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_366.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_367.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_369.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_371.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_372.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_373.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_374.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_375.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_376.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_378.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_379.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_380.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_381.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_384.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_385.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_389.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_392.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_393.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_397.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_398.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_399.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_401.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_402.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_403.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_404.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_405.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_407.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_408.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_410.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_412.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_413.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_414.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_415.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_417.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_418.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_419.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_420.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_422.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_424.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_425.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_427.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_430.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_431.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_433.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_435.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_436.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_437.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_439.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_440.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_443.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_444.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_446.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_447.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_448.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_449.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_450.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_451.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_452.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_453.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_454.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_455.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_456.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_457.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_459.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_460.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_462.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_463.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_464.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_465.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_466.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_467.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_468.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_469.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_470.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_472.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_473.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_474.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_475.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_476.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_478.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_479.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_480.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_482.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_483.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_486.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_487.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_488.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_490.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_493.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_494.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_495.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_496.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_497.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_498.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_499.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_501.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_502.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_503.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_504.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_505.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_507.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_508.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_509.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_511.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_512.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_513.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_514.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_515.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_516.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_518.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_519.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_521.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_522.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_523.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_524.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_525.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_527.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_529.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_530.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_532.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_533.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_536.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_538.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_539.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_540.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_542.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_543.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_545.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_547.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_548.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_549.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_550.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_551.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_552.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_553.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_554.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_555.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_560.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_561.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_562.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_564.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_565.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_566.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_567.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_568.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_569.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_572.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_573.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_574.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_576.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_577.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_579.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_581.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_582.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_583.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_584.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_587.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_588.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_589.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_591.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_593.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_594.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_595.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_596.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_597.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_598.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_600.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_602.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_604.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_608.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_610.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_611.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_612.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_613.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_615.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_616.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_617.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_618.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_620.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_621.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_622.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_623.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_625.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_626.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_627.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_628.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_629.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_630.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_631.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_632.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_633.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_634.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_635.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_636.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_638.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_639.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_640.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_641.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_642.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_643.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_644.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_645.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_646.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_647.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_648.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_649.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_651.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_652.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_654.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_655.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_656.ply  \n", "  inflating: Dataset/Dataset/Training_data/Feature/mesh_657.ply  \n", "   creating: Dataset/Dataset/Training_data/Label/\n", "  inflating: Dataset/Dataset/Training_data/Label/press_001.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_002.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_004.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_005.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_006.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_007.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_008.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_010.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_012.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_013.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_017.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_018.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_021.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_022.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_023.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_025.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_026.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_027.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_028.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_029.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_030.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_031.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_032.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_034.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_035.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_039.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_040.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_043.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_044.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_045.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_046.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_047.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_048.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_049.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_050.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_051.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_052.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_054.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_055.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_056.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_058.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_059.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_060.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_061.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_062.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_063.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_064.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_065.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_067.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_069.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_070.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_071.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_072.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_073.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_074.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_075.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_076.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_077.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_078.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_079.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_080.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_081.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_083.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_084.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_085.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_086.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_087.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_088.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_090.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_091.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_092.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_094.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_095.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_096.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_097.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_100.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_101.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_102.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_105.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_106.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_107.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_109.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_110.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_111.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_112.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_113.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_114.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_115.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_116.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_117.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_118.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_119.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_120.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_121.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_123.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_124.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_125.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_126.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_127.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_128.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_129.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_130.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_131.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_133.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_134.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_136.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_137.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_138.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_139.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_140.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_141.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_142.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_143.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_144.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_145.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_146.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_147.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_148.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_149.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_150.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_151.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_152.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_153.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_155.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_156.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_157.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_158.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_159.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_160.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_161.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_162.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_163.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_165.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_166.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_170.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_172.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_173.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_175.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_176.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_177.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_178.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_179.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_180.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_181.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_182.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_183.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_184.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_186.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_190.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_191.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_192.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_193.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_195.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_196.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_198.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_199.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_200.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_201.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_202.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_203.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_205.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_207.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_210.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_211.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_212.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_213.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_214.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_215.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_217.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_219.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_220.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_221.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_222.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_223.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_224.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_225.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_227.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_228.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_229.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_230.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_231.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_232.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_233.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_234.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_235.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_236.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_237.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_241.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_243.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_244.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_245.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_246.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_247.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_248.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_249.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_251.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_252.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_253.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_255.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_257.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_258.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_259.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_260.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_261.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_262.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_263.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_264.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_266.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_267.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_268.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_269.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_271.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_272.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_273.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_274.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_275.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_276.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_277.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_278.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_279.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_280.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_281.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_282.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_283.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_285.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_286.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_289.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_290.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_291.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_292.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_293.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_294.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_295.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_296.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_297.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_298.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_299.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_300.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_301.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_302.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_304.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_305.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_306.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_308.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_309.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_310.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_311.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_312.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_313.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_314.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_315.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_319.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_320.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_321.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_322.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_323.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_324.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_325.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_327.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_328.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_329.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_331.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_332.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_333.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_334.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_335.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_337.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_338.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_339.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_340.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_341.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_344.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_345.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_347.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_348.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_349.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_350.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_352.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_353.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_354.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_355.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_356.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_357.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_358.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_360.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_362.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_364.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_365.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_366.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_367.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_369.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_371.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_372.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_373.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_374.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_375.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_376.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_378.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_379.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_380.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_381.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_384.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_385.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_389.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_392.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_393.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_397.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_398.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_399.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_401.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_402.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_403.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_404.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_405.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_407.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_408.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_410.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_412.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_413.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_414.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_415.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_417.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_418.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_419.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_420.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_422.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_424.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_425.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_427.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_430.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_431.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_433.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_435.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_436.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_437.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_439.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_440.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_443.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_444.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_446.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_447.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_448.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_449.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_450.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_451.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_452.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_453.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_454.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_455.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_456.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_457.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_459.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_460.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_462.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_463.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_464.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_465.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_466.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_467.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_468.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_469.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_470.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_472.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_473.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_474.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_475.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_476.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_478.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_479.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_480.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_482.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_483.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_486.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_487.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_488.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_490.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_493.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_494.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_495.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_496.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_497.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_498.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_499.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_501.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_502.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_503.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_504.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_505.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_507.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_508.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_509.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_511.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_512.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_513.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_514.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_515.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_516.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_518.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_519.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_521.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_522.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_523.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_524.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_525.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_527.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_529.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_530.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_532.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_533.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_536.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_538.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_539.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_540.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_542.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_543.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_545.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_547.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_548.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_549.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_550.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_551.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_552.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_553.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_554.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_555.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_560.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_561.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_562.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_564.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_565.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_566.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_567.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_568.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_569.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_572.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_573.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_574.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_576.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_577.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_579.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_581.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_582.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_583.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_584.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_587.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_588.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_589.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_591.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_593.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_594.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_595.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_596.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_597.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_598.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_600.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_602.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_604.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_608.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_610.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_611.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_612.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_613.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_615.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_616.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_617.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_618.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_620.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_621.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_622.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_623.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_625.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_626.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_627.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_628.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_629.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_630.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_631.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_632.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_633.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_634.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_635.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_636.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_638.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_639.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_640.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_641.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_642.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_643.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_644.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_645.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_646.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_647.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_648.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_649.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_651.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_652.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_654.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_655.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_656.npy  \n", "  inflating: Dataset/Dataset/Training_data/Label/press_657.npy  \n", "  inflating: Dataset/Dataset/Training_data/train_pressure_min_std.txt  \n", "  inflating: Dataset/Dataset/Training_data/watertight_global_bounds.txt  \n", "  inflating: Dataset/Dataset/Training_data/watertight_meshes.txt  \n"]}], "source": ["!wget --header=\"Host: drive.usercontent.google.com\" --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\" --header=\"Cookie: __Secure-ENID=12.SE=Yd0Bj-CLJ14fnd4qzdJHmwUs4B5zz46UaPC1cPJigNqqFV9PtM2CYyBpSbCkOyzUwzlEdZ1nZFf-igtGi7wSdJ_gqQSfQfh84r9egqFQAy9-GKayCRbdQKdera-2mkpuIT-c64CyR9vfNojM3hxZ9Dej-dGvtxlGjal9ttEHybw; __gsas=ID=ae0421b9a34b478c:T=1710758437:RT=1710758437:S=ALNI_MZP13R9ZOHbCzC0rgHSMrGXj6GCsg; HSID=A-4I-ZudDNUIB6EKH; SSID=A7v_1v9un6xAwVNku; APISID=ctK8IbLjeuDUmgys/AFnMSLWt9KddceDI6; SAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; __Secure-1PAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; __Secure-3PAPISID=J7GhTwED67EBqJJT/A9nwK7mr0ijGPw08r; SID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_kzuBV1TvOhAIC8VF1e9fpgACgYKATQSARQSFQHGX2Mi8LXUwWoIwNCEPU8Sy3mXUxoVAUF8yKqGXVfjTGz9gQal7nwGr4Pl0076; __Secure-1PSID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_PDa-DzVmbdGFPyxMQpk9_QACgYKAewSARQSFQHGX2MiAeee4fn0OWglWZfAygqkyBoVAUF8yKp-Sfmtnueimxc-0QbJRF9I0076; __Secure-3PSID=g.a000kgiBabgKCiCYKve9zfoWVgz9eu8sBA6N4XDPPpP5pcW16_C_g9IrMeU98APBo9Stp6wEnAACgYKAQASARQSFQHGX2MiFWtc9ucONXnpxBzlRdudEhoVAUF8yKoeZwCpJDnjfAFjGssHSUGm0076; NID=515=GQhY9nKKFCx3qFDjE0MA4ubjWNdef6xCIY_RfWOPWKEtyfBN3nAUl8WHI2VczjNQ4rVkj1XBAY8WNWHXyqSK10CfT4FxsFlPzrHIJpeTtm1nWRNBd9AAfBKJHz4XpESszntVUTE_59RklZuKo0vk1poReVi2da1PZKC3CTKH2Ll3gB5xuB9wf4bmq8ylVUuIROPJczr0XnCuUHV3qLdBvgy9_870b6UwOq1iOlIxFQFm01EZ4pqF4q1Ub3QRSWpEMLh4LSZFpJ5O255R5OV7krmEdDvH_sHoTEPZAg2PoEpwAyGK6Xp9qcLIlldgx5-5V86N8Wtb93uTlQuA_CFXb5_2eP3bgeX8txwlJ5SrldVjg9ctzYtBU2RwJKTSvdHfIG7lpOkg6XlkvDOcJpR3DihT_OlqnPn7drCAJpvVDv29hZn5XPMXaSrNdbG64OJ9urJEw5odEwsLYkkpC1vmlUcuoo52S5f6RQu0Z8kZiV8iRW6XIqHsSmQHunVaxk6xWCStUg; __Secure-1PSIDTS=sidts-CjEB3EgAEtTS0OazynCofIH4RCBstiRP5flEcvYW3z4Fg9oGd5QOESDOZt1wO2iqUYHjEAA; __Secure-3PSIDTS=sidts-CjEB3EgAEtTS0OazynCofIH4RCBstiRP5flEcvYW3z4Fg9oGd5QOESDOZt1wO2iqUYHjEAA; SIDCC=AKEyXzVI6aMX8lSDja86Yts3FBAtBzPCzVNgaX5BCz78NWsWzlT3yFWKUV7ZE46SFzE1GiBI-cHdTw; __Secure-1PSIDCC=AKEyXzUo4NQAwqqPMxP2eye-MFEbZmBIm_sZqRU1amttg0YoQkc8ZKSNXdHl5jNCMEbhrUHhS9-K; __Secure-3PSIDCC=AKEyXzWf2lIdmDLeZKpXSi9GytVQb6XudrYiNUBA5gW952YuLh8kL6T3IbBlu8zOTfGEcdUp5O1R\" --header=\"Connection: keep-alive\" \"https://drive.usercontent.google.com/download?id=1JwR0Q1ArTg6c47EF2ZuIBpQwCPgXKrO2&export=download&authuser=0&confirm=t&uuid=dc3aa13c-c3a9-458f-983a-8586798cb635&at=APZUnTX25XMxi-z-3wBcgR93IGsL%3A1719235792953\" -c -O 'Dataset.zip'\n", "!mkdir -p Dataset\n", "!unzip -o Dataset.zip -d Dataset/"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "executionInfo": {"elapsed": 344790, "status": "ok", "timestamp": 1720876573464, "user": {"displayName": "<PERSON><PERSON><PERSON> Li", "userId": "11727574012572911053"}, "user_tz": -480}, "id": "VFzvkceAIWV2", "outputId": "3036bbd2-9752-4891-bbb7-1ff4bb30445e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2024-07-13 13:10:27--  https://ai-studio-online.bj.bcebos.com/v1/38e9adf0fce84527aad3558cc3e82d0e9a251aac4c934297afae9b74d9b3d1e9?responseContentDisposition=attachment%3B%20filename%3Dtrain_track_B.zip&authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2024-06-04T03%3A21%3A02Z%2F-1%2F%2Facd359add161bace603a52c7a268467406cb3c1889a7114bbb687de8002b55f6\n", "Resolving ai-studio-online.bj.bcebos.com (ai-studio-online.bj.bcebos.com)... **************, 2409:8c04:1001:1203:0:ff:b0bb:4f27\n", "Connecting to ai-studio-online.bj.bcebos.com (ai-studio-online.bj.bcebos.com)|**************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 4740031429 (4.4G) [application/octet-stream]\n", "Saving to: ‘train_track_B.zip’\n", "\n", "train_track_B.zip   100%[===================>]   4.41G  21.8MB/s    in 3m 48s  \n", "\n", "2024-07-13 13:14:16 (19.8 MB/s) - ‘train_track_B.zip’ saved [4740031429/4740031429]\n", "\n", "Archive:  train_track_B.zip\n", "  inflating: Dataset/train_track_B/area_0002.npy  \n", "  inflating: Dataset/train_track_B/area_0003.npy  \n", "  inflating: Dataset/train_track_B/area_0004.npy  \n", "  inflating: Dataset/train_track_B/area_0005.npy  \n", "  inflating: Dataset/train_track_B/area_0006.npy  \n", "  inflating: Dataset/train_track_B/area_0011.npy  \n", "  inflating: Dataset/train_track_B/area_0012.npy  \n", "  inflating: Dataset/train_track_B/area_0013.npy  \n", "  inflating: Dataset/train_track_B/area_0015.npy  \n", "  inflating: Dataset/train_track_B/area_0017.npy  \n", "  inflating: Dataset/train_track_B/area_0018.npy  \n", "  inflating: Dataset/train_track_B/area_0020.npy  \n", "  inflating: Dataset/train_track_B/area_0021.npy  \n", "  inflating: Dataset/train_track_B/area_0022.npy  \n", "  inflating: Dataset/train_track_B/area_0023.npy  \n", "  inflating: Dataset/train_track_B/area_0024.npy  \n", "  inflating: Dataset/train_track_B/area_0026.npy  \n", "  inflating: Dataset/train_track_B/area_0029.npy  \n", "  inflating: Dataset/train_track_B/area_0030.npy  \n", "  inflating: Dataset/train_track_B/area_0036.npy  \n", "  inflating: Dataset/train_track_B/area_0037.npy  \n", "  inflating: Dataset/train_track_B/area_0038.npy  \n", "  inflating: Dataset/train_track_B/area_0039.npy  \n", "  inflating: Dataset/train_track_B/area_0040.npy  \n", "  inflating: Dataset/train_track_B/area_0041.npy  \n", "  inflating: Dataset/train_track_B/area_0042.npy  \n", "  inflating: Dataset/train_track_B/area_0043.npy  \n", "  inflating: Dataset/train_track_B/area_0044.npy  \n", "  inflating: Dataset/train_track_B/area_0048.npy  \n", "  inflating: Dataset/train_track_B/area_0049.npy  \n", "  inflating: Dataset/train_track_B/area_0051.npy  \n", "  inflating: Dataset/train_track_B/area_0052.npy  \n", "  inflating: Dataset/train_track_B/area_0055.npy  \n", "  inflating: Dataset/train_track_B/area_0056.npy  \n", "  inflating: Dataset/train_track_B/area_0057.npy  \n", "  inflating: Dataset/train_track_B/area_0059.npy  \n", "  inflating: Dataset/train_track_B/area_0062.npy  \n", "  inflating: Dataset/train_track_B/area_0064.npy  \n", "  inflating: Dataset/train_track_B/area_0066.npy  \n", "  inflating: Dataset/train_track_B/area_0067.npy  \n", "  inflating: Dataset/train_track_B/area_0068.npy  \n", "  inflating: Dataset/train_track_B/area_0071.npy  \n", "  inflating: Dataset/train_track_B/area_0074.npy  \n", "  inflating: Dataset/train_track_B/area_0075.npy  \n", "  inflating: Dataset/train_track_B/area_0077.npy  \n", "  inflating: Dataset/train_track_B/area_0078.npy  \n", "  inflating: Dataset/train_track_B/area_0080.npy  \n", "  inflating: Dataset/train_track_B/area_0081.npy  \n", "  inflating: Dataset/train_track_B/area_0082.npy  \n", "  inflating: Dataset/train_track_B/area_0084.npy  \n", "  inflating: Dataset/train_track_B/area_0085.npy  \n", "  inflating: Dataset/train_track_B/area_0086.npy  \n", "  inflating: Dataset/train_track_B/area_0087.npy  \n", "  inflating: Dataset/train_track_B/area_0088.npy  \n", "  inflating: Dataset/train_track_B/area_0089.npy  \n", "  inflating: Dataset/train_track_B/area_0090.npy  \n", "  inflating: Dataset/train_track_B/area_0092.npy  \n", "  inflating: Dataset/train_track_B/area_0093.npy  \n", "  inflating: Dataset/train_track_B/area_0094.npy  \n", "  inflating: Dataset/train_track_B/area_0095.npy  \n", "  inflating: Dataset/train_track_B/area_0097.npy  \n", "  inflating: Dataset/train_track_B/area_0098.npy  \n", "  inflating: Dataset/train_track_B/area_0100.npy  \n", "  inflating: Dataset/train_track_B/area_0101.npy  \n", "  inflating: Dataset/train_track_B/area_0102.npy  \n", "  inflating: Dataset/train_track_B/area_0103.npy  \n", "  inflating: Dataset/train_track_B/area_0104.npy  \n", "  inflating: Dataset/train_track_B/area_0106.npy  \n", "  inflating: Dataset/train_track_B/area_0107.npy  \n", "  inflating: Dataset/train_track_B/area_0108.npy  \n", "  inflating: Dataset/train_track_B/area_0109.npy  \n", "  inflating: Dataset/train_track_B/area_0110.npy  \n", "  inflating: Dataset/train_track_B/area_0113.npy  \n", "  inflating: Dataset/train_track_B/area_0114.npy  \n", "  inflating: Dataset/train_track_B/area_0115.npy  \n", "  inflating: Dataset/train_track_B/area_0116.npy  \n", "  inflating: Dataset/train_track_B/area_0117.npy  \n", "  inflating: Dataset/train_track_B/area_0118.npy  \n", "  inflating: Dataset/train_track_B/area_0119.npy  \n", "  inflating: Dataset/train_track_B/area_0120.npy  \n", "  inflating: Dataset/train_track_B/area_0121.npy  \n", "  inflating: Dataset/train_track_B/area_0122.npy  \n", "  inflating: Dataset/train_track_B/area_0124.npy  \n", "  inflating: Dataset/train_track_B/area_0125.npy  \n", "  inflating: Dataset/train_track_B/area_0126.npy  \n", "  inflating: Dataset/train_track_B/area_0128.npy  \n", "  inflating: Dataset/train_track_B/area_0129.npy  \n", "  inflating: Dataset/train_track_B/area_0130.npy  \n", "  inflating: Dataset/train_track_B/area_0131.npy  \n", "  inflating: Dataset/train_track_B/area_0132.npy  \n", "  inflating: Dataset/train_track_B/area_0133.npy  \n", "  inflating: Dataset/train_track_B/area_0134.npy  \n", "  inflating: Dataset/train_track_B/area_0135.npy  \n", "  inflating: Dataset/train_track_B/area_0136.npy  \n", "  inflating: Dataset/train_track_B/area_0138.npy  \n", "  inflating: Dataset/train_track_B/area_0139.npy  \n", "  inflating: Dataset/train_track_B/area_0140.npy  \n", "  inflating: Dataset/train_track_B/area_0141.npy  \n", "  inflating: Dataset/train_track_B/area_0143.npy  \n", "  inflating: Dataset/train_track_B/area_0145.npy  \n", "  inflating: Dataset/train_track_B/area_0146.npy  \n", "  inflating: Dataset/train_track_B/area_0148.npy  \n", "  inflating: Dataset/train_track_B/area_0149.npy  \n", "  inflating: Dataset/train_track_B/area_0150.npy  \n", "  inflating: Dataset/train_track_B/area_0151.npy  \n", "  inflating: Dataset/train_track_B/area_0153.npy  \n", "  inflating: Dataset/train_track_B/area_0154.npy  \n", "  inflating: Dataset/train_track_B/area_0156.npy  \n", "  inflating: Dataset/train_track_B/area_0157.npy  \n", "  inflating: Dataset/train_track_B/area_0158.npy  \n", "  inflating: Dataset/train_track_B/area_0161.npy  \n", "  inflating: Dataset/train_track_B/area_0162.npy  \n", "  inflating: Dataset/train_track_B/area_0163.npy  \n", "  inflating: Dataset/train_track_B/area_0164.npy  \n", "  inflating: Dataset/train_track_B/area_0166.npy  \n", "  inflating: Dataset/train_track_B/area_0167.npy  \n", "  inflating: Dataset/train_track_B/area_0168.npy  \n", "  inflating: Dataset/train_track_B/area_0170.npy  \n", "  inflating: Dataset/train_track_B/area_0171.npy  \n", "  inflating: Dataset/train_track_B/area_0172.npy  \n", "  inflating: Dataset/train_track_B/area_0174.npy  \n", "  inflating: Dataset/train_track_B/area_0175.npy  \n", "  inflating: Dataset/train_track_B/area_0183.npy  \n", "  inflating: Dataset/train_track_B/area_0184.npy  \n", "  inflating: Dataset/train_track_B/area_0185.npy  \n", "  inflating: Dataset/train_track_B/area_0189.npy  \n", "  inflating: Dataset/train_track_B/area_0190.npy  \n", "  inflating: Dataset/train_track_B/area_0193.npy  \n", "  inflating: Dataset/train_track_B/area_0194.npy  \n", "  inflating: Dataset/train_track_B/area_0195.npy  \n", "  inflating: Dataset/train_track_B/area_0197.npy  \n", "  inflating: Dataset/train_track_B/area_0201.npy  \n", "  inflating: Dataset/train_track_B/area_0203.npy  \n", "  inflating: Dataset/train_track_B/area_0204.npy  \n", "  inflating: Dataset/train_track_B/area_0205.npy  \n", "  inflating: Dataset/train_track_B/area_0206.npy  \n", "  inflating: Dataset/train_track_B/area_0208.npy  \n", "  inflating: Dataset/train_track_B/area_0210.npy  \n", "  inflating: Dataset/train_track_B/area_0211.npy  \n", "  inflating: Dataset/train_track_B/area_0216.npy  \n", "  inflating: Dataset/train_track_B/area_0217.npy  \n", "  inflating: Dataset/train_track_B/area_0219.npy  \n", "  inflating: Dataset/train_track_B/area_0220.npy  \n", "  inflating: Dataset/train_track_B/area_0227.npy  \n", "  inflating: Dataset/train_track_B/area_0228.npy  \n", "  inflating: Dataset/train_track_B/area_0229.npy  \n", "  inflating: Dataset/train_track_B/area_0232.npy  \n", "  inflating: Dataset/train_track_B/area_0234.npy  \n", "  inflating: Dataset/train_track_B/area_0235.npy  \n", "  inflating: Dataset/train_track_B/area_0236.npy  \n", "  inflating: Dataset/train_track_B/area_0238.npy  \n", "  inflating: Dataset/train_track_B/area_0239.npy  \n", "  inflating: Dataset/train_track_B/area_0240.npy  \n", "  inflating: Dataset/train_track_B/area_0241.npy  \n", "  inflating: Dataset/train_track_B/area_0245.npy  \n", "  inflating: Dataset/train_track_B/area_0246.npy  \n", "  inflating: Dataset/train_track_B/area_0247.npy  \n", "  inflating: Dataset/train_track_B/area_0248.npy  \n", "  inflating: Dataset/train_track_B/area_0249.npy  \n", "  inflating: Dataset/train_track_B/area_0252.npy  \n", "  inflating: Dataset/train_track_B/area_0253.npy  \n", "  inflating: Dataset/train_track_B/area_0254.npy  \n", "  inflating: Dataset/train_track_B/area_0256.npy  \n", "  inflating: Dataset/train_track_B/area_0257.npy  \n", "  inflating: Dataset/train_track_B/area_0259.npy  \n", "  inflating: Dataset/train_track_B/area_0264.npy  \n", "  inflating: Dataset/train_track_B/area_0265.npy  \n", "  inflating: Dataset/train_track_B/area_0266.npy  \n", "  inflating: Dataset/train_track_B/area_0268.npy  \n", "  inflating: Dataset/train_track_B/area_0269.npy  \n", "  inflating: Dataset/train_track_B/area_0271.npy  \n", "  inflating: Dataset/train_track_B/area_0272.npy  \n", "  inflating: Dataset/train_track_B/area_0273.npy  \n", "  inflating: Dataset/train_track_B/area_0275.npy  \n", "  inflating: Dataset/train_track_B/area_0276.npy  \n", "  inflating: Dataset/train_track_B/area_0277.npy  \n", "  inflating: Dataset/train_track_B/area_0279.npy  \n", "  inflating: Dataset/train_track_B/area_0280.npy  \n", "  inflating: Dataset/train_track_B/area_0281.npy  \n", "  inflating: Dataset/train_track_B/area_0284.npy  \n", "  inflating: Dataset/train_track_B/area_0285.npy  \n", "  inflating: Dataset/train_track_B/area_0286.npy  \n", "  inflating: Dataset/train_track_B/area_0288.npy  \n", "  inflating: Dataset/train_track_B/area_0289.npy  \n", "  inflating: Dataset/train_track_B/area_0290.npy  \n", "  inflating: Dataset/train_track_B/area_0291.npy  \n", "  inflating: Dataset/train_track_B/area_0294.npy  \n", "  inflating: Dataset/train_track_B/area_0296.npy  \n", "  inflating: Dataset/train_track_B/area_0297.npy  \n", "  inflating: Dataset/train_track_B/area_0298.npy  \n", "  inflating: Dataset/train_track_B/area_0301.npy  \n", "  inflating: Dataset/train_track_B/area_0304.npy  \n", "  inflating: Dataset/train_track_B/area_0305.npy  \n", "  inflating: Dataset/train_track_B/area_0306.npy  \n", "  inflating: Dataset/train_track_B/area_0307.npy  \n", "  inflating: Dataset/train_track_B/area_0308.npy  \n", "  inflating: Dataset/train_track_B/area_0310.npy  \n", "  inflating: Dataset/train_track_B/area_0311.npy  \n", "  inflating: Dataset/train_track_B/area_0314.npy  \n", "  inflating: Dataset/train_track_B/area_0315.npy  \n", "  inflating: Dataset/train_track_B/area_0316.npy  \n", "  inflating: Dataset/train_track_B/area_0320.npy  \n", "  inflating: Dataset/train_track_B/area_0321.npy  \n", "  inflating: Dataset/train_track_B/area_0323.npy  \n", "  inflating: Dataset/train_track_B/area_0324.npy  \n", "  inflating: Dataset/train_track_B/area_0327.npy  \n", "  inflating: Dataset/train_track_B/area_0330.npy  \n", "  inflating: Dataset/train_track_B/area_0331.npy  \n", "  inflating: Dataset/train_track_B/area_0332.npy  \n", "  inflating: Dataset/train_track_B/area_0333.npy  \n", "  inflating: Dataset/train_track_B/area_0334.npy  \n", "  inflating: Dataset/train_track_B/area_0337.npy  \n", "  inflating: Dataset/train_track_B/area_0338.npy  \n", "  inflating: Dataset/train_track_B/area_0339.npy  \n", "  inflating: Dataset/train_track_B/area_0340.npy  \n", "  inflating: Dataset/train_track_B/area_0341.npy  \n", "  inflating: Dataset/train_track_B/area_0342.npy  \n", "  inflating: Dataset/train_track_B/area_0343.npy  \n", "  inflating: Dataset/train_track_B/area_0344.npy  \n", "  inflating: Dataset/train_track_B/area_0345.npy  \n", "  inflating: Dataset/train_track_B/area_0346.npy  \n", "  inflating: Dataset/train_track_B/area_0348.npy  \n", "  inflating: Dataset/train_track_B/area_0349.npy  \n", "  inflating: Dataset/train_track_B/area_0351.npy  \n", "  inflating: Dataset/train_track_B/area_0352.npy  \n", "  inflating: Dataset/train_track_B/area_0353.npy  \n", "  inflating: Dataset/train_track_B/area_0354.npy  \n", "  inflating: Dataset/train_track_B/area_0356.npy  \n", "  inflating: Dataset/train_track_B/area_0357.npy  \n", "  inflating: Dataset/train_track_B/area_0359.npy  \n", "  inflating: Dataset/train_track_B/area_0360.npy  \n", "  inflating: Dataset/train_track_B/area_0361.npy  \n", "  inflating: Dataset/train_track_B/area_0363.npy  \n", "  inflating: Dataset/train_track_B/area_0364.npy  \n", "  inflating: Dataset/train_track_B/area_0365.npy  \n", "  inflating: Dataset/train_track_B/area_0366.npy  \n", "  inflating: Dataset/train_track_B/area_0367.npy  \n", "  inflating: Dataset/train_track_B/area_0368.npy  \n", "  inflating: Dataset/train_track_B/area_0369.npy  \n", "  inflating: Dataset/train_track_B/area_0371.npy  \n", "  inflating: Dataset/train_track_B/area_0373.npy  \n", "  inflating: Dataset/train_track_B/area_0376.npy  \n", "  inflating: Dataset/train_track_B/area_0377.npy  \n", "  inflating: Dataset/train_track_B/area_0378.npy  \n", "  inflating: Dataset/train_track_B/area_0379.npy  \n", "  inflating: Dataset/train_track_B/area_0381.npy  \n", "  inflating: Dataset/train_track_B/area_0382.npy  \n", "  inflating: Dataset/train_track_B/area_0383.npy  \n", "  inflating: Dataset/train_track_B/area_0384.npy  \n", "  inflating: Dataset/train_track_B/area_0385.npy  \n", "  inflating: Dataset/train_track_B/area_0387.npy  \n", "  inflating: Dataset/train_track_B/area_0388.npy  \n", "  inflating: Dataset/train_track_B/area_0389.npy  \n", "  inflating: Dataset/train_track_B/area_0392.npy  \n", "  inflating: Dataset/train_track_B/area_0393.npy  \n", "  inflating: Dataset/train_track_B/area_0394.npy  \n", "  inflating: Dataset/train_track_B/area_0395.npy  \n", "  inflating: Dataset/train_track_B/area_0396.npy  \n", "  inflating: Dataset/train_track_B/area_0398.npy  \n", "  inflating: Dataset/train_track_B/area_0399.npy  \n", "  inflating: Dataset/train_track_B/area_0400.npy  \n", "  inflating: Dataset/train_track_B/area_0401.npy  \n", "  inflating: Dataset/train_track_B/area_0402.npy  \n", "  inflating: Dataset/train_track_B/area_0403.npy  \n", "  inflating: Dataset/train_track_B/area_0404.npy  \n", "  inflating: Dataset/train_track_B/area_0405.npy  \n", "  inflating: Dataset/train_track_B/area_0407.npy  \n", "  inflating: Dataset/train_track_B/area_0408.npy  \n", "  inflating: Dataset/train_track_B/area_0409.npy  \n", "  inflating: Dataset/train_track_B/area_0410.npy  \n", "  inflating: Dataset/train_track_B/area_0411.npy  \n", "  inflating: Dataset/train_track_B/area_0413.npy  \n", "  inflating: Dataset/train_track_B/area_0416.npy  \n", "  inflating: Dataset/train_track_B/area_0417.npy  \n", "  inflating: Dataset/train_track_B/area_0421.npy  \n", "  inflating: Dataset/train_track_B/area_0422.npy  \n", "  inflating: Dataset/train_track_B/area_0423.npy  \n", "  inflating: Dataset/train_track_B/area_0424.npy  \n", "  inflating: Dataset/train_track_B/area_0425.npy  \n", "  inflating: Dataset/train_track_B/area_0428.npy  \n", "  inflating: Dataset/train_track_B/area_0429.npy  \n", "  inflating: Dataset/train_track_B/area_0430.npy  \n", "  inflating: Dataset/train_track_B/area_0431.npy  \n", "  inflating: Dataset/train_track_B/area_0432.npy  \n", "  inflating: Dataset/train_track_B/area_0435.npy  \n", "  inflating: Dataset/train_track_B/area_0438.npy  \n", "  inflating: Dataset/train_track_B/area_0439.npy  \n", "  inflating: Dataset/train_track_B/area_0441.npy  \n", "  inflating: Dataset/train_track_B/area_0444.npy  \n", "  inflating: Dataset/train_track_B/area_0445.npy  \n", "  inflating: Dataset/train_track_B/area_0449.npy  \n", "  inflating: Dataset/train_track_B/area_0450.npy  \n", "  inflating: Dataset/train_track_B/area_0451.npy  \n", "  inflating: Dataset/train_track_B/area_0452.npy  \n", "  inflating: Dataset/train_track_B/area_0453.npy  \n", "  inflating: Dataset/train_track_B/area_0456.npy  \n", "  inflating: Dataset/train_track_B/area_0457.npy  \n", "  inflating: Dataset/train_track_B/area_0458.npy  \n", "  inflating: Dataset/train_track_B/area_0459.npy  \n", "  inflating: Dataset/train_track_B/area_0460.npy  \n", "  inflating: Dataset/train_track_B/area_0461.npy  \n", "  inflating: Dataset/train_track_B/area_0463.npy  \n", "  inflating: Dataset/train_track_B/area_0464.npy  \n", "  inflating: Dataset/train_track_B/area_0465.npy  \n", "  inflating: Dataset/train_track_B/area_0467.npy  \n", "  inflating: Dataset/train_track_B/area_0469.npy  \n", "  inflating: Dataset/train_track_B/area_0471.npy  \n", "  inflating: Dataset/train_track_B/area_0472.npy  \n", "  inflating: Dataset/train_track_B/area_0474.npy  \n", "  inflating: Dataset/train_track_B/area_0475.npy  \n", "  inflating: Dataset/train_track_B/area_0477.npy  \n", "  inflating: Dataset/train_track_B/area_0478.npy  \n", "  inflating: Dataset/train_track_B/area_0479.npy  \n", "  inflating: Dataset/train_track_B/area_0480.npy  \n", "  inflating: Dataset/train_track_B/area_0481.npy  \n", "  inflating: Dataset/train_track_B/area_0482.npy  \n", "  inflating: Dataset/train_track_B/area_0485.npy  \n", "  inflating: Dataset/train_track_B/area_0486.npy  \n", "  inflating: Dataset/train_track_B/area_0487.npy  \n", "  inflating: Dataset/train_track_B/area_0488.npy  \n", "  inflating: Dataset/train_track_B/area_0489.npy  \n", "  inflating: Dataset/train_track_B/area_0492.npy  \n", "  inflating: Dataset/train_track_B/area_0493.npy  \n", "  inflating: Dataset/train_track_B/area_0494.npy  \n", "  inflating: Dataset/train_track_B/area_0497.npy  \n", "  inflating: Dataset/train_track_B/area_0498.npy  \n", "  inflating: Dataset/train_track_B/area_0499.npy  \n", "  inflating: Dataset/train_track_B/area_0501.npy  \n", "  inflating: Dataset/train_track_B/area_0502.npy  \n", "  inflating: Dataset/train_track_B/area_0503.npy  \n", "  inflating: Dataset/train_track_B/area_0504.npy  \n", "  inflating: Dataset/train_track_B/area_0507.npy  \n", "  inflating: Dataset/train_track_B/area_0508.npy  \n", "  inflating: Dataset/train_track_B/area_0509.npy  \n", "  inflating: Dataset/train_track_B/area_0513.npy  \n", "  inflating: Dataset/train_track_B/area_0514.npy  \n", "  inflating: Dataset/train_track_B/area_0515.npy  \n", "  inflating: Dataset/train_track_B/area_0517.npy  \n", "  inflating: Dataset/train_track_B/area_0518.npy  \n", "  inflating: Dataset/train_track_B/area_0519.npy  \n", "  inflating: Dataset/train_track_B/area_0520.npy  \n", "  inflating: Dataset/train_track_B/area_0521.npy  \n", "  inflating: Dataset/train_track_B/area_0522.npy  \n", "  inflating: Dataset/train_track_B/area_0523.npy  \n", "  inflating: Dataset/train_track_B/area_0524.npy  \n", "  inflating: Dataset/train_track_B/area_0525.npy  \n", "  inflating: Dataset/train_track_B/area_0526.npy  \n", "  inflating: Dataset/train_track_B/area_0527.npy  \n", "  inflating: Dataset/train_track_B/area_0528.npy  \n", "  inflating: Dataset/train_track_B/area_0529.npy  \n", "  inflating: Dataset/train_track_B/area_0530.npy  \n", "  inflating: Dataset/train_track_B/area_0531.npy  \n", "  inflating: Dataset/train_track_B/area_0534.npy  \n", "  inflating: Dataset/train_track_B/area_0535.npy  \n", "  inflating: Dataset/train_track_B/area_0536.npy  \n", "  inflating: Dataset/train_track_B/area_0538.npy  \n", "  inflating: Dataset/train_track_B/area_0541.npy  \n", "  inflating: Dataset/train_track_B/area_0542.npy  \n", "  inflating: Dataset/train_track_B/area_0544.npy  \n", "  inflating: Dataset/train_track_B/area_0545.npy  \n", "  inflating: Dataset/train_track_B/area_0546.npy  \n", "  inflating: Dataset/train_track_B/area_0547.npy  \n", "  inflating: Dataset/train_track_B/area_0550.npy  \n", "  inflating: Dataset/train_track_B/area_0551.npy  \n", "  inflating: Dataset/train_track_B/area_0553.npy  \n", "  inflating: Dataset/train_track_B/area_0555.npy  \n", "  inflating: Dataset/train_track_B/area_0557.npy  \n", "  inflating: Dataset/train_track_B/area_0558.npy  \n", "  inflating: Dataset/train_track_B/area_0561.npy  \n", "  inflating: Dataset/train_track_B/area_0563.npy  \n", "  inflating: Dataset/train_track_B/area_0564.npy  \n", "  inflating: Dataset/train_track_B/area_0565.npy  \n", "  inflating: Dataset/train_track_B/area_0567.npy  \n", "  inflating: Dataset/train_track_B/area_0568.npy  \n", "  inflating: Dataset/train_track_B/area_0571.npy  \n", "  inflating: Dataset/train_track_B/area_0574.npy  \n", "  inflating: Dataset/train_track_B/area_0576.npy  \n", "  inflating: Dataset/train_track_B/area_0579.npy  \n", "  inflating: Dataset/train_track_B/area_0580.npy  \n", "  inflating: Dataset/train_track_B/area_0582.npy  \n", "  inflating: Dataset/train_track_B/area_0584.npy  \n", "  inflating: Dataset/train_track_B/area_0585.npy  \n", "  inflating: Dataset/train_track_B/area_0588.npy  \n", "  inflating: Dataset/train_track_B/area_0589.npy  \n", "  inflating: Dataset/train_track_B/area_0590.npy  \n", "  inflating: Dataset/train_track_B/area_0591.npy  \n", "  inflating: Dataset/train_track_B/area_0592.npy  \n", "  inflating: Dataset/train_track_B/area_0593.npy  \n", "  inflating: Dataset/train_track_B/area_0594.npy  \n", "  inflating: Dataset/train_track_B/area_0595.npy  \n", "  inflating: Dataset/train_track_B/area_0596.npy  \n", "  inflating: Dataset/train_track_B/area_0597.npy  \n", "  inflating: Dataset/train_track_B/area_0598.npy  \n", "  inflating: Dataset/train_track_B/area_0600.npy  \n", "  inflating: Dataset/train_track_B/area_0602.npy  \n", "  inflating: Dataset/train_track_B/area_0605.npy  \n", "  inflating: Dataset/train_track_B/area_0608.npy  \n", "  inflating: Dataset/train_track_B/area_0609.npy  \n", "  inflating: Dataset/train_track_B/area_0611.npy  \n", "  inflating: Dataset/train_track_B/area_0612.npy  \n", "  inflating: Dataset/train_track_B/area_0613.npy  \n", "  inflating: Dataset/train_track_B/area_0614.npy  \n", "  inflating: Dataset/train_track_B/area_0618.npy  \n", "  inflating: Dataset/train_track_B/area_0619.npy  \n", "  inflating: Dataset/train_track_B/area_0620.npy  \n", "  inflating: Dataset/train_track_B/area_0621.npy  \n", "  inflating: Dataset/train_track_B/area_0622.npy  \n", "  inflating: Dataset/train_track_B/area_0623.npy  \n", "  inflating: Dataset/train_track_B/area_0624.npy  \n", "  inflating: Dataset/train_track_B/area_0625.npy  \n", "  inflating: Dataset/train_track_B/area_0627.npy  \n", "  inflating: Dataset/train_track_B/area_0628.npy  \n", "  inflating: Dataset/train_track_B/area_0629.npy  \n", "  inflating: Dataset/train_track_B/area_0630.npy  \n", "  inflating: Dataset/train_track_B/area_0631.npy  \n", "  inflating: Dataset/train_track_B/area_0632.npy  \n", "  inflating: Dataset/train_track_B/area_0633.npy  \n", "  inflating: Dataset/train_track_B/area_0634.npy  \n", "  inflating: Dataset/train_track_B/area_0635.npy  \n", "  inflating: Dataset/train_track_B/area_0637.npy  \n", "  inflating: Dataset/train_track_B/area_0638.npy  \n", "  inflating: Dataset/train_track_B/area_0639.npy  \n", "  inflating: Dataset/train_track_B/area_0640.npy  \n", "  inflating: Dataset/train_track_B/area_0641.npy  \n", "  inflating: Dataset/train_track_B/area_0643.npy  \n", "  inflating: Dataset/train_track_B/area_0644.npy  \n", "  inflating: Dataset/train_track_B/area_0645.npy  \n", "  inflating: Dataset/train_track_B/area_0646.npy  \n", "  inflating: Dataset/train_track_B/area_0648.npy  \n", "  inflating: Dataset/train_track_B/area_0650.npy  \n", "  inflating: Dataset/train_track_B/area_0651.npy  \n", "  inflating: Dataset/train_track_B/area_0652.npy  \n", "  inflating: Dataset/train_track_B/area_0653.npy  \n", "  inflating: Dataset/train_track_B/area_0654.npy  \n", "  inflating: Dataset/train_track_B/area_0656.npy  \n", "  inflating: Dataset/train_track_B/area_0657.npy  \n", "  inflating: Dataset/train_track_B/area_0658.npy  \n", "  inflating: Dataset/train_track_B/area_0661.npy  \n", "  inflating: Dataset/train_track_B/area_0663.npy  \n", "  inflating: Dataset/train_track_B/area_0664.npy  \n", "  inflating: Dataset/train_track_B/area_0665.npy  \n", "  inflating: Dataset/train_track_B/area_0666.npy  \n", "  inflating: Dataset/train_track_B/area_0667.npy  \n", "  inflating: Dataset/train_track_B/area_0668.npy  \n", "  inflating: Dataset/train_track_B/area_0669.npy  \n", "  inflating: Dataset/train_track_B/area_0671.npy  \n", "  inflating: Dataset/train_track_B/area_0672.npy  \n", "  inflating: Dataset/train_track_B/area_0673.npy  \n", "  inflating: Dataset/train_track_B/area_0674.npy  \n", "  inflating: Dataset/train_track_B/area_0676.npy  \n", "  inflating: Dataset/train_track_B/area_0677.npy  \n", "  inflating: Dataset/train_track_B/area_0678.npy  \n", "  inflating: Dataset/train_track_B/area_0679.npy  \n", "  inflating: Dataset/train_track_B/area_0680.npy  \n", "  inflating: Dataset/train_track_B/area_0682.npy  \n", "  inflating: Dataset/train_track_B/area_0686.npy  \n", "  inflating: Dataset/train_track_B/area_0688.npy  \n", "  inflating: Dataset/train_track_B/area_0689.npy  \n", "  inflating: Dataset/train_track_B/area_0690.npy  \n", "  inflating: Dataset/train_track_B/area_0691.npy  \n", "  inflating: Dataset/train_track_B/area_0692.npy  \n", "  inflating: Dataset/train_track_B/area_0693.npy  \n", "  inflating: Dataset/train_track_B/area_0694.npy  \n", "  inflating: Dataset/train_track_B/area_0695.npy  \n", "  inflating: Dataset/train_track_B/area_0697.npy  \n", "  inflating: Dataset/train_track_B/area_0699.npy  \n", "  inflating: Dataset/train_track_B/area_0700.npy  \n", "  inflating: Dataset/train_track_B/area_0701.npy  \n", "  inflating: Dataset/train_track_B/area_0703.npy  \n", "  inflating: Dataset/train_track_B/area_0704.npy  \n", "  inflating: Dataset/train_track_B/area_0706.npy  \n", "  inflating: Dataset/train_track_B/area_0707.npy  \n", "  inflating: Dataset/train_track_B/area_0708.npy  \n", "  inflating: Dataset/train_track_B/area_0709.npy  \n", "  inflating: Dataset/train_track_B/area_0711.npy  \n", "  inflating: Dataset/train_track_B/area_0712.npy  \n", "  inflating: Dataset/train_track_B/area_0713.npy  \n", "  inflating: Dataset/train_track_B/area_0714.npy  \n", "  inflating: Dataset/train_track_B/area_0715.npy  \n", "  inflating: Dataset/train_track_B/area_0716.npy  \n", "  inflating: Dataset/train_track_B/area_0718.npy  \n", "  inflating: Dataset/train_track_B/area_0719.npy  \n", "  inflating: Dataset/train_track_B/area_0720.npy  \n", "  inflating: Dataset/train_track_B/area_0721.npy  \n", "  inflating: Dataset/train_track_B/area_0722.npy  \n", "  inflating: Dataset/train_track_B/area_0724.npy  \n", "  inflating: Dataset/train_track_B/area_0727.npy  \n", "  inflating: Dataset/train_track_B/area_0728.npy  \n", "  inflating: Dataset/train_track_B/area_0729.npy  \n", "  inflating: Dataset/train_track_B/area_0730.npy  \n", "  inflating: Dataset/train_track_B/area_0731.npy  \n", "  inflating: Dataset/train_track_B/area_0733.npy  \n", "  inflating: Dataset/train_track_B/area_0735.npy  \n", "  inflating: Dataset/train_track_B/area_0736.npy  \n", "  inflating: Dataset/train_track_B/area_0737.npy  \n", "  inflating: Dataset/train_track_B/area_0740.npy  \n", "  inflating: Dataset/train_track_B/area_0742.npy  \n", "  inflating: Dataset/train_track_B/area_0743.npy  \n", "  inflating: Dataset/train_track_B/area_0744.npy  \n", "  inflating: Dataset/train_track_B/area_0745.npy  \n", "  inflating: Dataset/train_track_B/centroid_0002.npy  \n", "  inflating: Dataset/train_track_B/centroid_0003.npy  \n", "  inflating: Dataset/train_track_B/centroid_0004.npy  \n", "  inflating: Dataset/train_track_B/centroid_0005.npy  \n", "  inflating: Dataset/train_track_B/centroid_0006.npy  \n", "  inflating: Dataset/train_track_B/centroid_0011.npy  \n", "  inflating: Dataset/train_track_B/centroid_0012.npy  \n", "  inflating: Dataset/train_track_B/centroid_0013.npy  \n", "  inflating: Dataset/train_track_B/centroid_0015.npy  \n", "  inflating: Dataset/train_track_B/centroid_0017.npy  \n", "  inflating: Dataset/train_track_B/centroid_0018.npy  \n", "  inflating: Dataset/train_track_B/centroid_0020.npy  \n", "  inflating: Dataset/train_track_B/centroid_0021.npy  \n", "  inflating: Dataset/train_track_B/centroid_0022.npy  \n", "  inflating: Dataset/train_track_B/centroid_0023.npy  \n", "  inflating: Dataset/train_track_B/centroid_0024.npy  \n", "  inflating: Dataset/train_track_B/centroid_0026.npy  \n", "  inflating: Dataset/train_track_B/centroid_0029.npy  \n", "  inflating: Dataset/train_track_B/centroid_0030.npy  \n", "  inflating: Dataset/train_track_B/centroid_0036.npy  \n", "  inflating: Dataset/train_track_B/centroid_0037.npy  \n", "  inflating: Dataset/train_track_B/centroid_0038.npy  \n", "  inflating: Dataset/train_track_B/centroid_0039.npy  \n", "  inflating: Dataset/train_track_B/centroid_0040.npy  \n", "  inflating: Dataset/train_track_B/centroid_0041.npy  \n", "  inflating: Dataset/train_track_B/centroid_0042.npy  \n", "  inflating: Dataset/train_track_B/centroid_0043.npy  \n", "  inflating: Dataset/train_track_B/centroid_0044.npy  \n", "  inflating: Dataset/train_track_B/centroid_0048.npy  \n", "  inflating: Dataset/train_track_B/centroid_0049.npy  \n", "  inflating: Dataset/train_track_B/centroid_0051.npy  \n", "  inflating: Dataset/train_track_B/centroid_0052.npy  \n", "  inflating: Dataset/train_track_B/centroid_0055.npy  \n", "  inflating: Dataset/train_track_B/centroid_0056.npy  \n", "  inflating: Dataset/train_track_B/centroid_0057.npy  \n", "  inflating: Dataset/train_track_B/centroid_0059.npy  \n", "  inflating: Dataset/train_track_B/centroid_0062.npy  \n", "  inflating: Dataset/train_track_B/centroid_0064.npy  \n", "  inflating: Dataset/train_track_B/centroid_0066.npy  \n", "  inflating: Dataset/train_track_B/centroid_0067.npy  \n", "  inflating: Dataset/train_track_B/centroid_0068.npy  \n", "  inflating: Dataset/train_track_B/centroid_0071.npy  \n", "  inflating: Dataset/train_track_B/centroid_0074.npy  \n", "  inflating: Dataset/train_track_B/centroid_0075.npy  \n", "  inflating: Dataset/train_track_B/centroid_0077.npy  \n", "  inflating: Dataset/train_track_B/centroid_0078.npy  \n", "  inflating: Dataset/train_track_B/centroid_0080.npy  \n", "  inflating: Dataset/train_track_B/centroid_0081.npy  \n", "  inflating: Dataset/train_track_B/centroid_0082.npy  \n", "  inflating: Dataset/train_track_B/centroid_0084.npy  \n", "  inflating: Dataset/train_track_B/centroid_0085.npy  \n", "  inflating: Dataset/train_track_B/centroid_0086.npy  \n", "  inflating: Dataset/train_track_B/centroid_0087.npy  \n", "  inflating: Dataset/train_track_B/centroid_0088.npy  \n", "  inflating: Dataset/train_track_B/centroid_0089.npy  \n", "  inflating: Dataset/train_track_B/centroid_0090.npy  \n", "  inflating: Dataset/train_track_B/centroid_0092.npy  \n", "  inflating: Dataset/train_track_B/centroid_0093.npy  \n", "  inflating: Dataset/train_track_B/centroid_0094.npy  \n", "  inflating: Dataset/train_track_B/centroid_0095.npy  \n", "  inflating: Dataset/train_track_B/centroid_0097.npy  \n", "  inflating: Dataset/train_track_B/centroid_0098.npy  \n", "  inflating: Dataset/train_track_B/centroid_0100.npy  \n", "  inflating: Dataset/train_track_B/centroid_0101.npy  \n", "  inflating: Dataset/train_track_B/centroid_0102.npy  \n", "  inflating: Dataset/train_track_B/centroid_0103.npy  \n", "  inflating: Dataset/train_track_B/centroid_0104.npy  \n", "  inflating: Dataset/train_track_B/centroid_0106.npy  \n", "  inflating: Dataset/train_track_B/centroid_0107.npy  \n", "  inflating: Dataset/train_track_B/centroid_0108.npy  \n", "  inflating: Dataset/train_track_B/centroid_0109.npy  \n", "  inflating: Dataset/train_track_B/centroid_0110.npy  \n", "  inflating: Dataset/train_track_B/centroid_0113.npy  \n", "  inflating: Dataset/train_track_B/centroid_0114.npy  \n", "  inflating: Dataset/train_track_B/centroid_0115.npy  \n", "  inflating: Dataset/train_track_B/centroid_0116.npy  \n", "  inflating: Dataset/train_track_B/centroid_0117.npy  \n", "  inflating: Dataset/train_track_B/centroid_0118.npy  \n", "  inflating: Dataset/train_track_B/centroid_0119.npy  \n", "  inflating: Dataset/train_track_B/centroid_0120.npy  \n", "  inflating: Dataset/train_track_B/centroid_0121.npy  \n", "  inflating: Dataset/train_track_B/centroid_0122.npy  \n", "  inflating: Dataset/train_track_B/centroid_0124.npy  \n", "  inflating: Dataset/train_track_B/centroid_0125.npy  \n", "  inflating: Dataset/train_track_B/centroid_0126.npy  \n", "  inflating: Dataset/train_track_B/centroid_0128.npy  \n", "  inflating: Dataset/train_track_B/centroid_0129.npy  \n", "  inflating: Dataset/train_track_B/centroid_0130.npy  \n", "  inflating: Dataset/train_track_B/centroid_0131.npy  \n", "  inflating: Dataset/train_track_B/centroid_0132.npy  \n", "  inflating: Dataset/train_track_B/centroid_0133.npy  \n", "  inflating: Dataset/train_track_B/centroid_0134.npy  \n", "  inflating: Dataset/train_track_B/centroid_0135.npy  \n", "  inflating: Dataset/train_track_B/centroid_0136.npy  \n", "  inflating: Dataset/train_track_B/centroid_0138.npy  \n", "  inflating: Dataset/train_track_B/centroid_0139.npy  \n", "  inflating: Dataset/train_track_B/centroid_0140.npy  \n", "  inflating: Dataset/train_track_B/centroid_0141.npy  \n", "  inflating: Dataset/train_track_B/centroid_0143.npy  \n", "  inflating: Dataset/train_track_B/centroid_0145.npy  \n", "  inflating: Dataset/train_track_B/centroid_0146.npy  \n", "  inflating: Dataset/train_track_B/centroid_0148.npy  \n", "  inflating: Dataset/train_track_B/centroid_0149.npy  \n", "  inflating: Dataset/train_track_B/centroid_0150.npy  \n", "  inflating: Dataset/train_track_B/centroid_0151.npy  \n", "  inflating: Dataset/train_track_B/centroid_0153.npy  \n", "  inflating: Dataset/train_track_B/centroid_0154.npy  \n", "  inflating: Dataset/train_track_B/centroid_0156.npy  \n", "  inflating: Dataset/train_track_B/centroid_0157.npy  \n", "  inflating: Dataset/train_track_B/centroid_0158.npy  \n", "  inflating: Dataset/train_track_B/centroid_0161.npy  \n", "  inflating: Dataset/train_track_B/centroid_0162.npy  \n", "  inflating: Dataset/train_track_B/centroid_0163.npy  \n", "  inflating: Dataset/train_track_B/centroid_0164.npy  \n", "  inflating: Dataset/train_track_B/centroid_0166.npy  \n", "  inflating: Dataset/train_track_B/centroid_0167.npy  \n", "  inflating: Dataset/train_track_B/centroid_0168.npy  \n", "  inflating: Dataset/train_track_B/centroid_0170.npy  \n", "  inflating: Dataset/train_track_B/centroid_0171.npy  \n", "  inflating: Dataset/train_track_B/centroid_0172.npy  \n", "  inflating: Dataset/train_track_B/centroid_0174.npy  \n", "  inflating: Dataset/train_track_B/centroid_0175.npy  \n", "  inflating: Dataset/train_track_B/centroid_0183.npy  \n", "  inflating: Dataset/train_track_B/centroid_0184.npy  \n", "  inflating: Dataset/train_track_B/centroid_0185.npy  \n", "  inflating: Dataset/train_track_B/centroid_0189.npy  \n", "  inflating: Dataset/train_track_B/centroid_0190.npy  \n", "  inflating: Dataset/train_track_B/centroid_0193.npy  \n", "  inflating: Dataset/train_track_B/centroid_0194.npy  \n", "  inflating: Dataset/train_track_B/centroid_0195.npy  \n", "  inflating: Dataset/train_track_B/centroid_0197.npy  \n", "  inflating: Dataset/train_track_B/centroid_0201.npy  \n", "  inflating: Dataset/train_track_B/centroid_0203.npy  \n", "  inflating: Dataset/train_track_B/centroid_0204.npy  \n", "  inflating: Dataset/train_track_B/centroid_0205.npy  \n", "  inflating: Dataset/train_track_B/centroid_0206.npy  \n", "  inflating: Dataset/train_track_B/centroid_0208.npy  \n", "  inflating: Dataset/train_track_B/centroid_0210.npy  \n", "  inflating: Dataset/train_track_B/centroid_0211.npy  \n", "  inflating: Dataset/train_track_B/centroid_0216.npy  \n", "  inflating: Dataset/train_track_B/centroid_0217.npy  \n", "  inflating: Dataset/train_track_B/centroid_0219.npy  \n", "  inflating: Dataset/train_track_B/centroid_0220.npy  \n", "  inflating: Dataset/train_track_B/centroid_0227.npy  \n", "  inflating: Dataset/train_track_B/centroid_0228.npy  \n", "  inflating: Dataset/train_track_B/centroid_0229.npy  \n", "  inflating: Dataset/train_track_B/centroid_0232.npy  \n", "  inflating: Dataset/train_track_B/centroid_0234.npy  \n", "  inflating: Dataset/train_track_B/centroid_0235.npy  \n", "  inflating: Dataset/train_track_B/centroid_0236.npy  \n", "  inflating: Dataset/train_track_B/centroid_0238.npy  \n", "  inflating: Dataset/train_track_B/centroid_0239.npy  \n", "  inflating: Dataset/train_track_B/centroid_0240.npy  \n", "  inflating: Dataset/train_track_B/centroid_0241.npy  \n", "  inflating: Dataset/train_track_B/centroid_0245.npy  \n", "  inflating: Dataset/train_track_B/centroid_0246.npy  \n", "  inflating: Dataset/train_track_B/centroid_0247.npy  \n", "  inflating: Dataset/train_track_B/centroid_0248.npy  \n", "  inflating: Dataset/train_track_B/centroid_0249.npy  \n", "  inflating: Dataset/train_track_B/centroid_0252.npy  \n", "  inflating: Dataset/train_track_B/centroid_0253.npy  \n", "  inflating: Dataset/train_track_B/centroid_0254.npy  \n", "  inflating: Dataset/train_track_B/centroid_0256.npy  \n", "  inflating: Dataset/train_track_B/centroid_0257.npy  \n", "  inflating: Dataset/train_track_B/centroid_0259.npy  \n", "  inflating: Dataset/train_track_B/centroid_0264.npy  \n", "  inflating: Dataset/train_track_B/centroid_0265.npy  \n", "  inflating: Dataset/train_track_B/centroid_0266.npy  \n", "  inflating: Dataset/train_track_B/centroid_0268.npy  \n", "  inflating: Dataset/train_track_B/centroid_0269.npy  \n", "  inflating: Dataset/train_track_B/centroid_0271.npy  \n", "  inflating: Dataset/train_track_B/centroid_0272.npy  \n", "  inflating: Dataset/train_track_B/centroid_0273.npy  \n", "  inflating: Dataset/train_track_B/centroid_0275.npy  \n", "  inflating: Dataset/train_track_B/centroid_0276.npy  \n", "  inflating: Dataset/train_track_B/centroid_0277.npy  \n", "  inflating: Dataset/train_track_B/centroid_0279.npy  \n", "  inflating: Dataset/train_track_B/centroid_0280.npy  \n", "  inflating: Dataset/train_track_B/centroid_0281.npy  \n", "  inflating: Dataset/train_track_B/centroid_0284.npy  \n", "  inflating: Dataset/train_track_B/centroid_0285.npy  \n", "  inflating: Dataset/train_track_B/centroid_0286.npy  \n", "  inflating: Dataset/train_track_B/centroid_0288.npy  \n", "  inflating: Dataset/train_track_B/centroid_0289.npy  \n", "  inflating: Dataset/train_track_B/centroid_0290.npy  \n", "  inflating: Dataset/train_track_B/centroid_0291.npy  \n", "  inflating: Dataset/train_track_B/centroid_0294.npy  \n", "  inflating: Dataset/train_track_B/centroid_0296.npy  \n", "  inflating: Dataset/train_track_B/centroid_0297.npy  \n", "  inflating: Dataset/train_track_B/centroid_0298.npy  \n", "  inflating: Dataset/train_track_B/centroid_0301.npy  \n", "  inflating: Dataset/train_track_B/centroid_0304.npy  \n", "  inflating: Dataset/train_track_B/centroid_0305.npy  \n", "  inflating: Dataset/train_track_B/centroid_0306.npy  \n", "  inflating: Dataset/train_track_B/centroid_0307.npy  \n", "  inflating: Dataset/train_track_B/centroid_0308.npy  \n", "  inflating: Dataset/train_track_B/centroid_0310.npy  \n", "  inflating: Dataset/train_track_B/centroid_0311.npy  \n", "  inflating: Dataset/train_track_B/centroid_0314.npy  \n", "  inflating: Dataset/train_track_B/centroid_0315.npy  \n", "  inflating: Dataset/train_track_B/centroid_0316.npy  \n", "  inflating: Dataset/train_track_B/centroid_0320.npy  \n", "  inflating: Dataset/train_track_B/centroid_0321.npy  \n", "  inflating: Dataset/train_track_B/centroid_0323.npy  \n", "  inflating: Dataset/train_track_B/centroid_0324.npy  \n", "  inflating: Dataset/train_track_B/centroid_0327.npy  \n", "  inflating: Dataset/train_track_B/centroid_0330.npy  \n", "  inflating: Dataset/train_track_B/centroid_0331.npy  \n", "  inflating: Dataset/train_track_B/centroid_0332.npy  \n", "  inflating: Dataset/train_track_B/centroid_0333.npy  \n", "  inflating: Dataset/train_track_B/centroid_0334.npy  \n", "  inflating: Dataset/train_track_B/centroid_0337.npy  \n", "  inflating: Dataset/train_track_B/centroid_0338.npy  \n", "  inflating: Dataset/train_track_B/centroid_0339.npy  \n", "  inflating: Dataset/train_track_B/centroid_0340.npy  \n", "  inflating: Dataset/train_track_B/centroid_0341.npy  \n", "  inflating: Dataset/train_track_B/centroid_0342.npy  \n", "  inflating: Dataset/train_track_B/centroid_0343.npy  \n", "  inflating: Dataset/train_track_B/centroid_0344.npy  \n", "  inflating: Dataset/train_track_B/centroid_0345.npy  \n", "  inflating: Dataset/train_track_B/centroid_0346.npy  \n", "  inflating: Dataset/train_track_B/centroid_0348.npy  \n", "  inflating: Dataset/train_track_B/centroid_0349.npy  \n", "  inflating: Dataset/train_track_B/centroid_0351.npy  \n", "  inflating: Dataset/train_track_B/centroid_0352.npy  \n", "  inflating: Dataset/train_track_B/centroid_0353.npy  \n", "  inflating: Dataset/train_track_B/centroid_0354.npy  \n", "  inflating: Dataset/train_track_B/centroid_0356.npy  \n", "  inflating: Dataset/train_track_B/centroid_0357.npy  \n", "  inflating: Dataset/train_track_B/centroid_0359.npy  \n", "  inflating: Dataset/train_track_B/centroid_0360.npy  \n", "  inflating: Dataset/train_track_B/centroid_0361.npy  \n", "  inflating: Dataset/train_track_B/centroid_0363.npy  \n", "  inflating: Dataset/train_track_B/centroid_0364.npy  \n", "  inflating: Dataset/train_track_B/centroid_0365.npy  \n", "  inflating: Dataset/train_track_B/centroid_0366.npy  \n", "  inflating: Dataset/train_track_B/centroid_0367.npy  \n", "  inflating: Dataset/train_track_B/centroid_0368.npy  \n", "  inflating: Dataset/train_track_B/centroid_0369.npy  \n", "  inflating: Dataset/train_track_B/centroid_0371.npy  \n", "  inflating: Dataset/train_track_B/centroid_0373.npy  \n", "  inflating: Dataset/train_track_B/centroid_0376.npy  \n", "  inflating: Dataset/train_track_B/centroid_0377.npy  \n", "  inflating: Dataset/train_track_B/centroid_0378.npy  \n", "  inflating: Dataset/train_track_B/centroid_0379.npy  \n", "  inflating: Dataset/train_track_B/centroid_0381.npy  \n", "  inflating: Dataset/train_track_B/centroid_0382.npy  \n", "  inflating: Dataset/train_track_B/centroid_0383.npy  \n", "  inflating: Dataset/train_track_B/centroid_0384.npy  \n", "  inflating: Dataset/train_track_B/centroid_0385.npy  \n", "  inflating: Dataset/train_track_B/centroid_0387.npy  \n", "  inflating: Dataset/train_track_B/centroid_0388.npy  \n", "  inflating: Dataset/train_track_B/centroid_0389.npy  \n", "  inflating: Dataset/train_track_B/centroid_0392.npy  \n", "  inflating: Dataset/train_track_B/centroid_0393.npy  \n", "  inflating: Dataset/train_track_B/centroid_0394.npy  \n", "  inflating: Dataset/train_track_B/centroid_0395.npy  \n", "  inflating: Dataset/train_track_B/centroid_0396.npy  \n", "  inflating: Dataset/train_track_B/centroid_0398.npy  \n", "  inflating: Dataset/train_track_B/centroid_0399.npy  \n", "  inflating: Dataset/train_track_B/centroid_0400.npy  \n", "  inflating: Dataset/train_track_B/centroid_0401.npy  \n", "  inflating: Dataset/train_track_B/centroid_0402.npy  \n", "  inflating: Dataset/train_track_B/centroid_0403.npy  \n", "  inflating: Dataset/train_track_B/centroid_0404.npy  \n", "  inflating: Dataset/train_track_B/centroid_0405.npy  \n", "  inflating: Dataset/train_track_B/centroid_0407.npy  \n", "  inflating: Dataset/train_track_B/centroid_0408.npy  \n", "  inflating: Dataset/train_track_B/centroid_0409.npy  \n", "  inflating: Dataset/train_track_B/centroid_0410.npy  \n", "  inflating: Dataset/train_track_B/centroid_0411.npy  \n", "  inflating: Dataset/train_track_B/centroid_0413.npy  \n", "  inflating: Dataset/train_track_B/centroid_0416.npy  \n", "  inflating: Dataset/train_track_B/centroid_0417.npy  \n", "  inflating: Dataset/train_track_B/centroid_0421.npy  \n", "  inflating: Dataset/train_track_B/centroid_0422.npy  \n", "  inflating: Dataset/train_track_B/centroid_0423.npy  \n", "  inflating: Dataset/train_track_B/centroid_0424.npy  \n", "  inflating: Dataset/train_track_B/centroid_0425.npy  \n", "  inflating: Dataset/train_track_B/centroid_0428.npy  \n", "  inflating: Dataset/train_track_B/centroid_0429.npy  \n", "  inflating: Dataset/train_track_B/centroid_0430.npy  \n", "  inflating: Dataset/train_track_B/centroid_0431.npy  \n", "  inflating: Dataset/train_track_B/centroid_0432.npy  \n", "  inflating: Dataset/train_track_B/centroid_0435.npy  \n", "  inflating: Dataset/train_track_B/centroid_0438.npy  \n", "  inflating: Dataset/train_track_B/centroid_0439.npy  \n", "  inflating: Dataset/train_track_B/centroid_0441.npy  \n", "  inflating: Dataset/train_track_B/centroid_0444.npy  \n", "  inflating: Dataset/train_track_B/centroid_0445.npy  \n", "  inflating: Dataset/train_track_B/centroid_0449.npy  \n", "  inflating: Dataset/train_track_B/centroid_0450.npy  \n", "  inflating: Dataset/train_track_B/centroid_0451.npy  \n", "  inflating: Dataset/train_track_B/centroid_0452.npy  \n", "  inflating: Dataset/train_track_B/centroid_0453.npy  \n", "  inflating: Dataset/train_track_B/centroid_0456.npy  \n", "  inflating: Dataset/train_track_B/centroid_0457.npy  \n", "  inflating: Dataset/train_track_B/centroid_0458.npy  \n", "  inflating: Dataset/train_track_B/centroid_0459.npy  \n", "  inflating: Dataset/train_track_B/centroid_0460.npy  \n", "  inflating: Dataset/train_track_B/centroid_0461.npy  \n", "  inflating: Dataset/train_track_B/centroid_0463.npy  \n", "  inflating: Dataset/train_track_B/centroid_0464.npy  \n", "  inflating: Dataset/train_track_B/centroid_0465.npy  \n", "  inflating: Dataset/train_track_B/centroid_0467.npy  \n", "  inflating: Dataset/train_track_B/centroid_0469.npy  \n", "  inflating: Dataset/train_track_B/centroid_0471.npy  \n", "  inflating: Dataset/train_track_B/centroid_0472.npy  \n", "  inflating: Dataset/train_track_B/centroid_0474.npy  \n", "  inflating: Dataset/train_track_B/centroid_0475.npy  \n", "  inflating: Dataset/train_track_B/centroid_0477.npy  \n", "  inflating: Dataset/train_track_B/centroid_0478.npy  \n", "  inflating: Dataset/train_track_B/centroid_0479.npy  \n", "  inflating: Dataset/train_track_B/centroid_0480.npy  \n", "  inflating: Dataset/train_track_B/centroid_0481.npy  \n", "  inflating: Dataset/train_track_B/centroid_0482.npy  \n", "  inflating: Dataset/train_track_B/centroid_0485.npy  \n", "  inflating: Dataset/train_track_B/centroid_0486.npy  \n", "  inflating: Dataset/train_track_B/centroid_0487.npy  \n", "  inflating: Dataset/train_track_B/centroid_0488.npy  \n", "  inflating: Dataset/train_track_B/centroid_0489.npy  \n", "  inflating: Dataset/train_track_B/centroid_0492.npy  \n", "  inflating: Dataset/train_track_B/centroid_0493.npy  \n", "  inflating: Dataset/train_track_B/centroid_0494.npy  \n", "  inflating: Dataset/train_track_B/centroid_0497.npy  \n", "  inflating: Dataset/train_track_B/centroid_0498.npy  \n", "  inflating: Dataset/train_track_B/centroid_0499.npy  \n", "  inflating: Dataset/train_track_B/centroid_0501.npy  \n", "  inflating: Dataset/train_track_B/centroid_0502.npy  \n", "  inflating: Dataset/train_track_B/centroid_0503.npy  \n", "  inflating: Dataset/train_track_B/centroid_0504.npy  \n", "  inflating: Dataset/train_track_B/centroid_0507.npy  \n", "  inflating: Dataset/train_track_B/centroid_0508.npy  \n", "  inflating: Dataset/train_track_B/centroid_0509.npy  \n", "  inflating: Dataset/train_track_B/centroid_0513.npy  \n", "  inflating: Dataset/train_track_B/centroid_0514.npy  \n", "  inflating: Dataset/train_track_B/centroid_0515.npy  \n", "  inflating: Dataset/train_track_B/centroid_0517.npy  \n", "  inflating: Dataset/train_track_B/centroid_0518.npy  \n", "  inflating: Dataset/train_track_B/centroid_0519.npy  \n", "  inflating: Dataset/train_track_B/centroid_0520.npy  \n", "  inflating: Dataset/train_track_B/centroid_0521.npy  \n", "  inflating: Dataset/train_track_B/centroid_0522.npy  \n", "  inflating: Dataset/train_track_B/centroid_0523.npy  \n", "  inflating: Dataset/train_track_B/centroid_0524.npy  \n", "  inflating: Dataset/train_track_B/centroid_0525.npy  \n", "  inflating: Dataset/train_track_B/centroid_0526.npy  \n", "  inflating: Dataset/train_track_B/centroid_0527.npy  \n", "  inflating: Dataset/train_track_B/centroid_0528.npy  \n", "  inflating: Dataset/train_track_B/centroid_0529.npy  \n", "  inflating: Dataset/train_track_B/centroid_0530.npy  \n", "  inflating: Dataset/train_track_B/centroid_0531.npy  \n", "  inflating: Dataset/train_track_B/centroid_0534.npy  \n", "  inflating: Dataset/train_track_B/centroid_0535.npy  \n", "  inflating: Dataset/train_track_B/centroid_0536.npy  \n", "  inflating: Dataset/train_track_B/centroid_0538.npy  \n", "  inflating: Dataset/train_track_B/centroid_0541.npy  \n", "  inflating: Dataset/train_track_B/centroid_0542.npy  \n", "  inflating: Dataset/train_track_B/centroid_0544.npy  \n", "  inflating: Dataset/train_track_B/centroid_0545.npy  \n", "  inflating: Dataset/train_track_B/centroid_0546.npy  \n", "  inflating: Dataset/train_track_B/centroid_0547.npy  \n", "  inflating: Dataset/train_track_B/centroid_0550.npy  \n", "  inflating: Dataset/train_track_B/centroid_0551.npy  \n", "  inflating: Dataset/train_track_B/centroid_0553.npy  \n", "  inflating: Dataset/train_track_B/centroid_0555.npy  \n", "  inflating: Dataset/train_track_B/centroid_0557.npy  \n", "  inflating: Dataset/train_track_B/centroid_0558.npy  \n", "  inflating: Dataset/train_track_B/centroid_0561.npy  \n", "  inflating: Dataset/train_track_B/centroid_0563.npy  \n", "  inflating: Dataset/train_track_B/centroid_0564.npy  \n", "  inflating: Dataset/train_track_B/centroid_0565.npy  \n", "  inflating: Dataset/train_track_B/centroid_0567.npy  \n", "  inflating: Dataset/train_track_B/centroid_0568.npy  \n", "  inflating: Dataset/train_track_B/centroid_0571.npy  \n", "  inflating: Dataset/train_track_B/centroid_0574.npy  \n", "  inflating: Dataset/train_track_B/centroid_0576.npy  \n", "  inflating: Dataset/train_track_B/centroid_0579.npy  \n", "  inflating: Dataset/train_track_B/centroid_0580.npy  \n", "  inflating: Dataset/train_track_B/centroid_0582.npy  \n", "  inflating: Dataset/train_track_B/centroid_0584.npy  \n", "  inflating: Dataset/train_track_B/centroid_0585.npy  \n", "  inflating: Dataset/train_track_B/centroid_0588.npy  \n", "  inflating: Dataset/train_track_B/centroid_0589.npy  \n", "  inflating: Dataset/train_track_B/centroid_0590.npy  \n", "  inflating: Dataset/train_track_B/centroid_0591.npy  \n", "  inflating: Dataset/train_track_B/centroid_0592.npy  \n", "  inflating: Dataset/train_track_B/centroid_0593.npy  \n", "  inflating: Dataset/train_track_B/centroid_0594.npy  \n", "  inflating: Dataset/train_track_B/centroid_0595.npy  \n", "  inflating: Dataset/train_track_B/centroid_0596.npy  \n", "  inflating: Dataset/train_track_B/centroid_0597.npy  \n", "  inflating: Dataset/train_track_B/centroid_0598.npy  \n", "  inflating: Dataset/train_track_B/centroid_0600.npy  \n", "  inflating: Dataset/train_track_B/centroid_0602.npy  \n", "  inflating: Dataset/train_track_B/centroid_0605.npy  \n", "  inflating: Dataset/train_track_B/centroid_0608.npy  \n", "  inflating: Dataset/train_track_B/centroid_0609.npy  \n", "  inflating: Dataset/train_track_B/centroid_0611.npy  \n", "  inflating: Dataset/train_track_B/centroid_0612.npy  \n", "  inflating: Dataset/train_track_B/centroid_0613.npy  \n", "  inflating: Dataset/train_track_B/centroid_0614.npy  \n", "  inflating: Dataset/train_track_B/centroid_0618.npy  \n", "  inflating: Dataset/train_track_B/centroid_0619.npy  \n", "  inflating: Dataset/train_track_B/centroid_0620.npy  \n", "  inflating: Dataset/train_track_B/centroid_0621.npy  \n", "  inflating: Dataset/train_track_B/centroid_0622.npy  \n", "  inflating: Dataset/train_track_B/centroid_0623.npy  \n", "  inflating: Dataset/train_track_B/centroid_0624.npy  \n", "  inflating: Dataset/train_track_B/centroid_0625.npy  \n", "  inflating: Dataset/train_track_B/centroid_0627.npy  \n", "  inflating: Dataset/train_track_B/centroid_0628.npy  \n", "  inflating: Dataset/train_track_B/centroid_0629.npy  \n", "  inflating: Dataset/train_track_B/centroid_0630.npy  \n", "  inflating: Dataset/train_track_B/centroid_0631.npy  \n", "  inflating: Dataset/train_track_B/centroid_0632.npy  \n", "  inflating: Dataset/train_track_B/centroid_0633.npy  \n", "  inflating: Dataset/train_track_B/centroid_0634.npy  \n", "  inflating: Dataset/train_track_B/centroid_0635.npy  \n", "  inflating: Dataset/train_track_B/centroid_0637.npy  \n", "  inflating: Dataset/train_track_B/centroid_0638.npy  \n", "  inflating: Dataset/train_track_B/centroid_0639.npy  \n", "  inflating: Dataset/train_track_B/centroid_0640.npy  \n", "  inflating: Dataset/train_track_B/centroid_0641.npy  \n", "  inflating: Dataset/train_track_B/centroid_0643.npy  \n", "  inflating: Dataset/train_track_B/centroid_0644.npy  \n", "  inflating: Dataset/train_track_B/centroid_0645.npy  \n", "  inflating: Dataset/train_track_B/centroid_0646.npy  \n", "  inflating: Dataset/train_track_B/centroid_0648.npy  \n", "  inflating: Dataset/train_track_B/centroid_0650.npy  \n", "  inflating: Dataset/train_track_B/centroid_0651.npy  \n", "  inflating: Dataset/train_track_B/centroid_0652.npy  \n", "  inflating: Dataset/train_track_B/centroid_0653.npy  \n", "  inflating: Dataset/train_track_B/centroid_0654.npy  \n", "  inflating: Dataset/train_track_B/centroid_0656.npy  \n", "  inflating: Dataset/train_track_B/centroid_0657.npy  \n", "  inflating: Dataset/train_track_B/centroid_0658.npy  \n", "  inflating: Dataset/train_track_B/centroid_0661.npy  \n", "  inflating: Dataset/train_track_B/centroid_0663.npy  \n", "  inflating: Dataset/train_track_B/centroid_0664.npy  \n", "  inflating: Dataset/train_track_B/centroid_0665.npy  \n", "  inflating: Dataset/train_track_B/centroid_0666.npy  \n", "  inflating: Dataset/train_track_B/centroid_0667.npy  \n", "  inflating: Dataset/train_track_B/centroid_0668.npy  \n", "  inflating: Dataset/train_track_B/centroid_0669.npy  \n", "  inflating: Dataset/train_track_B/centroid_0671.npy  \n", "  inflating: Dataset/train_track_B/centroid_0672.npy  \n", "  inflating: Dataset/train_track_B/centroid_0673.npy  \n", "  inflating: Dataset/train_track_B/centroid_0674.npy  \n", "  inflating: Dataset/train_track_B/centroid_0676.npy  \n", "  inflating: Dataset/train_track_B/centroid_0677.npy  \n", "  inflating: Dataset/train_track_B/centroid_0678.npy  \n", "  inflating: Dataset/train_track_B/centroid_0679.npy  \n", "  inflating: Dataset/train_track_B/centroid_0680.npy  \n", "  inflating: Dataset/train_track_B/centroid_0682.npy  \n", "  inflating: Dataset/train_track_B/centroid_0686.npy  \n", "  inflating: Dataset/train_track_B/centroid_0688.npy  \n", "  inflating: Dataset/train_track_B/centroid_0689.npy  \n", "  inflating: Dataset/train_track_B/centroid_0690.npy  \n", "  inflating: Dataset/train_track_B/centroid_0691.npy  \n", "  inflating: Dataset/train_track_B/centroid_0692.npy  \n", "  inflating: Dataset/train_track_B/centroid_0693.npy  \n", "  inflating: Dataset/train_track_B/centroid_0694.npy  \n", "  inflating: Dataset/train_track_B/centroid_0695.npy  \n", "  inflating: Dataset/train_track_B/centroid_0697.npy  \n", "  inflating: Dataset/train_track_B/centroid_0699.npy  \n", "  inflating: Dataset/train_track_B/centroid_0700.npy  \n", "  inflating: Dataset/train_track_B/centroid_0701.npy  \n", "  inflating: Dataset/train_track_B/centroid_0703.npy  \n", "  inflating: Dataset/train_track_B/centroid_0704.npy  \n", "  inflating: Dataset/train_track_B/centroid_0706.npy  \n", "  inflating: Dataset/train_track_B/centroid_0707.npy  \n", "  inflating: Dataset/train_track_B/centroid_0708.npy  \n", "  inflating: Dataset/train_track_B/centroid_0709.npy  \n", "  inflating: Dataset/train_track_B/centroid_0711.npy  \n", "  inflating: Dataset/train_track_B/centroid_0712.npy  \n", "  inflating: Dataset/train_track_B/centroid_0713.npy  \n", "  inflating: Dataset/train_track_B/centroid_0714.npy  \n", "  inflating: Dataset/train_track_B/centroid_0715.npy  \n", "  inflating: Dataset/train_track_B/centroid_0716.npy  \n", "  inflating: Dataset/train_track_B/centroid_0718.npy  \n", "  inflating: Dataset/train_track_B/centroid_0719.npy  \n", "  inflating: Dataset/train_track_B/centroid_0720.npy  \n", "  inflating: Dataset/train_track_B/centroid_0721.npy  \n", "  inflating: Dataset/train_track_B/centroid_0722.npy  \n", "  inflating: Dataset/train_track_B/centroid_0724.npy  \n", "  inflating: Dataset/train_track_B/centroid_0727.npy  \n", "  inflating: Dataset/train_track_B/centroid_0728.npy  \n", "  inflating: Dataset/train_track_B/centroid_0729.npy  \n", "  inflating: Dataset/train_track_B/centroid_0730.npy  \n", "  inflating: Dataset/train_track_B/centroid_0731.npy  \n", "  inflating: Dataset/train_track_B/centroid_0733.npy  \n", "  inflating: Dataset/train_track_B/centroid_0735.npy  \n", "  inflating: Dataset/train_track_B/centroid_0736.npy  \n", "  inflating: Dataset/train_track_B/centroid_0737.npy  \n", "  inflating: Dataset/train_track_B/centroid_0740.npy  \n", "  inflating: Dataset/train_track_B/centroid_0742.npy  \n", "  inflating: Dataset/train_track_B/centroid_0743.npy  \n", "  inflating: Dataset/train_track_B/centroid_0744.npy  \n", "  inflating: Dataset/train_track_B/centroid_0745.npy  \n", "  inflating: Dataset/train_track_B/press_0002.npy  \n", "  inflating: Dataset/train_track_B/press_0003.npy  \n", "  inflating: Dataset/train_track_B/press_0004.npy  \n", "  inflating: Dataset/train_track_B/press_0005.npy  \n", "  inflating: Dataset/train_track_B/press_0006.npy  \n", "  inflating: Dataset/train_track_B/press_0011.npy  \n", "  inflating: Dataset/train_track_B/press_0012.npy  \n", "  inflating: Dataset/train_track_B/press_0013.npy  \n", "  inflating: Dataset/train_track_B/press_0015.npy  \n", "  inflating: Dataset/train_track_B/press_0017.npy  \n", "  inflating: Dataset/train_track_B/press_0018.npy  \n", "  inflating: Dataset/train_track_B/press_0020.npy  \n", "  inflating: Dataset/train_track_B/press_0021.npy  \n", "  inflating: Dataset/train_track_B/press_0022.npy  \n", "  inflating: Dataset/train_track_B/press_0023.npy  \n", "  inflating: Dataset/train_track_B/press_0024.npy  \n", "  inflating: Dataset/train_track_B/press_0026.npy  \n", "  inflating: Dataset/train_track_B/press_0029.npy  \n", "  inflating: Dataset/train_track_B/press_0030.npy  \n", "  inflating: Dataset/train_track_B/press_0036.npy  \n", "  inflating: Dataset/train_track_B/press_0037.npy  \n", "  inflating: Dataset/train_track_B/press_0038.npy  \n", "  inflating: Dataset/train_track_B/press_0039.npy  \n", "  inflating: Dataset/train_track_B/press_0040.npy  \n", "  inflating: Dataset/train_track_B/press_0041.npy  \n", "  inflating: Dataset/train_track_B/press_0042.npy  \n", "  inflating: Dataset/train_track_B/press_0043.npy  \n", "  inflating: Dataset/train_track_B/press_0044.npy  \n", "  inflating: Dataset/train_track_B/press_0048.npy  \n", "  inflating: Dataset/train_track_B/press_0049.npy  \n", "  inflating: Dataset/train_track_B/press_0051.npy  \n", "  inflating: Dataset/train_track_B/press_0052.npy  \n", "  inflating: Dataset/train_track_B/press_0055.npy  \n", "  inflating: Dataset/train_track_B/press_0056.npy  \n", "  inflating: Dataset/train_track_B/press_0057.npy  \n", "  inflating: Dataset/train_track_B/press_0059.npy  \n", "  inflating: Dataset/train_track_B/press_0062.npy  \n", "  inflating: Dataset/train_track_B/press_0064.npy  \n", "  inflating: Dataset/train_track_B/press_0066.npy  \n", "  inflating: Dataset/train_track_B/press_0067.npy  \n", "  inflating: Dataset/train_track_B/press_0068.npy  \n", "  inflating: Dataset/train_track_B/press_0071.npy  \n", "  inflating: Dataset/train_track_B/press_0074.npy  \n", "  inflating: Dataset/train_track_B/press_0075.npy  \n", "  inflating: Dataset/train_track_B/press_0077.npy  \n", "  inflating: Dataset/train_track_B/press_0078.npy  \n", "  inflating: Dataset/train_track_B/press_0080.npy  \n", "  inflating: Dataset/train_track_B/press_0081.npy  \n", "  inflating: Dataset/train_track_B/press_0082.npy  \n", "  inflating: Dataset/train_track_B/press_0084.npy  \n", "  inflating: Dataset/train_track_B/press_0085.npy  \n", "  inflating: Dataset/train_track_B/press_0086.npy  \n", "  inflating: Dataset/train_track_B/press_0087.npy  \n", "  inflating: Dataset/train_track_B/press_0088.npy  \n", "  inflating: Dataset/train_track_B/press_0089.npy  \n", "  inflating: Dataset/train_track_B/press_0090.npy  \n", "  inflating: Dataset/train_track_B/press_0092.npy  \n", "  inflating: Dataset/train_track_B/press_0093.npy  \n", "  inflating: Dataset/train_track_B/press_0094.npy  \n", "  inflating: Dataset/train_track_B/press_0095.npy  \n", "  inflating: Dataset/train_track_B/press_0097.npy  \n", "  inflating: Dataset/train_track_B/press_0098.npy  \n", "  inflating: Dataset/train_track_B/press_0100.npy  \n", "  inflating: Dataset/train_track_B/press_0101.npy  \n", "  inflating: Dataset/train_track_B/press_0102.npy  \n", "  inflating: Dataset/train_track_B/press_0103.npy  \n", "  inflating: Dataset/train_track_B/press_0104.npy  \n", "  inflating: Dataset/train_track_B/press_0106.npy  \n", "  inflating: Dataset/train_track_B/press_0107.npy  \n", "  inflating: Dataset/train_track_B/press_0108.npy  \n", "  inflating: Dataset/train_track_B/press_0109.npy  \n", "  inflating: Dataset/train_track_B/press_0110.npy  \n", "  inflating: Dataset/train_track_B/press_0113.npy  \n", "  inflating: Dataset/train_track_B/press_0114.npy  \n", "  inflating: Dataset/train_track_B/press_0115.npy  \n", "  inflating: Dataset/train_track_B/press_0116.npy  \n", "  inflating: Dataset/train_track_B/press_0117.npy  \n", "  inflating: Dataset/train_track_B/press_0118.npy  \n", "  inflating: Dataset/train_track_B/press_0119.npy  \n", "  inflating: Dataset/train_track_B/press_0120.npy  \n", "  inflating: Dataset/train_track_B/press_0121.npy  \n", "  inflating: Dataset/train_track_B/press_0122.npy  \n", "  inflating: Dataset/train_track_B/press_0124.npy  \n", "  inflating: Dataset/train_track_B/press_0125.npy  \n", "  inflating: Dataset/train_track_B/press_0126.npy  \n", "  inflating: Dataset/train_track_B/press_0128.npy  \n", "  inflating: Dataset/train_track_B/press_0129.npy  \n", "  inflating: Dataset/train_track_B/press_0130.npy  \n", "  inflating: Dataset/train_track_B/press_0131.npy  \n", "  inflating: Dataset/train_track_B/press_0132.npy  \n", "  inflating: Dataset/train_track_B/press_0133.npy  \n", "  inflating: Dataset/train_track_B/press_0134.npy  \n", "  inflating: Dataset/train_track_B/press_0135.npy  \n", "  inflating: Dataset/train_track_B/press_0136.npy  \n", "  inflating: Dataset/train_track_B/press_0138.npy  \n", "  inflating: Dataset/train_track_B/press_0139.npy  \n", "  inflating: Dataset/train_track_B/press_0140.npy  \n", "  inflating: Dataset/train_track_B/press_0141.npy  \n", "  inflating: Dataset/train_track_B/press_0143.npy  \n", "  inflating: Dataset/train_track_B/press_0145.npy  \n", "  inflating: Dataset/train_track_B/press_0146.npy  \n", "  inflating: Dataset/train_track_B/press_0148.npy  \n", "  inflating: Dataset/train_track_B/press_0149.npy  \n", "  inflating: Dataset/train_track_B/press_0150.npy  \n", "  inflating: Dataset/train_track_B/press_0151.npy  \n", "  inflating: Dataset/train_track_B/press_0153.npy  \n", "  inflating: Dataset/train_track_B/press_0154.npy  \n", "  inflating: Dataset/train_track_B/press_0156.npy  \n", "  inflating: Dataset/train_track_B/press_0157.npy  \n", "  inflating: Dataset/train_track_B/press_0158.npy  \n", "  inflating: Dataset/train_track_B/press_0161.npy  \n", "  inflating: Dataset/train_track_B/press_0162.npy  \n", "  inflating: Dataset/train_track_B/press_0163.npy  \n", "  inflating: Dataset/train_track_B/press_0164.npy  \n", "  inflating: Dataset/train_track_B/press_0166.npy  \n", "  inflating: Dataset/train_track_B/press_0167.npy  \n", "  inflating: Dataset/train_track_B/press_0168.npy  \n", "  inflating: Dataset/train_track_B/press_0170.npy  \n", "  inflating: Dataset/train_track_B/press_0171.npy  \n", "  inflating: Dataset/train_track_B/press_0172.npy  \n", "  inflating: Dataset/train_track_B/press_0174.npy  \n", "  inflating: Dataset/train_track_B/press_0175.npy  \n", "  inflating: Dataset/train_track_B/press_0183.npy  \n", "  inflating: Dataset/train_track_B/press_0184.npy  \n", "  inflating: Dataset/train_track_B/press_0185.npy  \n", "  inflating: Dataset/train_track_B/press_0189.npy  \n", "  inflating: Dataset/train_track_B/press_0190.npy  \n", "  inflating: Dataset/train_track_B/press_0193.npy  \n", "  inflating: Dataset/train_track_B/press_0194.npy  \n", "  inflating: Dataset/train_track_B/press_0195.npy  \n", "  inflating: Dataset/train_track_B/press_0197.npy  \n", "  inflating: Dataset/train_track_B/press_0201.npy  \n", "  inflating: Dataset/train_track_B/press_0203.npy  \n", "  inflating: Dataset/train_track_B/press_0204.npy  \n", "  inflating: Dataset/train_track_B/press_0205.npy  \n", "  inflating: Dataset/train_track_B/press_0206.npy  \n", "  inflating: Dataset/train_track_B/press_0208.npy  \n", "  inflating: Dataset/train_track_B/press_0210.npy  \n", "  inflating: Dataset/train_track_B/press_0211.npy  \n", "  inflating: Dataset/train_track_B/press_0216.npy  \n", "  inflating: Dataset/train_track_B/press_0217.npy  \n", "  inflating: Dataset/train_track_B/press_0219.npy  \n", "  inflating: Dataset/train_track_B/press_0220.npy  \n", "  inflating: Dataset/train_track_B/press_0227.npy  \n", "  inflating: Dataset/train_track_B/press_0228.npy  \n", "  inflating: Dataset/train_track_B/press_0229.npy  \n", "  inflating: Dataset/train_track_B/press_0232.npy  \n", "  inflating: Dataset/train_track_B/press_0234.npy  \n", "  inflating: Dataset/train_track_B/press_0235.npy  \n", "  inflating: Dataset/train_track_B/press_0236.npy  \n", "  inflating: Dataset/train_track_B/press_0238.npy  \n", "  inflating: Dataset/train_track_B/press_0239.npy  \n", "  inflating: Dataset/train_track_B/press_0240.npy  \n", "  inflating: Dataset/train_track_B/press_0241.npy  \n", "  inflating: Dataset/train_track_B/press_0245.npy  \n", "  inflating: Dataset/train_track_B/press_0246.npy  \n", "  inflating: Dataset/train_track_B/press_0247.npy  \n", "  inflating: Dataset/train_track_B/press_0248.npy  \n", "  inflating: Dataset/train_track_B/press_0249.npy  \n", "  inflating: Dataset/train_track_B/press_0252.npy  \n", "  inflating: Dataset/train_track_B/press_0253.npy  \n", "  inflating: Dataset/train_track_B/press_0254.npy  \n", "  inflating: Dataset/train_track_B/press_0256.npy  \n", "  inflating: Dataset/train_track_B/press_0257.npy  \n", "  inflating: Dataset/train_track_B/press_0259.npy  \n", "  inflating: Dataset/train_track_B/press_0264.npy  \n", "  inflating: Dataset/train_track_B/press_0265.npy  \n", "  inflating: Dataset/train_track_B/press_0266.npy  \n", "  inflating: Dataset/train_track_B/press_0268.npy  \n", "  inflating: Dataset/train_track_B/press_0269.npy  \n", "  inflating: Dataset/train_track_B/press_0271.npy  \n", "  inflating: Dataset/train_track_B/press_0272.npy  \n", "  inflating: Dataset/train_track_B/press_0273.npy  \n", "  inflating: Dataset/train_track_B/press_0275.npy  \n", "  inflating: Dataset/train_track_B/press_0276.npy  \n", "  inflating: Dataset/train_track_B/press_0277.npy  \n", "  inflating: Dataset/train_track_B/press_0279.npy  \n", "  inflating: Dataset/train_track_B/press_0280.npy  \n", "  inflating: Dataset/train_track_B/press_0281.npy  \n", "  inflating: Dataset/train_track_B/press_0284.npy  \n", "  inflating: Dataset/train_track_B/press_0285.npy  \n", "  inflating: Dataset/train_track_B/press_0286.npy  \n", "  inflating: Dataset/train_track_B/press_0288.npy  \n", "  inflating: Dataset/train_track_B/press_0289.npy  \n", "  inflating: Dataset/train_track_B/press_0290.npy  \n", "  inflating: Dataset/train_track_B/press_0291.npy  \n", "  inflating: Dataset/train_track_B/press_0294.npy  \n", "  inflating: Dataset/train_track_B/press_0296.npy  \n", "  inflating: Dataset/train_track_B/press_0297.npy  \n", "  inflating: Dataset/train_track_B/press_0298.npy  \n", "  inflating: Dataset/train_track_B/press_0301.npy  \n", "  inflating: Dataset/train_track_B/press_0304.npy  \n", "  inflating: Dataset/train_track_B/press_0305.npy  \n", "  inflating: Dataset/train_track_B/press_0306.npy  \n", "  inflating: Dataset/train_track_B/press_0307.npy  \n", "  inflating: Dataset/train_track_B/press_0308.npy  \n", "  inflating: Dataset/train_track_B/press_0310.npy  \n", "  inflating: Dataset/train_track_B/press_0311.npy  \n", "  inflating: Dataset/train_track_B/press_0314.npy  \n", "  inflating: Dataset/train_track_B/press_0315.npy  \n", "  inflating: Dataset/train_track_B/press_0316.npy  \n", "  inflating: Dataset/train_track_B/press_0320.npy  \n", "  inflating: Dataset/train_track_B/press_0321.npy  \n", "  inflating: Dataset/train_track_B/press_0323.npy  \n", "  inflating: Dataset/train_track_B/press_0324.npy  \n", "  inflating: Dataset/train_track_B/press_0327.npy  \n", "  inflating: Dataset/train_track_B/press_0330.npy  \n", "  inflating: Dataset/train_track_B/press_0331.npy  \n", "  inflating: Dataset/train_track_B/press_0332.npy  \n", "  inflating: Dataset/train_track_B/press_0333.npy  \n", "  inflating: Dataset/train_track_B/press_0334.npy  \n", "  inflating: Dataset/train_track_B/press_0337.npy  \n", "  inflating: Dataset/train_track_B/press_0338.npy  \n", "  inflating: Dataset/train_track_B/press_0339.npy  \n", "  inflating: Dataset/train_track_B/press_0340.npy  \n", "  inflating: Dataset/train_track_B/press_0341.npy  \n", "  inflating: Dataset/train_track_B/press_0342.npy  \n", "  inflating: Dataset/train_track_B/press_0343.npy  \n", "  inflating: Dataset/train_track_B/press_0344.npy  \n", "  inflating: Dataset/train_track_B/press_0345.npy  \n", "  inflating: Dataset/train_track_B/press_0346.npy  \n", "  inflating: Dataset/train_track_B/press_0348.npy  \n", "  inflating: Dataset/train_track_B/press_0349.npy  \n", "  inflating: Dataset/train_track_B/press_0351.npy  \n", "  inflating: Dataset/train_track_B/press_0352.npy  \n", "  inflating: Dataset/train_track_B/press_0353.npy  \n", "  inflating: Dataset/train_track_B/press_0354.npy  \n", "  inflating: Dataset/train_track_B/press_0356.npy  \n", "  inflating: Dataset/train_track_B/press_0357.npy  \n", "  inflating: Dataset/train_track_B/press_0359.npy  \n", "  inflating: Dataset/train_track_B/press_0360.npy  \n", "  inflating: Dataset/train_track_B/press_0361.npy  \n", "  inflating: Dataset/train_track_B/press_0363.npy  \n", "  inflating: Dataset/train_track_B/press_0364.npy  \n", "  inflating: Dataset/train_track_B/press_0365.npy  \n", "  inflating: Dataset/train_track_B/press_0366.npy  \n", "  inflating: Dataset/train_track_B/press_0367.npy  \n", "  inflating: Dataset/train_track_B/press_0368.npy  \n", "  inflating: Dataset/train_track_B/press_0369.npy  \n", "  inflating: Dataset/train_track_B/press_0371.npy  \n", "  inflating: Dataset/train_track_B/press_0373.npy  \n", "  inflating: Dataset/train_track_B/press_0376.npy  \n", "  inflating: Dataset/train_track_B/press_0377.npy  \n", "  inflating: Dataset/train_track_B/press_0378.npy  \n", "  inflating: Dataset/train_track_B/press_0379.npy  \n", "  inflating: Dataset/train_track_B/press_0381.npy  \n", "  inflating: Dataset/train_track_B/press_0382.npy  \n", "  inflating: Dataset/train_track_B/press_0383.npy  \n", "  inflating: Dataset/train_track_B/press_0384.npy  \n", "  inflating: Dataset/train_track_B/press_0385.npy  \n", "  inflating: Dataset/train_track_B/press_0387.npy  \n", "  inflating: Dataset/train_track_B/press_0388.npy  \n", "  inflating: Dataset/train_track_B/press_0389.npy  \n", "  inflating: Dataset/train_track_B/press_0392.npy  \n", "  inflating: Dataset/train_track_B/press_0393.npy  \n", "  inflating: Dataset/train_track_B/press_0394.npy  \n", "  inflating: Dataset/train_track_B/press_0395.npy  \n", "  inflating: Dataset/train_track_B/press_0396.npy  \n", "  inflating: Dataset/train_track_B/press_0398.npy  \n", "  inflating: Dataset/train_track_B/press_0399.npy  \n", "  inflating: Dataset/train_track_B/press_0400.npy  \n", "  inflating: Dataset/train_track_B/press_0401.npy  \n", "  inflating: Dataset/train_track_B/press_0402.npy  \n", "  inflating: Dataset/train_track_B/press_0403.npy  \n", "  inflating: Dataset/train_track_B/press_0404.npy  \n", "  inflating: Dataset/train_track_B/press_0405.npy  \n", "  inflating: Dataset/train_track_B/press_0407.npy  \n", "  inflating: Dataset/train_track_B/press_0408.npy  \n", "  inflating: Dataset/train_track_B/press_0409.npy  \n", "  inflating: Dataset/train_track_B/press_0410.npy  \n", "  inflating: Dataset/train_track_B/press_0411.npy  \n", "  inflating: Dataset/train_track_B/press_0413.npy  \n", "  inflating: Dataset/train_track_B/press_0416.npy  \n", "  inflating: Dataset/train_track_B/press_0417.npy  \n", "  inflating: Dataset/train_track_B/press_0421.npy  \n", "  inflating: Dataset/train_track_B/press_0422.npy  \n", "  inflating: Dataset/train_track_B/press_0423.npy  \n", "  inflating: Dataset/train_track_B/press_0424.npy  \n", "  inflating: Dataset/train_track_B/press_0425.npy  \n", "  inflating: Dataset/train_track_B/press_0428.npy  \n", "  inflating: Dataset/train_track_B/press_0429.npy  \n", "  inflating: Dataset/train_track_B/press_0430.npy  \n", "  inflating: Dataset/train_track_B/press_0431.npy  \n", "  inflating: Dataset/train_track_B/press_0432.npy  \n", "  inflating: Dataset/train_track_B/press_0435.npy  \n", "  inflating: Dataset/train_track_B/press_0438.npy  \n", "  inflating: Dataset/train_track_B/press_0439.npy  \n", "  inflating: Dataset/train_track_B/press_0441.npy  \n", "  inflating: Dataset/train_track_B/press_0444.npy  \n", "  inflating: Dataset/train_track_B/press_0445.npy  \n", "  inflating: Dataset/train_track_B/press_0449.npy  \n", "  inflating: Dataset/train_track_B/press_0450.npy  \n", "  inflating: Dataset/train_track_B/press_0451.npy  \n", "  inflating: Dataset/train_track_B/press_0452.npy  \n", "  inflating: Dataset/train_track_B/press_0453.npy  \n", "  inflating: Dataset/train_track_B/press_0456.npy  \n", "  inflating: Dataset/train_track_B/press_0457.npy  \n", "  inflating: Dataset/train_track_B/press_0458.npy  \n", "  inflating: Dataset/train_track_B/press_0459.npy  \n", "  inflating: Dataset/train_track_B/press_0460.npy  \n", "  inflating: Dataset/train_track_B/press_0461.npy  \n", "  inflating: Dataset/train_track_B/press_0463.npy  \n", "  inflating: Dataset/train_track_B/press_0464.npy  \n", "  inflating: Dataset/train_track_B/press_0465.npy  \n", "  inflating: Dataset/train_track_B/press_0467.npy  \n", "  inflating: Dataset/train_track_B/press_0469.npy  \n", "  inflating: Dataset/train_track_B/press_0471.npy  \n", "  inflating: Dataset/train_track_B/press_0472.npy  \n", "  inflating: Dataset/train_track_B/press_0474.npy  \n", "  inflating: Dataset/train_track_B/press_0475.npy  \n", "  inflating: Dataset/train_track_B/press_0477.npy  \n", "  inflating: Dataset/train_track_B/press_0478.npy  \n", "  inflating: Dataset/train_track_B/press_0479.npy  \n", "  inflating: Dataset/train_track_B/press_0480.npy  \n", "  inflating: Dataset/train_track_B/press_0481.npy  \n", "  inflating: Dataset/train_track_B/press_0482.npy  \n", "  inflating: Dataset/train_track_B/press_0485.npy  \n", "  inflating: Dataset/train_track_B/press_0486.npy  \n", "  inflating: Dataset/train_track_B/press_0487.npy  \n", "  inflating: Dataset/train_track_B/press_0488.npy  \n", "  inflating: Dataset/train_track_B/press_0489.npy  \n", "  inflating: Dataset/train_track_B/press_0492.npy  \n", "  inflating: Dataset/train_track_B/press_0493.npy  \n", "  inflating: Dataset/train_track_B/press_0494.npy  \n", "  inflating: Dataset/train_track_B/press_0497.npy  \n", "  inflating: Dataset/train_track_B/press_0498.npy  \n", "  inflating: Dataset/train_track_B/press_0499.npy  \n", "  inflating: Dataset/train_track_B/press_0501.npy  \n", "  inflating: Dataset/train_track_B/press_0502.npy  \n", "  inflating: Dataset/train_track_B/press_0503.npy  \n", "  inflating: Dataset/train_track_B/press_0504.npy  \n", "  inflating: Dataset/train_track_B/press_0507.npy  \n", "  inflating: Dataset/train_track_B/press_0508.npy  \n", "  inflating: Dataset/train_track_B/press_0509.npy  \n", "  inflating: Dataset/train_track_B/press_0513.npy  \n", "  inflating: Dataset/train_track_B/press_0514.npy  \n", "  inflating: Dataset/train_track_B/press_0515.npy  \n", "  inflating: Dataset/train_track_B/press_0517.npy  \n", "  inflating: Dataset/train_track_B/press_0518.npy  \n", "  inflating: Dataset/train_track_B/press_0519.npy  \n", "  inflating: Dataset/train_track_B/press_0520.npy  \n", "  inflating: Dataset/train_track_B/press_0521.npy  \n", "  inflating: Dataset/train_track_B/press_0522.npy  \n", "  inflating: Dataset/train_track_B/press_0523.npy  \n", "  inflating: Dataset/train_track_B/press_0524.npy  \n", "  inflating: Dataset/train_track_B/press_0525.npy  \n", "  inflating: Dataset/train_track_B/press_0526.npy  \n", "  inflating: Dataset/train_track_B/press_0527.npy  \n", "  inflating: Dataset/train_track_B/press_0528.npy  \n", "  inflating: Dataset/train_track_B/press_0529.npy  \n", "  inflating: Dataset/train_track_B/press_0530.npy  \n", "  inflating: Dataset/train_track_B/press_0531.npy  \n", "  inflating: Dataset/train_track_B/press_0534.npy  \n", "  inflating: Dataset/train_track_B/press_0535.npy  \n", "  inflating: Dataset/train_track_B/press_0536.npy  \n", "  inflating: Dataset/train_track_B/press_0538.npy  \n", "  inflating: Dataset/train_track_B/press_0541.npy  \n", "  inflating: Dataset/train_track_B/press_0542.npy  \n", "  inflating: Dataset/train_track_B/press_0544.npy  \n", "  inflating: Dataset/train_track_B/press_0545.npy  \n", "  inflating: Dataset/train_track_B/press_0546.npy  \n", "  inflating: Dataset/train_track_B/press_0547.npy  \n", "  inflating: Dataset/train_track_B/press_0550.npy  \n", "  inflating: Dataset/train_track_B/press_0551.npy  \n", "  inflating: Dataset/train_track_B/press_0553.npy  \n", "  inflating: Dataset/train_track_B/press_0555.npy  \n", "  inflating: Dataset/train_track_B/press_0557.npy  \n", "  inflating: Dataset/train_track_B/press_0558.npy  \n", "  inflating: Dataset/train_track_B/press_0561.npy  \n", "  inflating: Dataset/train_track_B/press_0563.npy  \n", "  inflating: Dataset/train_track_B/press_0564.npy  \n", "  inflating: Dataset/train_track_B/press_0565.npy  \n", "  inflating: Dataset/train_track_B/press_0567.npy  \n", "  inflating: Dataset/train_track_B/press_0568.npy  \n", "  inflating: Dataset/train_track_B/press_0571.npy  \n", "  inflating: Dataset/train_track_B/press_0574.npy  \n", "  inflating: Dataset/train_track_B/press_0576.npy  \n", "  inflating: Dataset/train_track_B/press_0579.npy  \n", "  inflating: Dataset/train_track_B/press_0580.npy  \n", "  inflating: Dataset/train_track_B/press_0582.npy  \n", "  inflating: Dataset/train_track_B/press_0584.npy  \n", "  inflating: Dataset/train_track_B/press_0585.npy  \n", "  inflating: Dataset/train_track_B/press_0588.npy  \n", "  inflating: Dataset/train_track_B/press_0589.npy  \n", "  inflating: Dataset/train_track_B/press_0590.npy  \n", "  inflating: Dataset/train_track_B/press_0591.npy  \n", "  inflating: Dataset/train_track_B/press_0592.npy  \n", "  inflating: Dataset/train_track_B/press_0593.npy  \n", "  inflating: Dataset/train_track_B/press_0594.npy  \n", "  inflating: Dataset/train_track_B/press_0595.npy  \n", "  inflating: Dataset/train_track_B/press_0596.npy  \n", "  inflating: Dataset/train_track_B/press_0597.npy  \n", "  inflating: Dataset/train_track_B/press_0598.npy  \n", "  inflating: Dataset/train_track_B/press_0600.npy  \n", "  inflating: Dataset/train_track_B/press_0602.npy  \n", "  inflating: Dataset/train_track_B/press_0605.npy  \n", "  inflating: Dataset/train_track_B/press_0608.npy  \n", "  inflating: Dataset/train_track_B/press_0609.npy  \n", "  inflating: Dataset/train_track_B/press_0611.npy  \n", "  inflating: Dataset/train_track_B/press_0612.npy  \n", "  inflating: Dataset/train_track_B/press_0613.npy  \n", "  inflating: Dataset/train_track_B/press_0614.npy  \n", "  inflating: Dataset/train_track_B/press_0618.npy  \n", "  inflating: Dataset/train_track_B/press_0619.npy  \n", "  inflating: Dataset/train_track_B/press_0620.npy  \n", "  inflating: Dataset/train_track_B/press_0621.npy  \n", "  inflating: Dataset/train_track_B/press_0622.npy  \n", "  inflating: Dataset/train_track_B/press_0623.npy  \n", "  inflating: Dataset/train_track_B/press_0624.npy  \n", "  inflating: Dataset/train_track_B/press_0625.npy  \n", "  inflating: Dataset/train_track_B/press_0627.npy  \n", "  inflating: Dataset/train_track_B/press_0628.npy  \n", "  inflating: Dataset/train_track_B/press_0629.npy  \n", "  inflating: Dataset/train_track_B/press_0630.npy  \n", "  inflating: Dataset/train_track_B/press_0631.npy  \n", "  inflating: Dataset/train_track_B/press_0632.npy  \n", "  inflating: Dataset/train_track_B/press_0633.npy  \n", "  inflating: Dataset/train_track_B/press_0634.npy  \n", "  inflating: Dataset/train_track_B/press_0635.npy  \n", "  inflating: Dataset/train_track_B/press_0637.npy  \n", "  inflating: Dataset/train_track_B/press_0638.npy  \n", "  inflating: Dataset/train_track_B/press_0639.npy  \n", "  inflating: Dataset/train_track_B/press_0640.npy  \n", "  inflating: Dataset/train_track_B/press_0641.npy  \n", "  inflating: Dataset/train_track_B/press_0643.npy  \n", "  inflating: Dataset/train_track_B/press_0644.npy  \n", "  inflating: Dataset/train_track_B/press_0645.npy  \n", "  inflating: Dataset/train_track_B/press_0646.npy  \n", "  inflating: Dataset/train_track_B/press_0648.npy  \n", "  inflating: Dataset/train_track_B/press_0650.npy  \n", "  inflating: Dataset/train_track_B/press_0651.npy  \n", "  inflating: Dataset/train_track_B/press_0652.npy  \n", "  inflating: Dataset/train_track_B/press_0653.npy  \n", "  inflating: Dataset/train_track_B/press_0654.npy  \n", "  inflating: Dataset/train_track_B/press_0656.npy  \n", "  inflating: Dataset/train_track_B/press_0657.npy  \n", "  inflating: Dataset/train_track_B/press_0658.npy  \n", "  inflating: Dataset/train_track_B/press_0661.npy  \n", "  inflating: Dataset/train_track_B/press_0663.npy  \n", "  inflating: Dataset/train_track_B/press_0664.npy  \n", "  inflating: Dataset/train_track_B/press_0665.npy  \n", "  inflating: Dataset/train_track_B/press_0666.npy  \n", "  inflating: Dataset/train_track_B/press_0667.npy  \n", "  inflating: Dataset/train_track_B/press_0668.npy  \n", "  inflating: Dataset/train_track_B/press_0669.npy  \n", "  inflating: Dataset/train_track_B/press_0671.npy  \n", "  inflating: Dataset/train_track_B/press_0672.npy  \n", "  inflating: Dataset/train_track_B/press_0673.npy  \n", "  inflating: Dataset/train_track_B/press_0674.npy  \n", "  inflating: Dataset/train_track_B/press_0676.npy  \n", "  inflating: Dataset/train_track_B/press_0677.npy  \n", "  inflating: Dataset/train_track_B/press_0678.npy  \n", "  inflating: Dataset/train_track_B/press_0679.npy  \n", "  inflating: Dataset/train_track_B/press_0680.npy  \n", "  inflating: Dataset/train_track_B/press_0682.npy  \n", "  inflating: Dataset/train_track_B/press_0686.npy  \n", "  inflating: Dataset/train_track_B/press_0688.npy  \n", "  inflating: Dataset/train_track_B/press_0689.npy  \n", "  inflating: Dataset/train_track_B/press_0690.npy  \n", "  inflating: Dataset/train_track_B/press_0691.npy  \n", "  inflating: Dataset/train_track_B/press_0692.npy  \n", "  inflating: Dataset/train_track_B/press_0693.npy  \n", "  inflating: Dataset/train_track_B/press_0694.npy  \n", "  inflating: Dataset/train_track_B/press_0695.npy  \n", "  inflating: Dataset/train_track_B/press_0697.npy  \n", "  inflating: Dataset/train_track_B/press_0699.npy  \n", "  inflating: Dataset/train_track_B/press_0700.npy  \n", "  inflating: Dataset/train_track_B/press_0701.npy  \n", "  inflating: Dataset/train_track_B/press_0703.npy  \n", "  inflating: Dataset/train_track_B/press_0704.npy  \n", "  inflating: Dataset/train_track_B/press_0706.npy  \n", "  inflating: Dataset/train_track_B/press_0707.npy  \n", "  inflating: Dataset/train_track_B/press_0708.npy  \n", "  inflating: Dataset/train_track_B/press_0709.npy  \n", "  inflating: Dataset/train_track_B/press_0711.npy  \n", "  inflating: Dataset/train_track_B/press_0712.npy  \n", "  inflating: Dataset/train_track_B/press_0713.npy  \n", "  inflating: Dataset/train_track_B/press_0714.npy  \n", "  inflating: Dataset/train_track_B/press_0715.npy  \n", "  inflating: Dataset/train_track_B/press_0716.npy  \n", "  inflating: Dataset/train_track_B/press_0718.npy  \n", "  inflating: Dataset/train_track_B/press_0719.npy  \n", "  inflating: Dataset/train_track_B/press_0720.npy  \n", "  inflating: Dataset/train_track_B/press_0721.npy  \n", "  inflating: Dataset/train_track_B/press_0722.npy  \n", "  inflating: Dataset/train_track_B/press_0724.npy  \n", "  inflating: Dataset/train_track_B/press_0727.npy  \n", "  inflating: Dataset/train_track_B/press_0728.npy  \n", "  inflating: Dataset/train_track_B/press_0729.npy  \n", "  inflating: Dataset/train_track_B/press_0730.npy  \n", "  inflating: Dataset/train_track_B/press_0731.npy  \n", "  inflating: Dataset/train_track_B/press_0733.npy  \n", "  inflating: Dataset/train_track_B/press_0735.npy  \n", "  inflating: Dataset/train_track_B/press_0736.npy  \n", "  inflating: Dataset/train_track_B/press_0737.npy  \n", "  inflating: Dataset/train_track_B/press_0740.npy  \n", "  inflating: Dataset/train_track_B/press_0742.npy  \n", "  inflating: Dataset/train_track_B/press_0743.npy  \n", "  inflating: Dataset/train_track_B/press_0744.npy  \n", "  inflating: Dataset/train_track_B/press_0745.npy  \n"]}], "source": ["!wget --header=\"Host: ai-studio-online.bj.bcebos.com\" --header=\"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\" --header=\"Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\" --header=\"Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6\" --header=\"Referer: https://aistudio.baidu.com/\" \"https://ai-studio-online.bj.bcebos.com/v1/38e9adf0fce84527aad3558cc3e82d0e9a251aac4c934297afae9b74d9b3d1e9?responseContentDisposition=attachment%3B%20filename%3Dtrain_track_B.zip&authorization=bce-auth-v1%2F5cfe9a5e1454405eb2a975c43eace6ec%2F2024-06-04T03%3A21%3A02Z%2F-1%2F%2Facd359add161bace603a52c7a268467406cb3c1889a7114bbb687de8002b55f6\" -c -O 'train_track_B.zip'\n", "!mkdir -p Dataset/train_track_B && unzip -o train_track_B.zip -d Dataset/train_track_B/\n"]}], "metadata": {"colab": {"authorship_tag": "ABX9TyNUH9p5eYZb0k/VZfG/jPGH", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}