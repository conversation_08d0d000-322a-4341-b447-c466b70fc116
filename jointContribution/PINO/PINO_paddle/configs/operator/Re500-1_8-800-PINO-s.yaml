data:
  name: KF
  paths: ['/raid/hongkai/NS-Re500_T300_id0-shuffle.npy']
  Re: 500
  offset: 0
  total_num: 300
  raw_res: [256, 256, 513]
  n_data_samples: 100
  data_res: [64, 64, 257]  # resolution in 1 second
  pde_res: [256, 256, 513]   # resolution in 1 second
  a_offset: 0
  n_a_samples: 275
  testoffset: 275
  n_test_samples: 25
  t_duration: 0.125
  shuffle: True

model:
  layers: [64, 64, 64, 64, 64]
  modes1: [12, 12, 12, 12]
  modes2: [12, 12, 12, 12]
  modes3: [12, 12, 12, 12]
  fc_dim: 128
  act: gelu
  pad_ratio: [0.0, 0.125]

train:
  batchsize: 2
  start_iter: 0
  num_iter: 200_001
  milestones: [20_000, 60_000, 120_000]
  base_lr: 0.001
  scheduler_gamma: 0.5
  ic_loss: 10.0
  f_loss: 1.0
  xy_loss: 10.0
  save_step: 5000
  eval_step: 5000

test:
  batchsize: 1
  data_res: [256, 256, 513]

log:
  logdir: Re500-1_8s-800-PINO-s
  entity: hzzheng-pino
  project: PINO-KF-Re500
  group: Re500-1_8s-800-PINO-s
