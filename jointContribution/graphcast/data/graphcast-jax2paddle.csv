params:grid2mesh_gnn/~_networks_builder/encoder_edges_grid2mesh_layer_norm:offset,graphcast.encoder.embedding.grid2mesh_edge_embedding.layer_norm.bias
params:grid2mesh_gnn/~_networks_builder/encoder_edges_grid2mesh_layer_norm:scale,graphcast.encoder.embedding.grid2mesh_edge_embedding.layer_norm.weight
params:grid2mesh_gnn/~_networks_builder/encoder_edges_grid2mesh_mlp/~/linear_0:b,graphcast.encoder.embedding.grid2mesh_edge_embedding.mlp.0.bias
params:grid2mesh_gnn/~_networks_builder/encoder_edges_grid2mesh_mlp/~/linear_0:w,graphcast.encoder.embedding.grid2mesh_edge_embedding.mlp.0.weight
params:grid2mesh_gnn/~_networks_builder/encoder_edges_grid2mesh_mlp/~/linear_1:b,graphcast.encoder.embedding.grid2mesh_edge_embedding.mlp.2.bias
params:grid2mesh_gnn/~_networks_builder/encoder_edges_grid2mesh_mlp/~/linear_1:w,graphcast.encoder.embedding.grid2mesh_edge_embedding.mlp.2.weight
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_grid_nodes_layer_norm:offset,graphcast.encoder.embedding.grid_node_embedding.layer_norm.bias
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_grid_nodes_layer_norm:scale,graphcast.encoder.embedding.grid_node_embedding.layer_norm.weight
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_grid_nodes_mlp/~/linear_0:b,graphcast.encoder.embedding.grid_node_embedding.mlp.0.bias
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_grid_nodes_mlp/~/linear_0:w,graphcast.encoder.embedding.grid_node_embedding.mlp.0.weight
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_grid_nodes_mlp/~/linear_1:b,graphcast.encoder.embedding.grid_node_embedding.mlp.2.bias
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_grid_nodes_mlp/~/linear_1:w,graphcast.encoder.embedding.grid_node_embedding.mlp.2.weight
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_mesh_nodes_layer_norm:offset,graphcast.encoder.embedding.mesh_node_embedding.layer_norm.bias
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_mesh_nodes_layer_norm:scale,graphcast.encoder.embedding.mesh_node_embedding.layer_norm.weight
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_mesh_nodes_mlp/~/linear_0:b,graphcast.encoder.embedding.mesh_node_embedding.mlp.0.bias
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_mesh_nodes_mlp/~/linear_0:w,graphcast.encoder.embedding.mesh_node_embedding.mlp.0.weight
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_mesh_nodes_mlp/~/linear_1:b,graphcast.encoder.embedding.mesh_node_embedding.mlp.2.bias
params:grid2mesh_gnn/~_networks_builder/encoder_nodes_mesh_nodes_mlp/~/linear_1:w,graphcast.encoder.embedding.mesh_node_embedding.mlp.2.weight
params:grid2mesh_gnn/~_networks_builder/processor_edges_0_grid2mesh_layer_norm:offset,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.edge_layer.layer_norm.bias
params:grid2mesh_gnn/~_networks_builder/processor_edges_0_grid2mesh_layer_norm:scale,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.edge_layer.layer_norm.weight
params:grid2mesh_gnn/~_networks_builder/processor_edges_0_grid2mesh_mlp/~/linear_0:b,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.edge_layer.mlp.0.bias
params:grid2mesh_gnn/~_networks_builder/processor_edges_0_grid2mesh_mlp/~/linear_0:w,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.edge_layer.mlp.0.weight
params:grid2mesh_gnn/~_networks_builder/processor_edges_0_grid2mesh_mlp/~/linear_1:b,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.edge_layer.mlp.2.bias
params:grid2mesh_gnn/~_networks_builder/processor_edges_0_grid2mesh_mlp/~/linear_1:w,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.edge_layer.mlp.2.weight
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_grid_nodes_layer_norm:offset,graphcast.encoder.grid2mesh_gnn.grid_node_layer.fn.layer_norm.bias
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_grid_nodes_layer_norm:scale,graphcast.encoder.grid2mesh_gnn.grid_node_layer.fn.layer_norm.weight
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_grid_nodes_mlp/~/linear_0:b,graphcast.encoder.grid2mesh_gnn.grid_node_layer.fn.mlp.0.bias
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_grid_nodes_mlp/~/linear_0:w,graphcast.encoder.grid2mesh_gnn.grid_node_layer.fn.mlp.0.weight
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_grid_nodes_mlp/~/linear_1:b,graphcast.encoder.grid2mesh_gnn.grid_node_layer.fn.mlp.2.bias
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_grid_nodes_mlp/~/linear_1:w,graphcast.encoder.grid2mesh_gnn.grid_node_layer.fn.mlp.2.weight
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_layer_norm:offset,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.node_layer.layer_norm.bias
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_layer_norm:scale,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.node_layer.layer_norm.weight
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_0:b,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.node_layer.mlp.0.bias
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_0:w,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.node_layer.mlp.0.weight
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_1:b,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.node_layer.mlp.2.bias
params:grid2mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_1:w,graphcast.encoder.grid2mesh_gnn.grid2mesh_gnn.node_layer.mlp.2.weight
params:mesh2grid_gnn/~_networks_builder/decoder_nodes_grid_nodes_mlp/~/linear_0:b,graphcast.decoder.grid_node_layer.mlp.0.bias
params:mesh2grid_gnn/~_networks_builder/decoder_nodes_grid_nodes_mlp/~/linear_0:w,graphcast.decoder.grid_node_layer.mlp.0.weight
params:mesh2grid_gnn/~_networks_builder/decoder_nodes_grid_nodes_mlp/~/linear_1:b,graphcast.decoder.grid_node_layer.mlp.2.bias
params:mesh2grid_gnn/~_networks_builder/decoder_nodes_grid_nodes_mlp/~/linear_1:w,graphcast.decoder.grid_node_layer.mlp.2.weight
params:mesh2grid_gnn/~_networks_builder/encoder_edges_mesh2grid_layer_norm:offset,graphcast.encoder.embedding.mesh2grid_edge_embedding.layer_norm.bias
params:mesh2grid_gnn/~_networks_builder/encoder_edges_mesh2grid_layer_norm:scale,graphcast.encoder.embedding.mesh2grid_edge_embedding.layer_norm.weight
params:mesh2grid_gnn/~_networks_builder/encoder_edges_mesh2grid_mlp/~/linear_0:b,graphcast.encoder.embedding.mesh2grid_edge_embedding.mlp.0.bias
params:mesh2grid_gnn/~_networks_builder/encoder_edges_mesh2grid_mlp/~/linear_0:w,graphcast.encoder.embedding.mesh2grid_edge_embedding.mlp.0.weight
params:mesh2grid_gnn/~_networks_builder/encoder_edges_mesh2grid_mlp/~/linear_1:b,graphcast.encoder.embedding.mesh2grid_edge_embedding.mlp.2.bias
params:mesh2grid_gnn/~_networks_builder/encoder_edges_mesh2grid_mlp/~/linear_1:w,graphcast.encoder.embedding.mesh2grid_edge_embedding.mlp.2.weight
params:mesh2grid_gnn/~_networks_builder/processor_edges_0_mesh2grid_layer_norm:offset,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.edge_layer.layer_norm.bias
params:mesh2grid_gnn/~_networks_builder/processor_edges_0_mesh2grid_layer_norm:scale,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.edge_layer.layer_norm.weight
params:mesh2grid_gnn/~_networks_builder/processor_edges_0_mesh2grid_mlp/~/linear_0:b,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.edge_layer.mlp.0.bias
params:mesh2grid_gnn/~_networks_builder/processor_edges_0_mesh2grid_mlp/~/linear_0:w,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.edge_layer.mlp.0.weight
params:mesh2grid_gnn/~_networks_builder/processor_edges_0_mesh2grid_mlp/~/linear_1:b,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.edge_layer.mlp.2.bias
params:mesh2grid_gnn/~_networks_builder/processor_edges_0_mesh2grid_mlp/~/linear_1:w,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.edge_layer.mlp.2.weight
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_grid_nodes_layer_norm:offset,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.node_layer.layer_norm.bias
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_grid_nodes_layer_norm:scale,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.node_layer.layer_norm.weight
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_grid_nodes_mlp/~/linear_0:b,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.node_layer.mlp.0.bias
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_grid_nodes_mlp/~/linear_0:w,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.node_layer.mlp.0.weight
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_grid_nodes_mlp/~/linear_1:b,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.node_layer.mlp.2.bias
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_grid_nodes_mlp/~/linear_1:w,graphcast.decoder.mesh2grid_gnn.mesh2grid_gnn.node_layer.mlp.2.weight
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_layer_norm:offset,graphcast.decoder.mesh2grid_gnn.mesh_node_layer.fn.layer_norm.bias
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_layer_norm:scale,graphcast.decoder.mesh2grid_gnn.mesh_node_layer.fn.layer_norm.weight
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_0:b,graphcast.decoder.mesh2grid_gnn.mesh_node_layer.fn.mlp.0.bias
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_0:w,graphcast.decoder.mesh2grid_gnn.mesh_node_layer.fn.mlp.0.weight
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_1:b,graphcast.decoder.mesh2grid_gnn.mesh_node_layer.fn.mlp.2.bias
params:mesh2grid_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_1:w,graphcast.decoder.mesh2grid_gnn.mesh_node_layer.fn.mlp.2.weight
params:mesh_gnn/~_networks_builder/encoder_edges_mesh_layer_norm:offset,graphcast.encoder.embedding.mesh_edge_embedding.layer_norm.bias
params:mesh_gnn/~_networks_builder/encoder_edges_mesh_layer_norm:scale,graphcast.encoder.embedding.mesh_edge_embedding.layer_norm.weight
params:mesh_gnn/~_networks_builder/encoder_edges_mesh_mlp/~/linear_0:b,graphcast.encoder.embedding.mesh_edge_embedding.mlp.0.bias
params:mesh_gnn/~_networks_builder/encoder_edges_mesh_mlp/~/linear_0:w,graphcast.encoder.embedding.mesh_edge_embedding.mlp.0.weight
params:mesh_gnn/~_networks_builder/encoder_edges_mesh_mlp/~/linear_1:b,graphcast.encoder.embedding.mesh_edge_embedding.mlp.2.bias
params:mesh_gnn/~_networks_builder/encoder_edges_mesh_mlp/~/linear_1:w,graphcast.encoder.embedding.mesh_edge_embedding.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_0_mesh_layer_norm:offset,graphcast.processor.processor.0.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_0_mesh_layer_norm:scale,graphcast.processor.processor.0.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_0_mesh_mlp/~/linear_0:b,graphcast.processor.processor.0.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_0_mesh_mlp/~/linear_0:w,graphcast.processor.processor.0.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_0_mesh_mlp/~/linear_1:b,graphcast.processor.processor.0.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_0_mesh_mlp/~/linear_1:w,graphcast.processor.processor.0.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_1_mesh_layer_norm:offset,graphcast.processor.processor.1.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_1_mesh_layer_norm:scale,graphcast.processor.processor.1.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_1_mesh_mlp/~/linear_0:b,graphcast.processor.processor.1.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_1_mesh_mlp/~/linear_0:w,graphcast.processor.processor.1.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_1_mesh_mlp/~/linear_1:b,graphcast.processor.processor.1.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_1_mesh_mlp/~/linear_1:w,graphcast.processor.processor.1.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_2_mesh_layer_norm:offset,graphcast.processor.processor.2.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_2_mesh_layer_norm:scale,graphcast.processor.processor.2.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_2_mesh_mlp/~/linear_0:b,graphcast.processor.processor.2.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_2_mesh_mlp/~/linear_0:w,graphcast.processor.processor.2.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_2_mesh_mlp/~/linear_1:b,graphcast.processor.processor.2.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_2_mesh_mlp/~/linear_1:w,graphcast.processor.processor.2.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_3_mesh_layer_norm:offset,graphcast.processor.processor.3.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_3_mesh_layer_norm:scale,graphcast.processor.processor.3.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_3_mesh_mlp/~/linear_0:b,graphcast.processor.processor.3.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_3_mesh_mlp/~/linear_0:w,graphcast.processor.processor.3.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_3_mesh_mlp/~/linear_1:b,graphcast.processor.processor.3.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_3_mesh_mlp/~/linear_1:w,graphcast.processor.processor.3.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_4_mesh_layer_norm:offset,graphcast.processor.processor.4.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_4_mesh_layer_norm:scale,graphcast.processor.processor.4.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_4_mesh_mlp/~/linear_0:b,graphcast.processor.processor.4.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_4_mesh_mlp/~/linear_0:w,graphcast.processor.processor.4.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_4_mesh_mlp/~/linear_1:b,graphcast.processor.processor.4.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_4_mesh_mlp/~/linear_1:w,graphcast.processor.processor.4.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_5_mesh_layer_norm:offset,graphcast.processor.processor.5.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_5_mesh_layer_norm:scale,graphcast.processor.processor.5.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_5_mesh_mlp/~/linear_0:b,graphcast.processor.processor.5.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_5_mesh_mlp/~/linear_0:w,graphcast.processor.processor.5.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_5_mesh_mlp/~/linear_1:b,graphcast.processor.processor.5.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_5_mesh_mlp/~/linear_1:w,graphcast.processor.processor.5.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_6_mesh_layer_norm:offset,graphcast.processor.processor.6.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_6_mesh_layer_norm:scale,graphcast.processor.processor.6.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_6_mesh_mlp/~/linear_0:b,graphcast.processor.processor.6.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_6_mesh_mlp/~/linear_0:w,graphcast.processor.processor.6.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_6_mesh_mlp/~/linear_1:b,graphcast.processor.processor.6.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_6_mesh_mlp/~/linear_1:w,graphcast.processor.processor.6.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_7_mesh_layer_norm:offset,graphcast.processor.processor.7.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_7_mesh_layer_norm:scale,graphcast.processor.processor.7.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_7_mesh_mlp/~/linear_0:b,graphcast.processor.processor.7.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_7_mesh_mlp/~/linear_0:w,graphcast.processor.processor.7.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_7_mesh_mlp/~/linear_1:b,graphcast.processor.processor.7.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_7_mesh_mlp/~/linear_1:w,graphcast.processor.processor.7.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_8_mesh_layer_norm:offset,graphcast.processor.processor.8.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_8_mesh_layer_norm:scale,graphcast.processor.processor.8.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_8_mesh_mlp/~/linear_0:b,graphcast.processor.processor.8.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_8_mesh_mlp/~/linear_0:w,graphcast.processor.processor.8.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_8_mesh_mlp/~/linear_1:b,graphcast.processor.processor.8.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_8_mesh_mlp/~/linear_1:w,graphcast.processor.processor.8.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_9_mesh_layer_norm:offset,graphcast.processor.processor.9.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_9_mesh_layer_norm:scale,graphcast.processor.processor.9.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_9_mesh_mlp/~/linear_0:b,graphcast.processor.processor.9.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_9_mesh_mlp/~/linear_0:w,graphcast.processor.processor.9.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_9_mesh_mlp/~/linear_1:b,graphcast.processor.processor.9.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_9_mesh_mlp/~/linear_1:w,graphcast.processor.processor.9.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_10_mesh_layer_norm:offset,graphcast.processor.processor.10.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_10_mesh_layer_norm:scale,graphcast.processor.processor.10.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_10_mesh_mlp/~/linear_0:b,graphcast.processor.processor.10.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_10_mesh_mlp/~/linear_0:w,graphcast.processor.processor.10.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_10_mesh_mlp/~/linear_1:b,graphcast.processor.processor.10.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_10_mesh_mlp/~/linear_1:w,graphcast.processor.processor.10.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_11_mesh_layer_norm:offset,graphcast.processor.processor.11.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_11_mesh_layer_norm:scale,graphcast.processor.processor.11.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_11_mesh_mlp/~/linear_0:b,graphcast.processor.processor.11.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_11_mesh_mlp/~/linear_0:w,graphcast.processor.processor.11.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_11_mesh_mlp/~/linear_1:b,graphcast.processor.processor.11.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_11_mesh_mlp/~/linear_1:w,graphcast.processor.processor.11.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_12_mesh_layer_norm:offset,graphcast.processor.processor.12.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_12_mesh_layer_norm:scale,graphcast.processor.processor.12.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_12_mesh_mlp/~/linear_0:b,graphcast.processor.processor.12.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_12_mesh_mlp/~/linear_0:w,graphcast.processor.processor.12.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_12_mesh_mlp/~/linear_1:b,graphcast.processor.processor.12.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_12_mesh_mlp/~/linear_1:w,graphcast.processor.processor.12.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_13_mesh_layer_norm:offset,graphcast.processor.processor.13.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_13_mesh_layer_norm:scale,graphcast.processor.processor.13.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_13_mesh_mlp/~/linear_0:b,graphcast.processor.processor.13.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_13_mesh_mlp/~/linear_0:w,graphcast.processor.processor.13.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_13_mesh_mlp/~/linear_1:b,graphcast.processor.processor.13.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_13_mesh_mlp/~/linear_1:w,graphcast.processor.processor.13.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_14_mesh_layer_norm:offset,graphcast.processor.processor.14.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_14_mesh_layer_norm:scale,graphcast.processor.processor.14.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_14_mesh_mlp/~/linear_0:b,graphcast.processor.processor.14.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_14_mesh_mlp/~/linear_0:w,graphcast.processor.processor.14.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_14_mesh_mlp/~/linear_1:b,graphcast.processor.processor.14.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_14_mesh_mlp/~/linear_1:w,graphcast.processor.processor.14.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_edges_15_mesh_layer_norm:offset,graphcast.processor.processor.15.edge_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_edges_15_mesh_layer_norm:scale,graphcast.processor.processor.15.edge_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_edges_15_mesh_mlp/~/linear_0:b,graphcast.processor.processor.15.edge_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_edges_15_mesh_mlp/~/linear_0:w,graphcast.processor.processor.15.edge_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_edges_15_mesh_mlp/~/linear_1:b,graphcast.processor.processor.15.edge_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_edges_15_mesh_mlp/~/linear_1:w,graphcast.processor.processor.15.edge_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_layer_norm:offset,graphcast.processor.processor.0.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_layer_norm:scale,graphcast.processor.processor.0.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.0.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.0.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.0.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_0_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.0.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_1_mesh_nodes_layer_norm:offset,graphcast.processor.processor.1.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_1_mesh_nodes_layer_norm:scale,graphcast.processor.processor.1.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_1_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.1.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_1_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.1.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_1_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.1.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_1_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.1.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_2_mesh_nodes_layer_norm:offset,graphcast.processor.processor.2.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_2_mesh_nodes_layer_norm:scale,graphcast.processor.processor.2.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_2_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.2.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_2_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.2.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_2_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.2.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_2_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.2.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_3_mesh_nodes_layer_norm:offset,graphcast.processor.processor.3.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_3_mesh_nodes_layer_norm:scale,graphcast.processor.processor.3.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_3_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.3.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_3_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.3.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_3_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.3.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_3_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.3.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_4_mesh_nodes_layer_norm:offset,graphcast.processor.processor.4.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_4_mesh_nodes_layer_norm:scale,graphcast.processor.processor.4.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_4_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.4.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_4_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.4.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_4_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.4.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_4_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.4.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_5_mesh_nodes_layer_norm:offset,graphcast.processor.processor.5.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_5_mesh_nodes_layer_norm:scale,graphcast.processor.processor.5.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_5_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.5.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_5_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.5.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_5_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.5.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_5_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.5.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_6_mesh_nodes_layer_norm:offset,graphcast.processor.processor.6.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_6_mesh_nodes_layer_norm:scale,graphcast.processor.processor.6.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_6_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.6.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_6_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.6.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_6_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.6.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_6_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.6.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_7_mesh_nodes_layer_norm:offset,graphcast.processor.processor.7.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_7_mesh_nodes_layer_norm:scale,graphcast.processor.processor.7.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_7_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.7.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_7_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.7.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_7_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.7.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_7_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.7.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_8_mesh_nodes_layer_norm:offset,graphcast.processor.processor.8.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_8_mesh_nodes_layer_norm:scale,graphcast.processor.processor.8.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_8_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.8.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_8_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.8.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_8_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.8.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_8_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.8.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_9_mesh_nodes_layer_norm:offset,graphcast.processor.processor.9.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_9_mesh_nodes_layer_norm:scale,graphcast.processor.processor.9.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_9_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.9.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_9_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.9.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_9_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.9.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_9_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.9.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_10_mesh_nodes_layer_norm:offset,graphcast.processor.processor.10.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_10_mesh_nodes_layer_norm:scale,graphcast.processor.processor.10.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_10_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.10.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_10_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.10.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_10_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.10.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_10_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.10.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_11_mesh_nodes_layer_norm:offset,graphcast.processor.processor.11.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_11_mesh_nodes_layer_norm:scale,graphcast.processor.processor.11.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_11_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.11.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_11_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.11.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_11_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.11.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_11_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.11.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_12_mesh_nodes_layer_norm:offset,graphcast.processor.processor.12.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_12_mesh_nodes_layer_norm:scale,graphcast.processor.processor.12.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_12_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.12.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_12_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.12.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_12_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.12.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_12_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.12.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_13_mesh_nodes_layer_norm:offset,graphcast.processor.processor.13.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_13_mesh_nodes_layer_norm:scale,graphcast.processor.processor.13.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_13_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.13.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_13_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.13.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_13_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.13.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_13_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.13.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_14_mesh_nodes_layer_norm:offset,graphcast.processor.processor.14.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_14_mesh_nodes_layer_norm:scale,graphcast.processor.processor.14.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_14_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.14.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_14_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.14.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_14_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.14.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_14_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.14.node_layer.mlp.2.weight
params:mesh_gnn/~_networks_builder/processor_nodes_15_mesh_nodes_layer_norm:offset,graphcast.processor.processor.15.node_layer.layer_norm.bias
params:mesh_gnn/~_networks_builder/processor_nodes_15_mesh_nodes_layer_norm:scale,graphcast.processor.processor.15.node_layer.layer_norm.weight
params:mesh_gnn/~_networks_builder/processor_nodes_15_mesh_nodes_mlp/~/linear_0:b,graphcast.processor.processor.15.node_layer.mlp.0.bias
params:mesh_gnn/~_networks_builder/processor_nodes_15_mesh_nodes_mlp/~/linear_0:w,graphcast.processor.processor.15.node_layer.mlp.0.weight
params:mesh_gnn/~_networks_builder/processor_nodes_15_mesh_nodes_mlp/~/linear_1:b,graphcast.processor.processor.15.node_layer.mlp.2.bias
params:mesh_gnn/~_networks_builder/processor_nodes_15_mesh_nodes_mlp/~/linear_1:w,graphcast.processor.processor.15.node_layer.mlp.2.weight