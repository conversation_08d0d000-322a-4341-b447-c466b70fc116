name: 🙋🏼‍♀️🙋🏻‍♂️提问 Ask a Question
description: 提出一个使用/咨询问题。 Ask a usage or consultation question.
labels: [type/question, status/new-issue]

body:
  - type: markdown
    attributes:
      value: >
        #### 你可以在这里提出一个使用/咨询问题，提问之前请确保：

        - 1）已经百度/谷歌搜索过你的问题，但是没有找到解答；

        - 2）已经在官网查询过 [API文档](https://paddlescience-docs.readthedocs.io/zh/latest/zh/api/arch/)，但是没有找到解答；

        - 3）已经在[历史issue](https://github.com/PaddlePaddle/PaddleScience/issues)中搜索过，没有找到同类issue或issue未被解答。


        #### You could ask a usage or consultation question here, before your start, please make sure:

        - 1) You have searched your question on Baidu/Google, but found no answer;

        - 2) You have checked the [API documentation](https://paddlescience-docs.readthedocs.io/zh/latest/zh/api/arch/), but found no answer;

        - 3) You have searched [the existing and past issues](https://github.com/PaddlePaddle/PaddleScience/issues), but found no similar issue or the issue has not been answered.

  - type: textarea
    id: question
    attributes:
      label: 请提出你的问题 Please ask your question
    validations:
      required: true
