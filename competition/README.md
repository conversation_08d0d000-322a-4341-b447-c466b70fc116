# Competition

This part is for all competitions.
Codes of all competitions are stored by submoudule. If you need to obtain it, please initialize the submoudule after cloning PaddleScience.

When you initialize the submoudule, some competitions need your username and password(or token). You can register on the corresponding official website and then get it, usually in "Settings" - "Create Personal Token".

## submodule initialization

``` sh
# for example IJCAI_2024_CAR
cd PaddleScience
git submodule update --init --recursive competition/IJCAI_2024_CAR
```

## competition list

| competition name | submodule name | competition website | official website |
|-------------|-------------|-------------|-------------|
| IJCAI | IJCAI_2024_CAR | [IJCAI_2024_CAR](https://competition.atomgit.com/competitionInfo?id=7f3f276465e9e845fd3a811d2d6925b5) | [atomgit](https://atomgit.com/) |
