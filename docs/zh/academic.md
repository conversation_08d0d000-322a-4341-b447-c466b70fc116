# 学术成果

飞桨团队致力于与国内外高校、科研院所、企业等机构开展深度合作，共同推动飞桨生态在科学计算领域的应用和发展。

1. <PERSON>, <PERSON><PERSON>, & <PERSON>, C<PERSON> (2024). [Latent Neural Operator for Solving Forward and Inverse PDE Problems](https://arxiv.org/abs/2406.03923). Neural Information Processing Systems.
2. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, H., Hu, X., & <PERSON>, J. (2024).[Geometry-Guided Conditional Adaption for Surrogate Models of Large-Scale 3D PDEs on Arbitrary Geometries](https://ijcai24.org/main-track-accepted-papers/). International Joint Conference on Artificial Intelligence.
3. Xu, P<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, X., ... & <PERSON>, X. (2024). [YingLong: Skillful High Resolution Regional Short Term Forecasting with Boundary Smoothing](https://arxiv.org/abs/2401.16254). arXiv preprint arXiv:2401.16254.
4. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2024). [Flow reconstruction over a SUBOFF model based on LBM-generated data and physics-informed neural networks](https://www.sciencedirect.com/science/article/pii/S0029801824015889). Ocean Engineering, 308, 118250.
5. Huang, B., Hua, H., Han, H., He, S., Zhou, Y., Liu, S., & Zuo, Z. (2024). [Physics-informed neural networks for advection–diffusion–Langmuir adsorption processes](https://pubs.aip.org/aip/pof/article/36/8/081906/3308081). Physics of Fluids, 36(8).
6. Lu, Z., Zhou, Y., Zhang, Y., Hu, X., Zhao, Q., & Hu, X. (2024). [A fast general thermal simulation model based on Multi-Branch Physics-Informed deep operator neural network](https://pubs.aip.org/aip/pof/article/36/3/037142/3277890). Physics of Fluids, 36(3).
7. Xu, B., Zhou, Y., & Bian, X. (2024). [Self-supervised learning based on transformer for flow reconstruction and prediction](https://pubs.aip.org/aip/pof/article/36/2/023607/3262455). Physics of Fluids, 36(2).
8. Chen, K., Dai, M., Xu, L., Xu, S., Xie, X., Hu, X., ... & Zhang, H. (2024). [Inverse parameter identifications and forward strip temperature simulations of the continuous annealing line with physics-informed neural network and operation big data](https://www.sciencedirect.com/science/article/pii/S0952197623014914). Engineering Applications of Artificial Intelligence, 127, 107307.
9. Pang, H. Q., Shao, X., Zhang, Z. T., Xie, X., Dai, M. Y., Guo, J. F., ... & Gao, Y. F. (2023). [Physics-informed learning for thermophysical field reconstruction and parameter measurement in a nano-porous insulator's heat transfer problem](https://www.sciencedirect.com/science/article/pii/S0735193323004347). International Communications in Heat and Mass Transfer, 148, 107045.
10. Zhu, Y., Yan, Y., Zhang, Y., Zhou, Y., Zhao, Q., Liu, T., ... & Liang, Y. (2023, June). [Application of Physics-Informed Neural Network (PINN) in the Experimental Study of Vortex-Induced Vibration with Tunable Stiffness](https://onepetro.org/ISOPEIOPEC/proceedings-abstract/ISOPE23/All-ISOPE23/524711). In ISOPE International Ocean and Polar Engineering Conference (pp. ISOPE-I). ISOPE.
11. Chen, K., Huang, F., & Zhang, H. (2023, August). [Fan Rotation Speed Real-Time Optimizations of Continuous Annealing Line with Mechanism-Guided Multitask Classification and Regression Model](https://iopscience.iop.org/article/10.1088/1742-6596/2575/1/012010/meta). In Journal of Physics: Conference Series (Vol. 2575, No. 1, p. 012010). IOP Publishing.
12. Xiang, H., Zhang, Y., Zhou, Y., Liu, T., Xie, X., Li, Y., & Zhao, Q. (2022). [Simulation of Unsteady Incompressible 2D Cylinder Flow with Physics-Informed Neural Network](https://www.dbpia.co.kr/Journal/articleDetail?nodeId=NODE11412247). 한국전산유체공학회 학술대회논문집, 121-121.
13. Chen, K., Xie, X., Chu, Y., Leng, M., Zhang, J., Xu, Z., ... & Zhang, H. (2022, July). [Heat transfer coefficient predictions of the air-cooled condenser with machine learning based on the operation big data of the power plant](https://asmedigitalcollection.asme.org/HT/proceedings-abstract/HT2022/V001T08A002/1146554). In Heat Transfer Summer Conference (Vol. 85796, p. V001T08A002). American Society of Mechanical Engineers.
