# 共创计划

PaddleScience 作为一个开源项目，欢迎来各行各业的伙伴携手共建基于飞桨的 AI for Science 领域顶尖开源项目，打造活跃的前瞻性的 AI for Science 开源社区，建立产学研闭环，推动科研创新与产业赋能。点击了解 [飞桨 AI for Science 共创计划](https://www.paddlepaddle.org.cn/science)

## 项目精选

- 使用嵌套傅立叶神经算子进行实时高分辨二氧化碳地质封存预测: <https://aistudio.baidu.com/projectdetail/7390303>
- 多源异构数据与机理融合的极端天气预报算法研究: <https://aistudio.baidu.com/projectdetail/7586532>
- 基于强化学习的复杂系统控制 —— 以疾病传播: <https://aistudio.baidu.com/projectdetail/7520457>
- 基于 Transformer 架构的流体流动降阶模拟: <https://aistudio.baidu.com/projectdetail/7509905>
- 基于 Transformer 的神经算子预测模型: <https://aistudio.baidu.com/projectdetail/7309026>
- 基于 PINN 方法求解可压缩流体欧拉方程组的正问题: <https://aistudio.baidu.com/projectdetail/7502148>
- 基于连续演化数据预测双曲方程简断解: <https://aistudio.baidu.com/projectdetail/7620492>
- 拉格朗日粒子流体 Benchmark 开源数据集: <https://aistudio.baidu.com/projectdetail/7507477>
- 基于 PINN 方法求解可压缩流体欧拉方程组的正问题: <https://aistudio.baidu.com/projectdetail/7593837>
- 数据驱动 AI 模型的 PDE 方程可解释性评估: <https://aistudio.baidu.com/projectdetail/7463477>
- 数据驱动 AI 模型的 PDE 方程可解释性评估: <https://aistudio.baidu.com/projectdetail/7512749>
