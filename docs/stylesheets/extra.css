:root {
  /* primary */
  --md-primary-fg-color: #6067e7;
  --md-primary-fg-color--light: #5d63db;
  --md-primary-fg-color--dark: #000;
}

.md-grid {
  /* readable page width */
  max-width: 1550px;
}

.md-header__topic > .md-ellipsis {
  overflow: visible;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.avatar {
    height: 64px;
    width: 64px;
    border: 2px solid rgba(128, 128, 128, 0.308);
    border-radius: 50%;
}

.avatar:hover {
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.4);
    transition: 0.4s;
    transform:translateY(-10px);
}
